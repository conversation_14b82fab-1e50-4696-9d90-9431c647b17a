import math
import os

def clear():
    os.system('cls' if os.name == 'nt' else 'clear')

clear()

a = float(input("Input a(side): "))
A = float(input("Input A(angle): "))
b = float(input("Input b(side): "))
B = float(input("Input B(angle): "))

angle_A = math.radians(A)

sine_law = (a / math.sin(angle_A))

print(f"Sine law: {sine_law}")
if b == 0:
    b = sine_law * math.sin(math.radians(B))
    print(f"b: {b}")
elif B == 0:
    B = math.degrees(math.asin(b / sine_law))
    print(f"B: {B}")
else:
    pass

