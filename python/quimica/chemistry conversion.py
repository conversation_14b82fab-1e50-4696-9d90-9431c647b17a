import pint
import os
import re
from mendeleev import element
from functools import lru_cache
import json
from chempy import Substance
from chempy.chemistry import Equilibrium, Species
from chempy.kinetics.ode import get_odesys
from chempy import balance_stoichiometry
from chempy.kinetics.rates import MassAction
from chempy.units import to_unitless, default_units as u
from chempy.chemistry import Reaction

# Constants
CACHE_FILE = "element_molar_masses.json"
STP_TEMPERATURE = 275.00  # Standard temperature in Kelvin (0°C)
STP_PRESSURE = 1.0        # Standard pressure in atm
SATP_TEMPERATURE = 298.00 # Standard ambient temperature in Kelvin (25°C)
SATP_PRESSURE = 1.0       # Standard ambient pressure in atm
R_IDEAL_GAS_CONSTANT = 0.0821  # Ideal gas constant in L·atm/(mol·K)

# Initialize pint unit registry
ureg = pint.UnitRegistry()
R_IDEAL_GAS_CONSTANT = 0.0821 * (ureg.liter * ureg.atm) / (ureg.mole * ureg.kelvin)

# Helper Functions
@lru_cache(maxsize=None)
def get_element_molar_mass(symbol):
    try:
        el = element(symbol)
        if el.atomic_weight:
            return el.atomic_weight
        else:
            raise ValueError(f"Atomic weight for element '{symbol}' is not defined.")
    except Exception as e:
        raise ValueError(f"Error: Could not load atomic weight for element '{symbol}'. {e}")

def calculate_molar_mass(formula):
    try:
        substance = Substance.from_formula(formula)
        return substance.mass  # Retorna a massa molar diretamente
    except Exception as e:
        raise ValueError(f"Error calculating molar mass for {formula}: {e}")

def get_number_input(prompt):
    input_str = input(prompt).strip()
    try:
        if "x" in input_str.lower() or "×" in input_str.lower() or "^" in input_str:
            return parse_scientific_notation(input_str)
        return float(input_str)
    except ValueError:
        raise ValueError("Invalid input. Please enter a valid number.")

def parse_scientific_notation(input_str):
    pattern = r"([\d\.]+)\s*[x×]\s*10\^([\+\-]?\d+)"
    match = re.match(pattern, input_str, re.IGNORECASE)
    if not match:
        raise ValueError("Invalid format. Use 'X × 10^n' or 'X x 10^n'.")
    base = float(match.group(1))
    exponent = int(match.group(2))
    return base * (10 ** exponent)

def atoms_to_moles(atoms):
    AVOGADROS_NUMBER = 6.022e23
    return atoms / AVOGADROS_NUMBER

def grams_to_moles(mass, molar_mass):
    mass = mass * ureg.grams
    molar_mass = molar_mass * ureg.grams / ureg.mole
    return (mass / molar_mass).to(ureg.mole)

def moles_to_grams(moles, molar_mass):
    moles = moles * ureg.mole
    molar_mass = molar_mass * ureg.grams / ureg.mole
    return (moles * molar_mass).to(ureg.grams)

def grams_to_liters(mass, molar_mass, pressure, temperature):
    moles = grams_to_moles(mass, molar_mass)
    pressure = pressure * ureg.atm
    temperature = temperature * ureg.kelvin
    volume = (moles * R_IDEAL_GAS_CONSTANT * temperature / pressure).to(ureg.liters)
    return volume

def liters_to_grams(volume, molar_mass, pressure, temperature):
    volume = volume * ureg.liters
    pressure = pressure * ureg.atm
    temperature = temperature * ureg.kelvin
    moles = (pressure * volume / (R_IDEAL_GAS_CONSTANT * temperature)).to(ureg.mole)
    return moles_to_grams(moles.magnitude, molar_mass)

def moles_to_atoms(moles):
    AVOGADROS_NUMBER = 6.022e23  # Número de Avogadro em átomos/mol
    return moles * AVOGADROS_NUMBER

def liters_to_moles(volume, pressure, temperature):
    volume = volume * ureg.liters
    pressure = pressure * ureg.atm
    temperature = temperature * ureg.kelvin
    return (pressure * volume / (R_IDEAL_GAS_CONSTANT * temperature)).to(ureg.mole)

def clear_terminal():
    os.system('cls' if os.name == 'nt' else 'clear')

def display_parameters(**kwargs):
    print("\nParameters:")
    for key, value in kwargs.items():
        print(f"{key}: {value}")
    print()

def format_scientific(value, precision=2, threshold=1e5):
    """Formata um número em notação científica apenas se for muito grande ou muito pequeno."""
    if abs(value) >= threshold or (value != 0 and abs(value) < 1 / threshold):
        formatted = f"{value:.{precision}e}"
        base, exponent = formatted.split("e")
        return f"{base} × 10^{int(exponent)}"
    return f"{value:.{precision}f}"

# Main Program
def main():
    while True:
        clear_terminal()
        print("Welcome to the Chemistry Calculator!")
        print("\nChoose a category:")
        print("1. Conversions")
        print("2. Laws")
        print("3. Exit")

        category = input("\nEnter your choice: ")

        if category == "1":
            while True:
                clear_terminal()
                print("\nEnter the substance (or its chemical formula):")
                print("[Type '0' to go back to the main menu]")
                substance = input("Substance: ").strip()
                if substance == "0":
                    break
                try:
                    molar_mass = calculate_molar_mass(substance)
                    print(f"Molar mass of {substance}: {format_scientific(molar_mass, 2)} g/mol.")
                except ValueError as e:
                    print(f"Error: {e}")
                    input("\nPress Enter to try again...")
                    continue

                while True:
                    clear_terminal()
                    display_parameters(Substance=substance, Molar_Mass=f"{format_scientific(molar_mass, 2)} g/mol")
                    print("\n--- Conversions ---")
                    print("1. Grams to moles")
                    print("2. Moles to grams")
                    print("3. Grams to liters")
                    print("4. Liters to grams")
                    print("5. Atoms to moles")
                    print("6. Moles to atoms")
                    print("7. Liters to moles")
                    print("8. Change substance")
                    print("9. Back to main menu")

                    operation = input("\nEnter your choice: ")

                    if operation == "1":
                        clear_terminal()
                        grams = get_number_input("Enter the mass in grams: ")
                        moles = grams_to_moles(grams, molar_mass)
                        print(f"{format_scientific(grams, 2)} g of {substance} is {format_scientific(moles.magnitude, 2)} moles.")
                        input("\nPress Enter to continue...")
                    elif operation == "2":
                        clear_terminal()
                        moles = get_number_input("Enter the amount in moles: ")
                        grams = moles_to_grams(moles, molar_mass)
                        print(f"{format_scientific(moles, 2)} moles of {substance} is {format_scientific(grams.magnitude, 2)} g.")
                        input("\nPress Enter to continue...")
                    elif operation == "3":
                        clear_terminal()
                        grams = get_number_input("Enter the mass in grams: ")
                        condition = input("Use (1) STP, (2) SATP, or (3) custom conditions? ")
                        if condition == "1":
                            pressure, temperature = STP_PRESSURE, STP_TEMPERATURE
                        elif condition == "2":
                            pressure, temperature = SATP_PRESSURE, SATP_TEMPERATURE
                        else:
                            pressure = get_number_input("Enter the pressure in atm: ")
                            temperature = get_number_input("Enter the temperature in Kelvin: ")
                        volume = grams_to_liters(grams, molar_mass, pressure, temperature)
                        print(f"{format_scientific(grams, 2)} g of {substance} occupies {format_scientific(volume.magnitude, 2)} liters.")
                        input("\nPress Enter to continue...")
                    elif operation == "4":
                        clear_terminal()
                        volume = get_number_input("Enter the volume in liters: ")
                        condition = input("Use (1) STP, (2) SATP, or (3) custom conditions? ")
                        if condition == "1":
                            pressure, temperature = STP_PRESSURE, STP_TEMPERATURE
                        elif condition == "2":
                            pressure, temperature = SATP_PRESSURE, SATP_TEMPERATURE
                        else:
                            pressure = get_number_input("Enter the pressure in atm: ")
                            temperature = get_number_input("Enter the temperature in Kelvin: ")
                        mass = liters_to_grams(volume, molar_mass, pressure, temperature)
                        print(f"{format_scientific(volume, 2)} L of {substance} weighs {format_scientific(mass.magnitude, 2)} g.")
                        input("\nPress Enter to continue...")
                    elif operation == "5":
                        clear_terminal()
                        atoms = get_number_input("Enter the number of atoms: ")
                        moles = atoms_to_moles(atoms)
                        print(f"{format_scientific(atoms, 2)} atoms is equal to {format_scientific(moles, 6)} moles.")
                        input("\nPress Enter to continue...")
                    elif operation == "6":
                        clear_terminal()
                        moles = get_number_input("Enter the amount in moles: ")
                        atoms = moles_to_atoms(moles)
                        print(f"{format_scientific(moles, 2)} moles is equal to {format_scientific(atoms, 2)} atoms.")
                        input("\nPress Enter to continue...")
                    elif operation == "7":
                        clear_terminal()
                        volume = get_number_input("Enter the volume in liters: ")
                        condition = input("Use (1) STP, (2) SATP, or (3) custom conditions? ")
                        if condition == "1":
                            pressure, temperature = STP_PRESSURE, STP_TEMPERATURE
                        elif condition == "2":
                            pressure, temperature = SATP_PRESSURE, SATP_TEMPERATURE
                        else:
                            pressure = get_number_input("Enter the pressure in atm: ")
                            temperature = get_number_input("Enter the temperature in Kelvin: ")
                        moles = liters_to_moles(volume, pressure, temperature)
                        print(f"{format_scientific(volume, 2)} L of gas is equal to {format_scientific(moles.magnitude, 2)} moles.")
                        input("\nPress Enter to continue...")
                    elif operation == "8":
                        break
                    elif operation == "9":
                        return
                    else:
                        print("Invalid choice. Please try again.")
                        input("\nPress Enter to continue...")

        elif category == "2":
            while True:
                clear_terminal()
                print("\n--- Laws ---")
                print("1. Boyle's Law")
                print("2. Charles's Law")
                print("3. Avogadro's Law")
                print("4. Ideal Gas Law (Pressure)")
                print("5. Ideal Gas Law (Volume)")
                print("6. Ideal Gas Law (Temperature)")
                print("7. Ideal Gas Law (Moles)")
                print("8. Back to main menu")

                operation = input("\nEnter your choice: ")

                if operation == "8":
                    break
                else:
                    print("Feature not implemented yet.")
                    input("\nPress Enter to continue...")

        elif category == "3":
            clear_terminal()
            print("Goodbye!")
            break
        else:
            print("Invalid category. Please try again.")
            input("\nPress Enter to continue...")

    # Definir espécies químicas
    H2 = Species.from_formula('H2')
    O2 = Species.from_formula('O2')
    H2O = Species.from_formula('H2O')

    # Definir equilíbrio químico
    eq = Equilibrium({'H2': 2, 'O2': 1}, {'H2O': 2}, 10**-3)  # K_eq = 10^-3

    # Resolver o equilíbrio
    initial_concentrations = {'H2': 1.0, 'O2': 0.5, 'H2O': 0.0}  # mol/L
    odesys, extra = get_odesys([eq])
    result = odesys.integrate(10, initial_concentrations)

    print(result.y)  # Concentrações no equilíbrio

    # Balancear uma reação química
    reactants, products = balance_stoichiometry({'H2', 'O2'}, {'H2O'})
    print("Reactants:", reactants)
    print("Products:", products)

    # Calcular energia livre de Gibbs
    gibbs = GibbsEnergy({'H2': 0, 'O2': 0, 'H2O': -237.13})  # Valores em kJ/mol
    delta_g = gibbs({'H2': 2, 'O2': 1}, {'H2O': 2})
    print(f"ΔG = {delta_g:.2f} kJ/mol")

    # Definir uma reação química
    rate = MassAction([2, 1], [1], k_forward=1.0, k_backward=0.1)

    # Resolver a cinética
    odesys, extra = get_odesys([rate])
    initial_concentrations = {'H2': 1.0, 'O2': 0.5, 'H2O': 0.0}
    result = odesys.integrate(10, initial_concentrations)

    print(result.y)  # Concentrações ao longo do tempo

    # Converter pressão de atm para Pa
    pressure_atm = 1.0 * u.atm
    pressure_pa = to_unitless(pressure_atm, u.Pa)
    print(f"Pressure: {pressure_pa} Pa")

    # Definir uma reação química
    reaction = Reaction({'H2': 2, 'O2': 1}, {'H2O': 2}, param=1.0)

    # Simular a reação
    print(reaction)
    print("Rate:", reaction.rate({'H2': 1.0, 'O2': 0.5}))

if __name__ == "__main__":
    main()
