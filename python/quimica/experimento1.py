from pint import UnitRegistry

ureg = UnitRegistry()
Q_ = ureg.Quantity

# Exemplo de uso no cálculo da Lei dos Gases Ideais
def ideal_gas_law_pressure(n, r, t, v):
    try:
        n = Q_(n, "mole")
        r = Q_(r, "joule/(mole*kelvin)")  # Corrigido para unidade SI
        t = Q_(t, "kelvin")
        v = Q_(v, "liter").to("meter**3")  # Convertendo litros para metros cúbicos
        pressure = (n * r * t) / v
        return pressure.to("pascal").magnitude  # Retorna a pressão em Pascal
    except Exception as e:
        return f"Erro no cálculo: {e}"

# Teste para verificar funcionalidade
if __name__ == "__main__":
    # Valores de teste
    n = 1  # mol
    r = 8.314  # J/(mol*K)
    t = 300  # K
    v = 22.4  # L (volume molar aproximado a 0°C e 1 atm)
    
    resultado = ideal_gas_law_pressure(n, r, t, v)
    print(f"Pressão calculada: {resultado} Pa")