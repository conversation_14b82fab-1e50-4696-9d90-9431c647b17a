import pygame
import pygame_gui
from chempy import Substance

# Funções para cálculos químicos
def calculate_molar_mass(substance_formula):
    try:
        substance = Substance.from_formula(substance_formula)
        return substance.mass
    except Exception:
        return None  # Retorna None em caso de erro

def grams_to_moles(mass, molar_mass):
    try:
        return mass / molar_mass
    except ZeroDivisionError:
        return None  # Retorna None se a massa molar for zero

def moles_to_grams(moles, molar_mass):
    return moles * molar_mass

# Inicializar o pygame
pygame.init()

# Configurações da janela
WINDOW_WIDTH = 800
WINDOW_HEIGHT = 600
screen = pygame.display.set_mode((WINDOW_WIDTH, WINDOW_HEIGHT))
pygame.display.set_caption("Chemistry Conversion")

# Gerenciador de interface do pygame_gui
manager = pygame_gui.UIManager((WINDOW_WIDTH, WINDOW_HEIGHT))

# Configurações de cores
def configure_colors():
    global BACKGROUND_COLOR, TEXT_COLOR, INPUT_BOX_COLOR, INPUT_TEXT_COLOR, BUTTON_BG_COLOR, BUTTON_TEXT_COLOR
    BACKGROUND_COLOR = (0, 0, 0)  # Preto
    TEXT_COLOR = (0, 255, 243)  # Azul claro
    INPUT_BOX_COLOR = (0, 255, 243)  # Azul claro
    INPUT_TEXT_COLOR = (0, 0, 0)  # Preto
    BUTTON_BG_COLOR = (0, 255, 243)  # Azul claro
    BUTTON_TEXT_COLOR = (0, 0, 0)  # Preto

# Chamar a função para configurar as cores
configure_colors()

# Atualizar o tema do pygame_gui para aplicar as cores
manager.get_theme().load_theme({
    "colours": {
        "#input_formula_box": {
            "normal_bg": INPUT_BOX_COLOR,
            "normal_text": INPUT_TEXT_COLOR,
            "focused_bg": INPUT_BOX_COLOR,
            "focused_text": INPUT_TEXT_COLOR
        },
        "#input_mass_box": {
            "normal_bg": INPUT_BOX_COLOR,
            "normal_text": INPUT_TEXT_COLOR,
            "focused_bg": INPUT_BOX_COLOR,
            "focused_text": INPUT_TEXT_COLOR
        },
        "#input_moles_box": {
            "normal_bg": INPUT_BOX_COLOR,
            "normal_text": INPUT_TEXT_COLOR,
            "focused_bg": INPUT_BOX_COLOR,
            "focused_text": INPUT_TEXT_COLOR
        },
        "#calculate_mass_button": {
            "normal_bg": BUTTON_BG_COLOR,
            "normal_text": BUTTON_TEXT_COLOR,
            "hovered_bg": BUTTON_BG_COLOR,
            "hovered_text": BUTTON_TEXT_COLOR
        },
        "#grams_to_moles_button": {
            "normal_bg": BUTTON_BG_COLOR,
            "normal_text": BUTTON_TEXT_COLOR,
            "hovered_bg": BUTTON_BG_COLOR,
            "hovered_text": BUTTON_TEXT_COLOR
        },
        "#moles_to_grams_button": {
            "normal_bg": BUTTON_BG_COLOR,
            "normal_text": BUTTON_TEXT_COLOR,
            "hovered_bg": BUTTON_BG_COLOR,
            "hovered_text": BUTTON_TEXT_COLOR
        },
        "#output_label": {
            "normal_text": TEXT_COLOR
        }
    }
})

# Elementos da interface
input_formula_box = pygame_gui.elements.UITextEntryLine(
    relative_rect=pygame.Rect((50, 50), (300, 50)),
    manager=manager,
    object_id="#input_formula_box"
)
input_formula_box.set_text("")

calculate_mass_button = pygame_gui.elements.UIButton(
    relative_rect=pygame.Rect((370, 50), (100, 50)),
    text="Massa Molar",
    manager=manager,
    object_id="#calculate_mass_button"
)

input_mass_box = pygame_gui.elements.UITextEntryLine(
    relative_rect=pygame.Rect((50, 120), (300, 50)),
    manager=manager,
    object_id="#input_mass_box"
)
input_mass_box.set_text("")

grams_to_moles_button = pygame_gui.elements.UIButton(
    relative_rect=pygame.Rect((370, 120), (150, 50)),
    text="Gramas -> Moles",
    manager=manager,
    object_id="#grams_to_moles_button"
)

input_moles_box = pygame_gui.elements.UITextEntryLine(
    relative_rect=pygame.Rect((50, 190), (300, 50)),
    manager=manager,
    object_id="#input_moles_box"
)
input_moles_box.set_text("")

moles_to_grams_button = pygame_gui.elements.UIButton(
    relative_rect=pygame.Rect((370, 190), (150, 50)),
    text="Moles -> Gramas",
    manager=manager,
    object_id="#moles_to_grams_button"
)

output_label = pygame_gui.elements.UILabel(
    relative_rect=pygame.Rect((50, 260), (700, 50)),
    text="Resultado aparecerá aqui.",
    manager=manager,
    object_id="#output_label"
)

# Loop principal
clock = pygame.time.Clock()
running = True
molar_mass = None

while running:
    time_delta = clock.tick(60) / 1000.0  # Controlar FPS

    for event in pygame.event.get():
        if event.type == pygame.QUIT:
            running = False

        # Processar eventos do pygame_gui
        manager.process_events(event)

        # Verificar se os botões foram clicados
        if event.type == pygame_gui.UI_BUTTON_PRESSED:
            if event.ui_element == calculate_mass_button:
                formula = input_formula_box.get_text()
                molar_mass = calculate_molar_mass(formula)
                if molar_mass is None:  # Caso de erro
                    output_label.set_text("Erro ao calcular a massa molar.")
                else:
                    output_label.set_text(f"Massa molar de {formula}: {molar_mass:.2f} g/mol")

            elif event.ui_element == grams_to_moles_button:
                if molar_mass is None:
                    output_label.set_text("Erro: Calcule a massa molar primeiro.")
                else:
                    try:
                        mass = float(input_mass_box.get_text())
                        moles = grams_to_moles(mass, molar_mass)
                        if moles is None:
                            output_label.set_text("Erro: Massa molar inválida.")
                        else:
                            output_label.set_text(f"{mass} g é equivalente a {moles:.4f} moles.")
                    except ValueError:
                        output_label.set_text("Erro: Insira um valor válido para a massa.")

            elif event.ui_element == moles_to_grams_button:
                if molar_mass is None:
                    output_label.set_text("Erro: Calcule a massa molar primeiro.")
                else:
                    try:
                        moles = float(input_moles_box.get_text())
                        mass = moles_to_grams(moles, molar_mass)
                        output_label.set_text(f"{moles} moles é equivalente a {mass:.4f} g.")
                    except ValueError:
                        output_label.set_text("Erro: Insira um valor válido para os moles.")

    # Atualizar a interface
    manager.update(time_delta)

    # Renderizar a interface
    screen.fill(BACKGROUND_COLOR)  # Usar a cor de fundo selecionada
    manager.draw_ui(screen)

    pygame.display.update()

pygame.quit()