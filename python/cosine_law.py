import math
import os

def clear():
    os.system('cls' if os.name == 'nt' else 'clear')

clear()

a = float(input("Input a(side): "))
b = float(input("Input b(side): "))
c = float(input("Input c(side): "))
C = float(input("Input C(angle): "))

if C == 0:
    cosine_law = (a**2 + b**2 - c**2) / (2*a*b)
    print(f"="*40)
    print(f"cosine law: {cosine_law}")
    print(f"C in degrees: {math.degrees(math.acos(cosine_law))}")
    print(f"="*40)
elif c == 0:
    angle_C = math.radians(C)
    cosine_law = (a**2 + b**2 - 2*a*b*math.cos(angle_C))
    print(f"="*40)
    print(f"cosine law: {cosine_law}")
    print(f"c: {math.sqrt(cosine_law)}")
    print(f"="*40)
