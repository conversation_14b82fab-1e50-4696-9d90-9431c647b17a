import math
import os 

def clear():
    os.system('cls' if os.name == 'nt' else 'clear')

def newton_coeficiente(n, k):
    if k < 0:
        return 0 
    result = 1.0
    for i in range(k):
        result *= (n - i) / (i + 1)
    return result

def newton_triangle(n, lines=5):
    triangle = []
    for k in range(lines):
        line = [newton_coeficiente(n, k) for k in range(k + 1)]
        triangle.append(line)
    return triangle

clear()
n = float(input("Input: "))
lines = 10
triangle = newton_triangle(n, lines)

print(f"Newton triangle for n = {n}: ")
for line in triangle:
    print(line)