from cryptography.fernet import Fernet
import os 

def clear():
    os.system('cls' if os.name == 'nt' else 'clear')

dir_script = os.path.dirname(__file__)

path_key = os.path.join(dir_script, "secret_key.key")
with open(path_key, "rb") as file_key:
    key = file_key.read()


path_text = os.path.join(dir_script, "encrypted_text.txt")
with open(path_text, "rb") as text_file:
    encrypted_text = text_file.read()


cipher_suite = Fernet(key)
uncrypted_text = cipher_suite.decrypt(encrypted_text).decode()
clear()
print("uncrypted text:", uncrypted_text)