from cryptography.fernet import Fernet
import os 


key = Fernet.generate_key()
cipher_suite = Fernet(key)

def clear():
    os.system('cls' if os.name == 'nt' else 'clear')



clear()
orignal_message = input("texto to be encrypted: ")

encrypted_text = cipher_suite.encrypt(orignal_message.encode())
print(f"Encrypted text: {encrypted_text}")

decrypted_text = cipher_suite.decrypt(encrypted_text).decode()
print(f"Decrypted text: {decrypted_text}")