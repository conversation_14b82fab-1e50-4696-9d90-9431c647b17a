from cryptography.fernet import Fernet
import os 

def clear():
    os.system('cls' if os.name == 'nt' else 'clear')


dir_script = os.path.dirname(__file__)

key = Fernet.generate_key()
path_key = os.path.join(dir_script, "secret_key.key")
with open(path_key, "wb") as file_key:
    file_key.write(key)


clear()
cipher_suite = Fernet(key)
original_text = input("Input your text: ")
encrypted_text = cipher_suite.encrypt(original_text.encode())

path_text = os.path.join(dir_script, "encrypted_text.txt")
with open(path_text, "wb") as text_file:
    text_file.write(encrypted_text)


print("Sucess to save the key and encrypted the text")