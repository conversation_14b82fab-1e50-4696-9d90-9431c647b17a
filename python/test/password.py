import os
import random
import time
import string
import math
from collections import Counter

# CONFIGURATION - Change this value to adjust simultaneous attempts
SIMULTANEOUS_ATTEMPTS = 5  # Can be any positive integer

def clear():
    os.system('cls' if os.name == 'nt' else 'clear')

def determine_char_type(char):
    """Identifies character type"""
    if char == ' ':
        return "space"
    elif char == 'ˆ':
        return "caret"
    elif char.isdigit():
        return "digit"
    elif char.islower():
        return "lowercase"
    elif char.isupper():
        return "uppercase"
    else:
        return "special"

def generate_random_char(char_type):
    """Generates random character of specified type"""
    char_sets = {
        "space": [' '],
        "digit": list(string.digits),
        "lowercase": list(string.ascii_lowercase),
        "uppercase": list(string.ascii_uppercase),
        "caret": ['ˆ'],
        "special": list('!@#$%^&*()-_=+[{]}\\|;:\'",<.>/?`~')
    }
    return random.choice(char_sets[char_type])

def generate_guess(char_type, attempted_guesses):
    """Generates non-repeating guesses"""
    char_sets = {
        "space": [' '],
        "digit": list(string.digits),
        "lowercase": list(string.ascii_lowercase),
        "uppercase": list(string.ascii_uppercase),
        "caret": ['ˆ'],
        "special": list('!@#$%^&*()-_=+[{]}\\|;:\'",<.>/?`~')
    }
    
    available = [c for c in char_sets[char_type] if c not in attempted_guesses]
    return random.choice(available) if available else None

def estimate_password_strength(password):
    """Estimates password strength (0-100%)"""
    length = len(password)
    if length == 0:
        return 0
    
    # Character type diversity
    types = set()
    for c in password:
        types.add(determine_char_type(c))
    type_score = min(len(types) * 20, 40)  # Max 40 points
    
    # Length score (max 40 points)
    length_score = min(length * 2, 40)
    
    # Entropy score (max 20 points)
    char_counts = Counter(password)
    entropy = 0
    for count in char_counts.values():
        p = count / length
        if p > 0:  # Only calculate if probability is positive
            entropy -= p * math.log2(p)
    entropy_score = min(entropy * 2, 20)
    
    total_score = type_score + length_score + entropy_score
    return min(int(total_score), 100)  # Cap at 100%

def crack_password(password):
    password_chars = list(password)
    guessed = [generate_random_char(determine_char_type(c)) if c != ' ' else ' ' for c in password_chars]
    attempts = 0
    remaining_positions = list(range(len(password_chars)))
    attempted_guesses = [set() for _ in password_chars]
    strength = estimate_password_strength(password)

    clear()
    print("┌" + "─" * 50 + "┐")
    print(f"│ ACTUAL PASSWORD: \033[1;32m{password}\033[0m")
    print(f"│ CURRENT GUESS:   \033[1;33m{''.join(guessed)}\033[0m")
    print(f"│ ATTEMPTS:        {attempts}")
    print(f"│ SIMULTANEOUS:    {SIMULTANEOUS_ATTEMPTS} chars/attempt")
    print(f"│ STRENGTH:        {strength}% secure")
    print("└" + "─" * 50 + "┘")

    while remaining_positions:
        # Select batch of positions to try
        batch = remaining_positions[:SIMULTANEOUS_ATTEMPTS]
        
        # Generate guesses for each position
        guesses = []
        for pos in batch:
            char = password_chars[pos]
            char_type = determine_char_type(char)
            guess = generate_guess(char_type, attempted_guesses[pos]) or char
            attempted_guesses[pos].add(guess)
            guesses.append(guess)
        
        attempts += 1

        # Update characters
        for i, pos in enumerate(batch):
            if guesses[i] == password_chars[pos]:
                guessed[pos] = password_chars[pos]
                remaining_positions.remove(pos)
            else:
                guessed[pos] = generate_random_char(determine_char_type(password_chars[pos]))

        # Update non-batch characters
        for pos in remaining_positions:
            if pos not in batch:
                guessed[pos] = generate_random_char(determine_char_type(password_chars[pos]))

        # Update display
        print(f"\033[5A", end="")  # Move cursor up 5 lines
        print(f"│ CURRENT GUESS:   \033[1;33m{''.join(guessed)}\033[0m")
        print(f"│ ATTEMPTS:        {attempts}")
        print(f"│ SIMULTANEOUS:    {SIMULTANEOUS_ATTEMPTS} chars/attempt")
        print(f"│ STRENGTH:        {strength}% secure")
        print(f"│ TRYING:          Positions {[x+1 for x in batch]} with {guesses}")
        time.sleep(max(0.01, 0.05 - (SIMULTANEOUS_ATTEMPTS * 0.005)))

    # Final result
    clear()
    print("┌" + "─" * 50 + "┐")
    print(f"│ \033[1;32mPASSWORD CRACKED!\033[0m")
    print(f"│ PASSWORD:     \033[1;36m{password}\033[0m")
    print(f"│ ATTEMPTS:     {attempts}")
    print(f"│ STRENGTH:     {strength}% secure")
    print(f"│ EFFICIENCY:   {len(password)/attempts:.2f} chars/attempt")
    print("└" + "─" * 50 + "┘")

if __name__ == "__main__":
    clear()
    password = input("Enter password to crack: ")
    crack_password(password)