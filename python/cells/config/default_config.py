"""
Configurações padrão para a simulação de células.
"""

# Configurações do ambiente
ENVIRONMENT_CONFIG = {
    "width": 800,
    "height": 600,
    "initial_cells": 5,
    "nutrient_level": 1.0,
    "simulation_speed": 1.0
}

# Configurações de visualização
VISUALIZATION_CONFIG = {
    "window_width": 1200,
    "window_height": 800,
    "background_color": (240, 240, 255),
    "grid_color": (200, 200, 220, 100),
    "grid_size": 50,
    "show_grid": True,
    "show_nutrient_distribution": True,
    "fps_limit": 60
}

# Configurações de células
CELL_CONFIG = {
    "default_radius": 10.0,
    "min_radius": 5.0,
    "max_radius": 30.0,
    "default_energy": 100.0,
    "energy_consumption_rate": 0.1,
    "division_energy_threshold": 150.0,
    "division_size_threshold": 20.0,
    "mutation_rate": 0.05,
    "default_flexibility": 0.5
}

# Configurações de física
PHYSICS_CONFIG = {
    "friction": 0.02,
    "collision_elasticity": 0.8,
    "random_force_magnitude": 0.01,
    "apply_random_forces": True
}

# Configurações de genes
GENE_CONFIG = {
    "size_gene": {
        "radius": {
            "default": 10.0,
            "min": 5.0,
            "max": 30.0,
            "mutation_rate": 0.1
        },
        "growth_rate": {
            "default": 0.1,  # Aumentado para crescimento mais rápido
            "min": 0.05,
            "max": 0.3,
            "mutation_rate": 0.05
        }
    },
    "metabolism_gene": {
        "efficiency": {
            "default": 0.8,  # Aumentado para melhor eficiência
            "min": 0.4,
            "max": 0.95,
            "mutation_rate": 0.05
        },
        "consumption_rate": {
            "default": 0.15,  # Aumentado para consumo mais rápido
            "min": 0.05,
            "max": 0.4,
            "mutation_rate": 0.05
        }
    },
    "reproduction_gene": {
        "division_threshold": {
            "default": 15.0,  # Reduzido para facilitar a divisão
            "min": 12.0,
            "max": 30.0,
            "mutation_rate": 0.05
        },
        "division_energy_cost": {
            "default": 0.4,  # Reduzido para facilitar a divisão
            "min": 0.2,
            "max": 0.6,
            "mutation_rate": 0.05
        }
    },
    "membrane_gene": {
        "flexibility": {
            "default": 0.5,
            "min": 0.1,
            "max": 0.9,
            "mutation_rate": 0.05
        },
        "strength": {
            "default": 0.6,
            "min": 0.2,
            "max": 0.9,
            "mutation_rate": 0.05
        }
    }
}

# Configurações de estatísticas
STATISTICS_CONFIG = {
    "record_interval": 1.0,  # segundos
    "max_history_points": 1000
}

# Configurações para futura integração com redes neurais
NEURAL_NETWORK_CONFIG = {
    "input_size": 10,  # Número de entradas para a rede neural
    "hidden_layers": [20, 10],  # Tamanho das camadas ocultas
    "output_size": 5,  # Número de saídas da rede neural
    "activation": "relu",  # Função de ativação
    "learning_rate": 0.001  # Taxa de aprendizado
}

# Configurações de salvamento/carregamento
SAVE_CONFIG = {
    "auto_save": False,
    "auto_save_interval": 300,  # segundos
    "save_directory": "data/saves"
}

# Configurações de interface do usuário
UI_CONFIG = {
    "cell_info_width": 250,
    "control_panel_width": 300,
    "slider_min_speed": 0.1,
    "slider_max_speed": 5.0,
    "slider_min_nutrients": 0.0,
    "slider_max_nutrients": 2.0,
    "theme": "light"  # "light" ou "dark"
}
