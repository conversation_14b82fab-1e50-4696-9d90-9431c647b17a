# Simulação de Células

Este projeto implementa uma simulação interativa de células com propriedades físicas e genéticas.

## Características

- Simulação de células com propriedades físicas realistas
- Células com forma flexível que se adaptam ao espaço disponível
- Visualização interativa dos genes das células (hover/clique)
- Controles para ajustar velocidade da simulação e quantidade de nutrientes
- Preparado para futura implementação de redes neurais

## Estrutura do Projeto

```
cells/
├── assets/            # Recursos gráficos
├── config/            # Arquivos de configuração
├── data/              # Dados de simulação
├── docs/              # Documentação
├── src/               # Código fonte
│   ├── models/        # Modelos de células e genes
│   ├── simulation/    # Lógica de simulação
│   ├── utils/         # Funções utilitárias
│   └── visualization/ # Interface gráfica
├── tests/             # Testes unitários
└── main.py            # Ponto de entrada principal
```

## Requisitos

- Python 3.8+
- PyQt6
- Mesa
- NumPy
- Matplotlib

## Como Executar

```bash
cd python/cells
python main.py
```

## Controles

- **Barra de Velocidade**: Ajusta a velocidade da simulação
- **Barra de Nutrientes**: Controla a quantidade de nutrientes disponíveis
- **Hover/Clique nas Células**: Mostra informações genéticas da célula

## Futuras Implementações

- Integração com redes neurais para evolução das células
- Mais parâmetros ambientais
- Exportação e importação de configurações genéticas
