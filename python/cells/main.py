#!/usr/bin/env python3
"""
Simulação de Células - Ponto de entrada principal

Este programa implementa uma simulação interativa de células com propriedades
físicas e genéticas. As células podem crescer, se dividir, mudar de forma
e interagir umas com as outras.

Características:
- Células com forma flexível que se adaptam ao espaço
- Visualização interativa dos genes das células (hover/clique)
- Controles para ajustar velocidade da simulação e quantidade de nutrientes
- Preparado para futura implementação de redes neurais
"""
import argparse
import sys
from typing import Dict, List, Optional, Tuple

from PyQt6.QtWidgets import QApplication

from config.default_config import ENVIRONMENT_CONFIG, VISUALIZATION_CONFIG
from src.simulation.environment import Environment
from src.visualization.ui import SimulationWindow, run_simulation


def parse_arguments():
    """Analisa os argumentos da linha de comando."""
    parser = argparse.ArgumentParser(description='Simulação de Células')
    
    parser.add_argument('--width', type=int, default=ENVIRONMENT_CONFIG["width"],
                        help='Largura da área de simulação')
    parser.add_argument('--height', type=int, default=ENVIRONMENT_CONFIG["height"],
                        help='Altura da área de simulação')
    parser.add_argument('--cells', type=int, default=ENVIRONMENT_CONFIG["initial_cells"],
                        help='Número inicial de células')
    parser.add_argument('--nutrients', type=float, default=ENVIRONMENT_CONFIG["nutrient_level"],
                        help='Nível inicial de nutrientes (0.0 a 2.0)')
    parser.add_argument('--speed', type=float, default=ENVIRONMENT_CONFIG["simulation_speed"],
                        help='Velocidade inicial da simulação (0.1 a 5.0)')
    parser.add_argument('--window-width', type=int, default=VISUALIZATION_CONFIG["window_width"],
                        help='Largura da janela')
    parser.add_argument('--window-height', type=int, default=VISUALIZATION_CONFIG["window_height"],
                        help='Altura da janela')
    parser.add_argument('--load', type=str, default=None,
                        help='Carregar simulação de um arquivo')
    
    return parser.parse_args()


def main():
    """Função principal."""
    # Analisar argumentos
    args = parse_arguments()
    
    # Iniciar simulação
    run_simulation(width=args.width, height=args.height)


if __name__ == "__main__":
    main()
