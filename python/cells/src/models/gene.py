"""
Módulo para definição e manipulação de genes das células.
"""
import random
import uuid
from dataclasses import dataclass
from typing import Dict, List, Optional, Union


@dataclass
class GeneProperty:
    """Representa uma propriedade controlada por um gene."""
    name: str
    value: float
    min_value: float
    max_value: float
    mutation_rate: float = 0.05
    description: str = ""

    def mutate(self) -> None:
        """Aplica uma mutação aleatória ao valor do gene."""
        if random.random() < self.mutation_rate:
            # Mutação com distribuição normal centrada no valor atual
            mutation = random.gauss(0, (self.max_value - self.min_value) * 0.1)
            new_value = self.value + mutation
            # Garantir que o valor está dentro dos limites
            self.value = max(self.min_value, min(self.max_value, new_value))


class Gene:
    """
    Representa um gene que controla uma ou mais propriedades da célula.
    """
    def __init__(self, name: str, properties: Dict[str, GeneProperty],
                 active: bool = True, description: str = ""):
        self.id = str(uuid.uuid4())
        self.name = name
        self.properties = properties
        self.active = active
        self.description = description

    def mutate(self) -> None:
        """Aplica mutações a todas as propriedades do gene."""
        for prop in self.properties.values():
            prop.mutate()

    def copy(self) -> 'Gene':
        """Cria uma cópia do gene."""
        new_properties = {
            name: GeneProperty(
                name=prop.name,
                value=prop.value,
                min_value=prop.min_value,
                max_value=prop.max_value,
                mutation_rate=prop.mutation_rate,
                description=prop.description
            )
            for name, prop in self.properties.items()
        }
        return Gene(
            name=self.name,
            properties=new_properties,
            active=self.active,
            description=self.description
        )

    def to_dict(self) -> Dict:
        """Converte o gene para um dicionário."""
        return {
            "id": self.id,
            "name": self.name,
            "active": self.active,
            "description": self.description,
            "properties": {
                name: {
                    "value": prop.value,
                    "min_value": prop.min_value,
                    "max_value": prop.max_value,
                    "mutation_rate": prop.mutation_rate,
                    "description": prop.description
                }
                for name, prop in self.properties.items()
            }
        }

    @classmethod
    def from_dict(cls, data: Dict) -> 'Gene':
        """Cria um gene a partir de um dicionário."""
        properties = {
            name: GeneProperty(
                name=name,
                value=prop["value"],
                min_value=prop["min_value"],
                max_value=prop["max_value"],
                mutation_rate=prop.get("mutation_rate", 0.05),
                description=prop.get("description", "")
            )
            for name, prop in data["properties"].items()
        }
        gene = cls(
            name=data["name"],
            properties=properties,
            active=data["active"],
            description=data.get("description", "")
        )
        gene.id = data["id"]
        return gene


class Genome:
    """
    Representa o conjunto completo de genes de uma célula.
    """
    def __init__(self, genes: Optional[List[Gene]] = None):
        self.genes = genes or []

    def add_gene(self, gene: Gene) -> None:
        """Adiciona um gene ao genoma."""
        self.genes.append(gene)

    def get_gene(self, gene_id: str) -> Optional[Gene]:
        """Obtém um gene pelo ID."""
        for gene in self.genes:
            if gene.id == gene_id:
                return gene
        return None

    def get_gene_by_name(self, name: str) -> Optional[Gene]:
        """Obtém um gene pelo nome."""
        for gene in self.genes:
            if gene.name == name:
                return gene
        return None

    def mutate(self, mutation_probability: float = 0.3) -> None:
        """Aplica mutações a genes aleatórios."""
        for gene in self.genes:
            if random.random() < mutation_probability:
                gene.mutate()

    def copy(self) -> 'Genome':
        """Cria uma cópia do genoma."""
        return Genome(genes=[gene.copy() for gene in self.genes])

    def to_dict(self) -> Dict:
        """Converte o genoma para um dicionário."""
        return {
            "genes": [gene.to_dict() for gene in self.genes]
        }

    @classmethod
    def from_dict(cls, data: Dict) -> 'Genome':
        """Cria um genoma a partir de um dicionário."""
        genes = [Gene.from_dict(gene_data) for gene_data in data["genes"]]
        return cls(genes=genes)


def create_default_genome() -> Genome:
    """Cria um genoma padrão com genes básicos."""
    # Gene de tamanho
    size_gene = Gene(
        name="Tamanho",
        properties={
            "radius": GeneProperty(
                name="radius",
                value=10.0,
                min_value=5.0,
                max_value=30.0,
                mutation_rate=0.1,
                description="Raio base da célula"
            ),
            "growth_rate": GeneProperty(
                name="growth_rate",
                value=0.1,  # Aumentado para crescimento mais rápido
                min_value=0.05,
                max_value=0.3,
                mutation_rate=0.05,
                description="Taxa de crescimento da célula"
            )
        },
        description="Controla o tamanho e crescimento da célula"
    )

    # Gene de metabolismo
    metabolism_gene = Gene(
        name="Metabolismo",
        properties={
            "efficiency": GeneProperty(
                name="efficiency",
                value=0.8,  # Aumentado para melhor eficiência
                min_value=0.4,
                max_value=0.95,
                mutation_rate=0.05,
                description="Eficiência na conversão de nutrientes"
            ),
            "consumption_rate": GeneProperty(
                name="consumption_rate",
                value=0.15,  # Aumentado para consumo mais rápido
                min_value=0.05,
                max_value=0.4,
                mutation_rate=0.05,
                description="Taxa de consumo de nutrientes"
            )
        },
        description="Controla como a célula processa nutrientes"
    )

    # Gene de reprodução
    reproduction_gene = Gene(
        name="Reprodução",
        properties={
            "division_threshold": GeneProperty(
                name="division_threshold",
                value=15.0,  # Reduzido para facilitar a divisão
                min_value=12.0,
                max_value=30.0,
                mutation_rate=0.05,
                description="Tamanho necessário para divisão"
            ),
            "division_energy_cost": GeneProperty(
                name="division_energy_cost",
                value=0.4,  # Reduzido para facilitar a divisão
                min_value=0.2,
                max_value=0.6,
                mutation_rate=0.05,
                description="Custo energético da divisão"
            )
        },
        description="Controla a reprodução celular"
    )

    # Gene de flexibilidade da membrana
    membrane_gene = Gene(
        name="Membrana",
        properties={
            "flexibility": GeneProperty(
                name="flexibility",
                value=0.5,
                min_value=0.1,
                max_value=0.9,
                mutation_rate=0.05,
                description="Flexibilidade da membrana celular"
            ),
            "strength": GeneProperty(
                name="strength",
                value=0.6,
                min_value=0.2,
                max_value=0.9,
                mutation_rate=0.05,
                description="Resistência da membrana"
            )
        },
        description="Controla as propriedades físicas da membrana"
    )

    # Criar o genoma com os genes
    genome = Genome()
    genome.add_gene(size_gene)
    genome.add_gene(metabolism_gene)
    genome.add_gene(reproduction_gene)
    genome.add_gene(membrane_gene)

    return genome
