"""
Módulo para definição e manipulação de células.
"""
import math
import random
import uuid
from dataclasses import dataclass
from typing import Dict, List, Optional, Tuple, Union

import numpy as np

from .gene import Genome, create_default_genome


@dataclass
class Point:
    """Representa um ponto 2D."""
    x: float
    y: float

    def distance_to(self, other: 'Point') -> float:
        """Calcula a distância euclidiana até outro ponto."""
        return math.sqrt((self.x - other.x) ** 2 + (self.y - other.y) ** 2)

    def __add__(self, other: 'Point') -> 'Point':
        return Point(self.x + other.x, self.y + other.y)

    def __sub__(self, other: 'Point') -> 'Point':
        return Point(self.x - other.x, self.y - other.y)

    def __mul__(self, scalar: float) -> 'Point':
        return Point(self.x * scalar, self.y * scalar)


class Cell:
    """
    Representa uma célula com propriedades físicas e genéticas.
    """
    def __init__(self, position: Point, genome: Optional[Genome] = None,
                 energy: float = 100.0, age: int = 0):
        self.id = str(uuid.uuid4())
        self.position = position
        self.genome = genome or create_default_genome()
        self.energy = energy
        self.age = age
        self.is_alive = True

        # Inicializa a forma da célula
        self.initialize_shape()

        # Velocidade da célula
        self.velocity = Point(
            random.uniform(-0.5, 0.5),
            random.uniform(-0.5, 0.5)
        )

        # Histórico de estados para análise
        self.history = []

    def initialize_shape(self) -> None:
        """Inicializa a forma da célula como um círculo com pontos de controle."""
        size_gene = self.genome.get_gene_by_name("Tamanho")
        membrane_gene = self.genome.get_gene_by_name("Membrana")

        if size_gene and membrane_gene:
            self.base_radius = size_gene.properties["radius"].value
            self.flexibility = membrane_gene.properties["flexibility"].value
        else:
            self.base_radius = 10.0
            self.flexibility = 0.5

        # Pontos de controle para a forma da célula (inicialmente um círculo perfeito)
        self.num_control_points = 16
        self.control_points = []
        self.deformation_factors = []

        for i in range(self.num_control_points):
            angle = 2 * math.pi * i / self.num_control_points
            x = self.base_radius * math.cos(angle)
            y = self.base_radius * math.sin(angle)
            self.control_points.append(Point(x, y))
            # Fator de deformação inicial (1.0 = sem deformação)
            self.deformation_factors.append(1.0)

    def get_radius(self) -> float:
        """Obtém o raio atual da célula."""
        size_gene = self.genome.get_gene_by_name("Tamanho")
        if size_gene:
            return size_gene.properties["radius"].value
        return self.base_radius

    def get_actual_shape(self) -> List[Point]:
        """
        Retorna a forma atual da célula, considerando deformações.
        """
        actual_points = []
        for i, point in enumerate(self.control_points):
            # Aplica o fator de deformação ao ponto
            deformed_point = Point(
                self.position.x + point.x * self.deformation_factors[i],
                self.position.y + point.y * self.deformation_factors[i]
            )
            actual_points.append(deformed_point)
        return actual_points

    def update_shape(self, nearby_cells: List['Cell'], bounds: Tuple[int, int]) -> None:
        """
        Atualiza a forma da célula com base nas células próximas e limites do ambiente.

        Args:
            nearby_cells: Lista de células próximas que podem causar deformação
            bounds: Limites do ambiente (width, height)
        """
        # Resetar fatores de deformação
        self.deformation_factors = [1.0] * self.num_control_points

        # Verificar colisões com outras células
        for cell in nearby_cells:
            if cell.id == self.id:
                continue

            # Calcular distância entre células
            distance = self.position.distance_to(cell.position)
            combined_radius = self.get_radius() + cell.get_radius()

            # Se as células estão se sobrepondo
            if distance < combined_radius:
                # Calcular a direção da colisão
                direction = Point(
                    cell.position.x - self.position.x,
                    cell.position.y - self.position.y
                )

                # Normalizar a direção
                if distance > 0:
                    direction = Point(
                        direction.x / distance,
                        direction.y / distance
                    )

                # Aplicar deformação aos pontos de controle
                for i in range(self.num_control_points):
                    point = self.control_points[i]
                    # Calcular o ângulo entre o ponto e a direção da colisão
                    point_angle = math.atan2(point.y, point.x)
                    direction_angle = math.atan2(direction.y, direction.x)
                    angle_diff = abs((point_angle - direction_angle + math.pi) % (2 * math.pi) - math.pi)

                    # Se o ponto está na direção da colisão
                    if angle_diff < math.pi / 2:
                        # Calcular fator de deformação baseado na proximidade da colisão
                        deform_factor = 1.0 - (1.0 - distance / combined_radius) * self.flexibility * (1.0 - angle_diff / (math.pi / 2))
                        self.deformation_factors[i] = min(self.deformation_factors[i], max(0.5, deform_factor))

        # Verificar colisões com os limites do ambiente
        width, height = bounds
        margin = self.get_radius()

        # Colisão com as bordas
        if self.position.x < margin:
            # Deformar pontos na direção esquerda
            self.deform_direction(math.pi, 1.0 - self.position.x / margin)
        elif self.position.x > width - margin:
            # Deformar pontos na direção direita
            self.deform_direction(0, 1.0 - (width - self.position.x) / margin)

        if self.position.y < margin:
            # Deformar pontos na direção superior
            self.deform_direction(3 * math.pi / 2, 1.0 - self.position.y / margin)
        elif self.position.y > height - margin:
            # Deformar pontos na direção inferior
            self.deform_direction(math.pi / 2, 1.0 - (height - self.position.y) / margin)

    def deform_direction(self, direction_angle: float, intensity: float) -> None:
        """
        Deforma a célula em uma direção específica.

        Args:
            direction_angle: Ângulo da direção (em radianos)
            intensity: Intensidade da deformação (0.0 a 1.0)
        """
        for i in range(self.num_control_points):
            point = self.control_points[i]
            point_angle = math.atan2(point.y, point.x)
            angle_diff = abs((point_angle - direction_angle + math.pi) % (2 * math.pi) - math.pi)

            if angle_diff < math.pi / 2:
                deform_factor = 1.0 - intensity * self.flexibility * (1.0 - angle_diff / (math.pi / 2))
                self.deformation_factors[i] = min(self.deformation_factors[i], max(0.5, deform_factor))

    def update(self, nutrients: float, dt: float = 1.0) -> None:
        """
        Atualiza o estado da célula.

        Args:
            nutrients: Quantidade de nutrientes disponíveis
            dt: Delta de tempo
        """
        if not self.is_alive:
            return

        # Obter genes relevantes
        size_gene = self.genome.get_gene_by_name("Tamanho")
        metabolism_gene = self.genome.get_gene_by_name("Metabolismo")

        # Consumir nutrientes
        if metabolism_gene:
            consumption_rate = metabolism_gene.properties["consumption_rate"].value
            efficiency = metabolism_gene.properties["efficiency"].value

            # Quantidade de nutrientes consumidos
            consumed = min(nutrients, consumption_rate * dt)

            # Converter nutrientes em energia
            self.energy += consumed * efficiency * 10

        # Crescer
        if size_gene and self.energy > 5:
            growth_rate = size_gene.properties["growth_rate"].value
            current_radius = size_gene.properties["radius"].value

            # Crescer proporcionalmente à energia disponível
            growth = growth_rate * dt * (self.energy / 100)
            new_radius = current_radius + growth

            # Limitar o crescimento ao máximo permitido
            max_radius = size_gene.properties["radius"].max_value
            size_gene.properties["radius"].value = min(new_radius, max_radius)

            # Consumir energia para crescer
            self.energy -= growth * 5

        # Verificar se deve se dividir
        self.check_division()

        # Envelhecer
        self.age += dt

        # Consumir energia para manutenção
        self.energy -= 0.1 * dt

        # Verificar se a célula morreu
        if self.energy <= 0:
            self.is_alive = False

        # Atualizar posição com base na velocidade
        self.position.x += self.velocity.x * dt
        self.position.y += self.velocity.y * dt

        # Registrar estado atual no histórico
        self.record_state()

    def check_division(self) -> Optional['Cell']:
        """
        Verifica se a célula deve se dividir e realiza a divisão se necessário.

        Returns:
            Cell: Nova célula criada pela divisão, ou None se não houve divisão
        """
        reproduction_gene = self.genome.get_gene_by_name("Reprodução")
        size_gene = self.genome.get_gene_by_name("Tamanho")

        if not reproduction_gene or not size_gene:
            return None

        division_threshold = reproduction_gene.properties["division_threshold"].value
        division_energy_cost = reproduction_gene.properties["division_energy_cost"].value
        current_radius = size_gene.properties["radius"].value

        # Verificar se a célula está grande o suficiente e tem energia suficiente
        # Reduzir o limiar para facilitar a divisão
        if current_radius >= division_threshold and self.energy >= 50 * division_energy_cost:
            # Criar nova célula
            new_genome = self.genome.copy()
            new_genome.mutate()  # Aplicar mutações ao genoma da nova célula

            # Posição da nova célula (ligeiramente deslocada)
            angle = random.uniform(0, 2 * math.pi)
            offset_distance = current_radius * 0.8
            new_position = Point(
                self.position.x + math.cos(angle) * offset_distance,
                self.position.y + math.sin(angle) * offset_distance
            )

            # Criar nova célula
            new_cell = Cell(
                position=new_position,
                genome=new_genome,
                energy=self.energy * 0.4  # Transferir parte da energia
            )

            # Reduzir tamanho e energia da célula mãe
            size_gene.properties["radius"].value = current_radius * 0.7
            self.energy *= 0.6  # Perder parte da energia na divisão

            # Reinicializar a forma
            self.initialize_shape()

            return new_cell

        return None

    def record_state(self) -> None:
        """Registra o estado atual da célula no histórico."""
        state = {
            "age": self.age,
            "energy": self.energy,
            "radius": self.get_radius(),
            "position": (self.position.x, self.position.y)
        }
        self.history.append(state)

        # Limitar o tamanho do histórico
        if len(self.history) > 100:
            self.history = self.history[-100:]

    def to_dict(self) -> Dict:
        """Converte a célula para um dicionário."""
        return {
            "id": self.id,
            "position": {"x": self.position.x, "y": self.position.y},
            "energy": self.energy,
            "age": self.age,
            "is_alive": self.is_alive,
            "genome": self.genome.to_dict(),
            "base_radius": self.base_radius,
            "flexibility": self.flexibility,
            "velocity": {"x": self.velocity.x, "y": self.velocity.y}
        }

    @classmethod
    def from_dict(cls, data: Dict) -> 'Cell':
        """Cria uma célula a partir de um dicionário."""
        position = Point(data["position"]["x"], data["position"]["y"])
        genome = Genome.from_dict(data["genome"])

        cell = cls(
            position=position,
            genome=genome,
            energy=data["energy"],
            age=data["age"]
        )

        cell.id = data["id"]
        cell.is_alive = data["is_alive"]
        cell.base_radius = data["base_radius"]
        cell.flexibility = data["flexibility"]
        cell.velocity = Point(data["velocity"]["x"], data["velocity"]["y"])

        return cell
