"""
Módulo com funções auxiliares para a simulação de células.
"""
import json
import math
import os
import random
from typing import Dict, List, Optional, Tuple, Union

import numpy as np

from ..models.cell import Cell, Point
from ..models.gene import Gene, Genome
from ..simulation.environment import Environment


def distance(p1: Point, p2: Point) -> float:
    """
    Calcula a distância euclidiana entre dois pontos.
    
    Args:
        p1: Primeiro ponto
        p2: Se<PERSON><PERSON> ponto
    
    Returns:
        Distância entre os pontos
    """
    return math.sqrt((p1.x - p2.x) ** 2 + (p1.y - p2.y) ** 2)


def normalize_vector(x: float, y: float) -> Tuple[float, float]:
    """
    Normaliza um vetor 2D.
    
    Args:
        x: Componente X do vetor
        y: Componente Y do vetor
    
    Returns:
        Tupla com as componentes normalizadas
    """
    length = math.sqrt(x * x + y * y)
    if length < 0.0001:
        return 0.0, 0.0
    return x / length, y / length


def random_point_in_circle(center: Point, radius: float) -> Point:
    """
    Gera um ponto aleatório dentro de um círculo.
    
    Args:
        center: Centro do círculo
        radius: Raio do círculo
    
    Returns:
        Ponto aleatório dentro do círculo
    """
    # Gerar ângulo aleatório
    angle = random.uniform(0, 2 * math.pi)
    
    # Gerar distância aleatória do centro (distribuição uniforme)
    # Usar raiz quadrada para distribuição uniforme na área
    distance = radius * math.sqrt(random.random())
    
    # Calcular coordenadas
    x = center.x + distance * math.cos(angle)
    y = center.y + distance * math.sin(angle)
    
    return Point(x, y)


def create_random_cell(x_range: Tuple[float, float], 
                      y_range: Tuple[float, float]) -> Cell:
    """
    Cria uma célula com posição aleatória.
    
    Args:
        x_range: Intervalo para a coordenada X (min, max)
        y_range: Intervalo para a coordenada Y (min, max)
    
    Returns:
        Nova célula com posição aleatória
    """
    x = random.uniform(x_range[0], x_range[1])
    y = random.uniform(y_range[0], y_range[1])
    
    return Cell(position=Point(x, y))


def create_random_genome(mutation_rate: float = 0.3) -> Genome:
    """
    Cria um genoma com valores aleatórios.
    
    Args:
        mutation_rate: Taxa de mutação para aplicar ao genoma padrão
    
    Returns:
        Genoma com valores aleatórios
    """
    from ..models.gene import create_default_genome
    
    # Criar genoma padrão
    genome = create_default_genome()
    
    # Aplicar mutações aleatórias
    for _ in range(5):  # Aplicar várias mutações
        genome.mutate(mutation_rate)
    
    return genome


def save_environment(environment: Environment, filepath: str) -> None:
    """
    Salva o estado do ambiente em um arquivo JSON.
    
    Args:
        environment: Ambiente a ser salvo
        filepath: Caminho do arquivo
    """
    # Converter ambiente para dicionário
    data = environment.to_dict()
    
    # Salvar em arquivo JSON
    with open(filepath, 'w') as f:
        json.dump(data, f, indent=2)


def load_environment(filepath: str) -> Optional[Environment]:
    """
    Carrega o estado do ambiente de um arquivo JSON.
    
    Args:
        filepath: Caminho do arquivo
    
    Returns:
        Ambiente carregado ou None se ocorrer erro
    """
    try:
        # Carregar arquivo JSON
        with open(filepath, 'r') as f:
            data = json.load(f)
        
        # Criar ambiente a partir dos dados
        return Environment.from_dict(data)
    except Exception as e:
        print(f"Erro ao carregar ambiente: {e}")
        return None


def calculate_statistics(cells: List[Cell]) -> Dict:
    """
    Calcula estatísticas sobre uma lista de células.
    
    Args:
        cells: Lista de células
    
    Returns:
        Dicionário com estatísticas
    """
    if not cells:
        return {
            "count": 0,
            "avg_energy": 0,
            "avg_size": 0,
            "avg_age": 0,
            "max_energy": 0,
            "max_size": 0,
            "max_age": 0
        }
    
    # Calcular estatísticas
    count = len(cells)
    total_energy = sum(cell.energy for cell in cells)
    total_size = sum(cell.get_radius() for cell in cells)
    total_age = sum(cell.age for cell in cells)
    
    max_energy = max(cell.energy for cell in cells)
    max_size = max(cell.get_radius() for cell in cells)
    max_age = max(cell.age for cell in cells)
    
    return {
        "count": count,
        "avg_energy": total_energy / count,
        "avg_size": total_size / count,
        "avg_age": total_age / count,
        "max_energy": max_energy,
        "max_size": max_size,
        "max_age": max_age
    }


def create_grid_of_cells(rows: int, cols: int, 
                         width: int, height: int) -> List[Cell]:
    """
    Cria uma grade de células uniformemente distribuídas.
    
    Args:
        rows: Número de linhas
        cols: Número de colunas
        width: Largura total da área
        height: Altura total da área
    
    Returns:
        Lista de células em grade
    """
    cells = []
    
    # Calcular espaçamento
    x_spacing = width / (cols + 1)
    y_spacing = height / (rows + 1)
    
    # Criar células em grade
    for row in range(1, rows + 1):
        for col in range(1, cols + 1):
            x = col * x_spacing
            y = row * y_spacing
            
            cell = Cell(position=Point(x, y))
            cells.append(cell)
    
    return cells


def apply_random_mutations(cells: List[Cell], 
                          probability: float = 0.1,
                          intensity: float = 0.3) -> None:
    """
    Aplica mutações aleatórias a uma lista de células.
    
    Args:
        cells: Lista de células
        probability: Probabilidade de mutação para cada célula
        intensity: Intensidade da mutação (0.0 a 1.0)
    """
    for cell in cells:
        if random.random() < probability:
            cell.genome.mutate(intensity)


def find_nearest_cell(position: Point, cells: List[Cell]) -> Optional[Cell]:
    """
    Encontra a célula mais próxima de uma posição.
    
    Args:
        position: Posição de referência
        cells: Lista de células
    
    Returns:
        Célula mais próxima ou None se a lista estiver vazia
    """
    if not cells:
        return None
    
    nearest = cells[0]
    min_distance = distance(position, nearest.position)
    
    for cell in cells[1:]:
        dist = distance(position, cell.position)
        if dist < min_distance:
            min_distance = dist
            nearest = cell
    
    return nearest


def create_color_gradient(value: float, min_value: float, max_value: float,
                         start_color: Tuple[int, int, int],
                         end_color: Tuple[int, int, int]) -> Tuple[int, int, int]:
    """
    Cria uma cor em um gradiente com base em um valor.
    
    Args:
        value: Valor a ser mapeado
        min_value: Valor mínimo
        max_value: Valor máximo
        start_color: Cor inicial (R, G, B)
        end_color: Cor final (R, G, B)
    
    Returns:
        Cor interpolada (R, G, B)
    """
    # Normalizar valor entre 0 e 1
    if max_value == min_value:
        ratio = 0.5
    else:
        ratio = (value - min_value) / (max_value - min_value)
    ratio = max(0, min(1, ratio))
    
    # Interpolar componentes de cor
    r = int(start_color[0] + ratio * (end_color[0] - start_color[0]))
    g = int(start_color[1] + ratio * (end_color[1] - start_color[1]))
    b = int(start_color[2] + ratio * (end_color[2] - start_color[2]))
    
    return (r, g, b)
