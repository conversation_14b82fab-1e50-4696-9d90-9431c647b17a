"""
Módulo para a interface gráfica da simulação de células.
"""
import random
import sys
from typing import Dict, List, Optional, Tuple

import numpy as np
from PyQt6.QtCore import QPointF, QRectF, QTimer, Qt, pyqtSignal
from PyQt6.QtGui import QBrush, QColor, QPainter, QPen, QPolygonF
from PyQt6.QtWidgets import (QApplication, QGraphicsEllipseItem,
                             QGraphicsItem, QGraphicsPolygonItem,
                             QGraphicsScene, QGraphicsView, QHBoxLayout,
                             QLabel, QMainWindow, QPushButton, QSlider,
                             QVBoxLayout, QWidget)

from ..models.cell import Cell, Point
from ..simulation.environment import Environment


class CellGraphicsItem(QGraphicsPolygonItem):
    """Item gráfico para representar uma célula."""

    def __init__(self, cell: Cell):
        super().__init__()
        self.cell = cell
        self.setAcceptHoverEvents(True)
        self.update_polygon()

        # Definir aparência
        self.setBrush(QBrush(QColor(100, 200, 100, 180)))
        self.setPen(QPen(QColor(20, 100, 20), 1.5))

        # Adicionar núcleo
        self.nucleus = QGraphicsEllipseItem(0, 0, 0, 0, self)
        self.nucleus.setBrush(QBrush(QColor(50, 100, 50, 200)))
        self.nucleus.setPen(QPen(Qt.PenStyle.NoPen))
        self.update_nucleus()

    def update_polygon(self) -> None:
        """Atualiza o polígono da célula com base na forma atual."""
        # Obter a forma atual da célula
        shape_points = self.cell.get_actual_shape()

        # Converter para QPolygonF
        polygon = QPolygonF()
        for point in shape_points:
            polygon.append(QPointF(point.x, point.y))

        self.setPolygon(polygon)

    def update_nucleus(self) -> None:
        """Atualiza a posição e tamanho do núcleo."""
        radius = self.cell.get_radius() * 0.4
        x = self.cell.position.x - radius
        y = self.cell.position.y - radius
        self.nucleus.setRect(x, y, radius * 2, radius * 2)

    def update(self) -> None:
        """Atualiza a aparência da célula."""
        self.update_polygon()
        self.update_nucleus()

        # Ajustar cor com base na energia
        energy_ratio = min(1.0, self.cell.energy / 100.0)
        green = int(100 + energy_ratio * 155)
        self.setBrush(QBrush(QColor(100, green, 100, 180)))

    def hoverEnterEvent(self, event) -> None:
        """Evento quando o mouse entra na área da célula."""
        self.setBrush(QBrush(QColor(150, 230, 150, 200)))
        self.setPen(QPen(QColor(20, 150, 20), 2.0))
        super().hoverEnterEvent(event)

    def hoverLeaveEvent(self, event) -> None:
        """Evento quando o mouse sai da área da célula."""
        energy_ratio = min(1.0, self.cell.energy / 100.0)
        green = int(100 + energy_ratio * 155)
        self.setBrush(QBrush(QColor(100, green, 100, 180)))
        self.setPen(QPen(QColor(20, 100, 20), 1.5))
        super().hoverLeaveEvent(event)


class CellInfoWidget(QWidget):
    """Widget para exibir informações sobre uma célula."""

    def __init__(self):
        super().__init__()
        self.initUI()

    def initUI(self) -> None:
        """Inicializa a interface do widget."""
        self.layout = QVBoxLayout()
        self.setLayout(self.layout)

        # Título
        self.title_label = QLabel("Informações da Célula")
        self.title_label.setStyleSheet("font-weight: bold; font-size: 14px;")
        self.layout.addWidget(self.title_label)

        # Informações básicas
        self.id_label = QLabel("ID: -")
        self.age_label = QLabel("Idade: -")
        self.energy_label = QLabel("Energia: -")
        self.size_label = QLabel("Tamanho: -")

        self.layout.addWidget(self.id_label)
        self.layout.addWidget(self.age_label)
        self.layout.addWidget(self.energy_label)
        self.layout.addWidget(self.size_label)

        # Título dos genes
        self.genes_title = QLabel("Genes:")
        self.genes_title.setStyleSheet("font-weight: bold; margin-top: 10px;")
        self.layout.addWidget(self.genes_title)

        # Lista de genes (será preenchida dinamicamente)
        self.gene_labels = []

        # Espaçador para empurrar tudo para cima
        self.layout.addStretch()

        # Estilo
        self.setStyleSheet("""
            QWidget {
                background-color: rgba(240, 255, 240, 220);
                border: 1px solid rgba(0, 100, 0, 150);
                border-radius: 5px;
                padding: 10px;
            }
            QLabel {
                color: #333;
            }
        """)

        # Tamanho fixo
        self.setFixedWidth(250)
        self.setMinimumHeight(300)

        # Inicialmente oculto
        self.hide()

    def update_info(self, cell: Cell) -> None:
        """Atualiza as informações exibidas com base na célula."""
        if not cell:
            self.hide()
            return

        # Atualizar informações básicas
        self.id_label.setText(f"ID: {cell.id[:8]}...")
        self.age_label.setText(f"Idade: {cell.age:.1f}")
        self.energy_label.setText(f"Energia: {cell.energy:.1f}")
        self.size_label.setText(f"Tamanho: {cell.get_radius():.1f}")

        # Limpar labels de genes antigos
        for label in self.gene_labels:
            self.layout.removeWidget(label)
            label.deleteLater()
        self.gene_labels = []

        # Adicionar informações dos genes
        for gene in cell.genome.genes:
            # Label para o nome do gene
            gene_label = QLabel(f"<b>{gene.name}</b>: {gene.description}")
            gene_label.setWordWrap(True)
            self.layout.addWidget(gene_label)
            self.gene_labels.append(gene_label)

            # Labels para as propriedades do gene
            for name, prop in gene.properties.items():
                prop_label = QLabel(f"  • {prop.name}: {prop.value:.2f}")
                self.layout.addWidget(prop_label)
                self.gene_labels.append(prop_label)

        # Adicionar espaçador novamente
        self.layout.addStretch()

        # Mostrar o widget
        self.show()


class PopulationGraphWidget(QWidget):
    """Widget para exibir gráfico de população."""

    def __init__(self):
        super().__init__()
        self.points = []
        self.max_points = 100
        self.max_value = 10
        self.setMinimumHeight(100)
        self.setMinimumWidth(200)

    def update_data(self, data: List[int]) -> None:
        """Atualiza os dados do gráfico."""
        # Limitar o número de pontos
        if len(data) > self.max_points:
            data = data[-self.max_points:]

        self.points = data
        self.max_value = max(self.points) if self.points else 10
        self.update()

    def paintEvent(self, event) -> None:
        """Evento de pintura do widget."""
        if not self.points:
            return

        painter = QPainter(self)
        painter.setRenderHint(QPainter.RenderHint.Antialiasing)

        # Configurar pincel e caneta
        painter.setBrush(QBrush(QColor(200, 240, 200)))
        painter.setPen(QPen(QColor(0, 100, 0), 2))

        # Dimensões do widget
        width = self.width()
        height = self.height()

        # Desenhar fundo
        painter.fillRect(0, 0, width, height, QColor(240, 255, 240))

        # Desenhar eixos
        painter.setPen(QPen(QColor(100, 100, 100), 1))
        painter.drawLine(0, height - 1, width, height - 1)  # Eixo X
        painter.drawLine(1, 0, 1, height)  # Eixo Y

        # Desenhar linhas de grade
        painter.setPen(QPen(QColor(200, 200, 200), 1, Qt.PenStyle.DashLine))
        for i in range(1, 5):
            y = int(height - (i * height / 5))
            painter.drawLine(0, y, width, y)

        # Desenhar valores no eixo Y
        painter.setPen(QPen(QColor(0, 0, 0), 1))
        for i in range(0, 6):
            y = int(height - (i * height / 5))
            value = int(i * self.max_value / 5)
            painter.drawText(5, y, str(value))

        # Desenhar o gráfico
        if len(self.points) > 1:
            painter.setPen(QPen(QColor(0, 100, 0), 2))

            # Criar polígono para área preenchida
            polygon = QPolygonF()
            polygon.append(QPointF(0, height))  # Ponto inicial na base

            # Adicionar pontos do gráfico
            for i, value in enumerate(self.points):
                x = i * width / (len(self.points) - 1)
                y = height - (value * height / self.max_value) if self.max_value > 0 else height
                polygon.append(QPointF(x, y))

            # Fechar o polígono
            polygon.append(QPointF(width, height))  # Ponto final na base

            # Desenhar área preenchida
            painter.setBrush(QBrush(QColor(100, 200, 100, 100)))
            painter.drawPolygon(polygon)

            # Desenhar linha
            painter.setBrush(Qt.BrushStyle.NoBrush)
            for i in range(len(self.points) - 1):
                x1 = int(i * width / (len(self.points) - 1))
                y1 = int(height - (self.points[i] * height / self.max_value) if self.max_value > 0 else height)
                x2 = int((i + 1) * width / (len(self.points) - 1))
                y2 = int(height - (self.points[i + 1] * height / self.max_value) if self.max_value > 0 else height)
                painter.drawLine(x1, y1, x2, y2)


class SimulationView(QGraphicsView):
    """Visualização da simulação de células."""

    cell_clicked = pyqtSignal(Cell)
    cell_hovered = pyqtSignal(Cell)
    cell_hover_left = pyqtSignal()

    def __init__(self, environment: Environment):
        self.scene = QGraphicsScene()
        super().__init__(self.scene)

        self.environment = environment
        self.cell_items = {}  # Mapeamento de ID de célula para item gráfico

        # Configurar visualização
        self.setRenderHint(QPainter.RenderHint.Antialiasing)
        self.setBackgroundBrush(QBrush(QColor(240, 240, 255)))
        self.setSceneRect(0, 0, environment.width, environment.height)
        self.setHorizontalScrollBarPolicy(Qt.ScrollBarPolicy.ScrollBarAlwaysOff)
        self.setVerticalScrollBarPolicy(Qt.ScrollBarPolicy.ScrollBarAlwaysOff)

        # Configurar interação
        self.setInteractive(True)
        self.setMouseTracking(True)

        # Célula atualmente sob o mouse
        self.hovered_cell = None

        # Configurações de interação
        self.add_cells_enabled = True  # Se é possível adicionar células ao clicar
        self.selected_cell = None  # Célula selecionada para copiar genes
        self.copy_genes_mode = False  # Se está no modo de copiar genes

    def update_view(self) -> None:
        """Atualiza a visualização com o estado atual do ambiente."""
        # Remover células que não existem mais
        cell_ids = {cell.id for cell in self.environment.cells}
        for cell_id in list(self.cell_items.keys()):
            if cell_id not in cell_ids:
                self.scene.removeItem(self.cell_items[cell_id])
                del self.cell_items[cell_id]

        # Atualizar ou adicionar células existentes
        for cell in self.environment.cells:
            if cell.id in self.cell_items:
                # Atualizar célula existente
                self.cell_items[cell.id].update()

                # Destacar a célula selecionada
                if self.selected_cell and cell.id == self.selected_cell.id:
                    self.cell_items[cell.id].setBrush(QBrush(QColor(200, 100, 200, 180)))
                    self.cell_items[cell.id].setPen(QPen(QColor(150, 50, 150), 2.0))
            else:
                # Adicionar nova célula
                cell_item = CellGraphicsItem(cell)
                self.scene.addItem(cell_item)
                self.cell_items[cell.id] = cell_item

    def mousePressEvent(self, event) -> None:
        """Evento de clique do mouse."""
        if event.button() == Qt.MouseButton.LeftButton:
            # Converter posição do mouse para coordenadas da cena
            scene_pos = self.mapToScene(event.pos())
            x, y = scene_pos.x(), scene_pos.y()

            # Verificar se clicou em uma célula
            cell = self.environment.get_cell_at(x, y)

            if cell:
                # Emitir sinal de célula clicada
                self.cell_clicked.emit(cell)

                # Se estiver no modo de copiar genes, selecionar esta célula
                if self.copy_genes_mode:
                    self.selected_cell = cell
                    print(f"Célula selecionada para copiar genes: {cell.id[:8]}...")
            elif self.add_cells_enabled:
                # Adicionar nova célula na posição do clique
                if self.copy_genes_mode and self.selected_cell:
                    # Criar célula com genes copiados
                    self.add_cell_with_copied_genes(x, y)
                else:
                    # Adicionar célula normal
                    self.environment.add_cell(Point(x, y))

        super().mousePressEvent(event)

    def add_cell_with_copied_genes(self, x: float, y: float) -> None:
        """Adiciona uma nova célula com genes copiados da célula selecionada."""
        if not self.selected_cell:
            return

        # Criar nova célula na posição especificada
        position = Point(x, y)

        # Copiar o genoma da célula selecionada
        genome = self.selected_cell.genome.copy()

        # Criar nova célula com o genoma copiado
        new_cell = Cell(position=position, genome=genome)

        # Adicionar ao ambiente
        self.environment.cells.append(new_cell)
        print(f"Nova célula criada com genes copiados: {new_cell.id[:8]}...")

    def mouseMoveEvent(self, event) -> None:
        """Evento de movimento do mouse."""
        # Converter posição do mouse para coordenadas da cena
        scene_pos = self.mapToScene(event.pos())
        x, y = scene_pos.x(), scene_pos.y()

        # Verificar se o mouse está sobre uma célula
        cell = self.environment.get_cell_at(x, y)

        if cell:
            if self.hovered_cell != cell:
                self.hovered_cell = cell
                self.cell_hovered.emit(cell)
        elif self.hovered_cell:
            self.hovered_cell = None
            self.cell_hover_left.emit()

        super().mouseMoveEvent(event)

    def wheelEvent(self, event) -> None:
        """Evento de rolagem do mouse (zoom)."""
        zoom_factor = 1.15

        if event.angleDelta().y() > 0:
            # Zoom in
            self.scale(zoom_factor, zoom_factor)
        else:
            # Zoom out
            self.scale(1.0 / zoom_factor, 1.0 / zoom_factor)


class SimulationWindow(QMainWindow):
    """Janela principal da simulação."""

    def __init__(self, environment: Environment):
        super().__init__()
        self.environment = environment
        self.initUI()

        # Timer para atualização da simulação
        self.timer = QTimer()
        self.timer.timeout.connect(self.update_simulation)
        self.timer.start(16)  # ~60 FPS

    def initUI(self) -> None:
        """Inicializa a interface do usuário."""
        self.setWindowTitle("Simulação de Células")
        self.setGeometry(100, 100, 1200, 800)

        # Widget central
        central_widget = QWidget()
        self.setCentralWidget(central_widget)

        # Layout principal
        main_layout = QHBoxLayout()
        central_widget.setLayout(main_layout)

        # Painel de controle (lado direito)
        control_panel = QWidget()
        control_layout = QVBoxLayout()
        control_panel.setLayout(control_layout)
        control_panel.setFixedWidth(300)

        # Visualização da simulação (lado esquerdo)
        self.simulation_view = SimulationView(self.environment)

        # Conectar sinais da visualização
        self.simulation_view.cell_clicked.connect(self.show_cell_info)
        self.simulation_view.cell_hovered.connect(self.show_cell_info)
        self.simulation_view.cell_hover_left.connect(self.hide_cell_info)

        # Adicionar visualização e painel de controle ao layout principal
        main_layout.addWidget(self.simulation_view, 3)
        main_layout.addWidget(control_panel, 1)

        # Adicionar controles ao painel

        # Título
        title_label = QLabel("Controles da Simulação")
        title_label.setStyleSheet("font-weight: bold; font-size: 16px;")
        control_layout.addWidget(title_label)

        # Controle de velocidade
        speed_label = QLabel("Velocidade da Simulação:")
        control_layout.addWidget(speed_label)

        self.speed_slider = QSlider(Qt.Orientation.Horizontal)
        self.speed_slider.setMinimum(10)
        self.speed_slider.setMaximum(500)
        self.speed_slider.setValue(int(self.environment.simulation_speed * 100))
        self.speed_slider.valueChanged.connect(self.update_speed)
        control_layout.addWidget(self.speed_slider)

        # Controle de nutrientes
        nutrients_label = QLabel("Nível de Nutrientes:")
        control_layout.addWidget(nutrients_label)

        self.nutrients_slider = QSlider(Qt.Orientation.Horizontal)
        self.nutrients_slider.setMinimum(0)
        self.nutrients_slider.setMaximum(200)
        self.nutrients_slider.setValue(int(self.environment.nutrient_level * 100))
        self.nutrients_slider.valueChanged.connect(self.update_nutrients)
        control_layout.addWidget(self.nutrients_slider)

        # Gráfico de população
        population_label = QLabel("População:")
        control_layout.addWidget(population_label)

        self.population_graph = PopulationGraphWidget()
        control_layout.addWidget(self.population_graph)

        # Informações da célula
        self.cell_info = CellInfoWidget()
        control_layout.addWidget(self.cell_info)

        # Botões de controle da simulação
        button_layout = QHBoxLayout()

        self.reset_button = QPushButton("Reiniciar")
        self.reset_button.clicked.connect(self.reset_simulation)
        button_layout.addWidget(self.reset_button)

        self.pause_button = QPushButton("Pausar")
        self.pause_button.clicked.connect(self.toggle_pause)
        button_layout.addWidget(self.pause_button)

        control_layout.addLayout(button_layout)

        # Título para controles de células
        cells_control_title = QLabel("Controles de Células")
        cells_control_title.setStyleSheet("font-weight: bold; font-size: 14px; margin-top: 10px;")
        control_layout.addWidget(cells_control_title)

        # Botões para controle de adição de células
        cells_button_layout = QVBoxLayout()

        # Botão para ativar/desativar adição de células
        self.toggle_add_cells_button = QPushButton("Desativar Adição de Células")
        self.toggle_add_cells_button.clicked.connect(self.toggle_add_cells)
        cells_button_layout.addWidget(self.toggle_add_cells_button)

        # Botão para ativar/desativar modo de cópia de genes
        self.toggle_copy_genes_button = QPushButton("Ativar Cópia de Genes")
        self.toggle_copy_genes_button.clicked.connect(self.toggle_copy_genes_mode)
        cells_button_layout.addWidget(self.toggle_copy_genes_button)

        # Botão para adicionar célula normal
        self.add_normal_cell_button = QPushButton("Adicionar Célula Normal")
        self.add_normal_cell_button.clicked.connect(self.add_normal_cell)
        cells_button_layout.addWidget(self.add_normal_cell_button)

        # Botão para limpar seleção
        self.clear_selection_button = QPushButton("Limpar Seleção")
        self.clear_selection_button.clicked.connect(self.clear_cell_selection)
        cells_button_layout.addWidget(self.clear_selection_button)

        control_layout.addLayout(cells_button_layout)

        # Espaçador para empurrar tudo para cima
        control_layout.addStretch()

        # Estilo
        self.setStyleSheet("""
            QMainWindow {
                background-color: #f0f0f0;
            }
            QSlider::groove:horizontal {
                border: 1px solid #999999;
                height: 8px;
                background: #cccccc;
                margin: 2px 0;
                border-radius: 4px;
            }
            QSlider::handle:horizontal {
                background: #5c5c5c;
                border: 1px solid #5c5c5c;
                width: 18px;
                margin: -2px 0;
                border-radius: 9px;
            }
            QPushButton {
                background-color: #4c9e4c;
                color: white;
                border: none;
                padding: 8px 16px;
                border-radius: 4px;
            }
            QPushButton:hover {
                background-color: #45a049;
            }
        """)

    def update_simulation(self) -> None:
        """Atualiza a simulação e a interface."""
        try:
            # Atualizar o ambiente
            self.environment.update(0.1)

            # Atualizar a visualização
            self.simulation_view.update_view()

            # Atualizar o gráfico de população
            if self.environment.statistics["population"]:
                self.population_graph.update_data(self.environment.statistics["population"])
        except Exception as e:
            print(f"Erro na atualização da simulação: {e}")
            # Não interromper a simulação por causa de um erro

    def update_speed(self) -> None:
        """Atualiza a velocidade da simulação."""
        speed = self.speed_slider.value() / 100.0
        self.environment.set_simulation_speed(speed)

    def update_nutrients(self) -> None:
        """Atualiza o nível de nutrientes."""
        nutrients = self.nutrients_slider.value() / 100.0
        self.environment.set_nutrient_level(nutrients)

    def show_cell_info(self, cell: Cell) -> None:
        """Exibe informações sobre uma célula."""
        self.cell_info.update_info(cell)

    def hide_cell_info(self) -> None:
        """Oculta o painel de informações da célula."""
        self.cell_info.hide()

    def reset_simulation(self) -> None:
        """Reinicia a simulação."""
        # Criar novo ambiente
        self.environment = Environment(
            width=self.environment.width,
            height=self.environment.height,
            initial_cells=5,
            nutrient_level=self.environment.nutrient_level,
            simulation_speed=self.environment.simulation_speed
        )

        # Atualizar visualização
        self.simulation_view.environment = self.environment
        self.simulation_view.cell_items = {}
        self.simulation_view.scene.clear()

    def toggle_pause(self) -> None:
        """Pausa ou continua a simulação."""
        if self.timer.isActive():
            self.timer.stop()
            self.pause_button.setText("Continuar")
        else:
            self.timer.start(16)
            self.pause_button.setText("Pausar")

    def toggle_add_cells(self) -> None:
        """Ativa ou desativa a adição de células ao clicar."""
        self.simulation_view.add_cells_enabled = not self.simulation_view.add_cells_enabled
        if self.simulation_view.add_cells_enabled:
            self.toggle_add_cells_button.setText("Desativar Adição de Células")
        else:
            self.toggle_add_cells_button.setText("Ativar Adição de Células")

    def toggle_copy_genes_mode(self) -> None:
        """Ativa ou desativa o modo de cópia de genes."""
        self.simulation_view.copy_genes_mode = not self.simulation_view.copy_genes_mode
        if self.simulation_view.copy_genes_mode:
            self.toggle_copy_genes_button.setText("Desativar Cópia de Genes")
            print("Modo de cópia de genes ativado. Clique em uma célula para selecioná-la.")
        else:
            self.toggle_copy_genes_button.setText("Ativar Cópia de Genes")
            print("Modo de cópia de genes desativado.")

    def add_normal_cell(self) -> None:
        """Adiciona uma célula normal no centro da visualização."""
        width = self.environment.width
        height = self.environment.height
        position = Point(width / 2 + random.uniform(-50, 50), height / 2 + random.uniform(-50, 50))
        self.environment.add_cell(position)

    def clear_cell_selection(self) -> None:
        """Limpa a seleção de célula atual."""
        self.simulation_view.selected_cell = None
        print("Seleção de célula limpa.")


def run_simulation(width: int = 800, height: int = 600) -> None:
    """
    Inicia a simulação com interface gráfica.

    Args:
        width: Largura da área de simulação
        height: Altura da área de simulação
    """
    app = QApplication(sys.argv)

    # Criar ambiente
    environment = Environment(width=width, height=height, initial_cells=5)

    # Criar janela
    window = SimulationWindow(environment)
    window.show()

    sys.exit(app.exec())
