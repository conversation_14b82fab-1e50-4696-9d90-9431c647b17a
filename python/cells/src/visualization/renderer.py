"""
Módulo para renderização das células.
"""
import math
from typing import Dict, List, Optional, Tuple

import numpy as np
from PyQt6.QtCore import QPointF, QRectF, Qt
from PyQt6.QtGui import QBrush, QColor, QPainter, QPainterPath, QPen, QPolygonF
from PyQt6.QtWidgets import QGraphicsItem, QStyleOptionGraphicsItem, QWidget

from ..models.cell import Cell, Point


class CellRenderer:
    """
    Classe para renderizar células com diferentes estilos.
    """
    
    @staticmethod
    def render_cell(painter: QPainter, cell: Cell, hover: bool = False) -> None:
        """
        Renderiza uma célula com o estilo padrão.
        
        Args:
            painter: Objeto QPainter para desenho
            cell: Célula a ser renderizada
            hover: Se o mouse está sobre a célula
        """
        # Obter a forma atual da célula
        shape_points = cell.get_actual_shape()
        
        # Criar caminho para a célula
        path = QPainterPath()
        
        if shape_points:
            # Iniciar o caminho no primeiro ponto
            path.moveTo(shape_points[0].x, shape_points[0].y)
            
            # Adicionar os demais pontos
            for i in range(1, len(shape_points)):
                path.lineTo(shape_points[i].x, shape_points[i].y)
            
            # Fechar o caminho
            path.closeSubpath()
        
        # Definir cores com base no estado da célula
        energy_ratio = min(1.0, cell.energy / 100.0)
        
        if hover:
            # Cores para quando o mouse está sobre a célula
            fill_color = QColor(150, 230, 150, 200)
            border_color = QColor(20, 150, 20)
            border_width = 2.0
        else:
            # Cores normais
            green = int(100 + energy_ratio * 155)
            fill_color = QColor(100, green, 100, 180)
            border_color = QColor(20, 100, 20)
            border_width = 1.5
        
        # Desenhar a célula
        painter.setBrush(QBrush(fill_color))
        painter.setPen(QPen(border_color, border_width))
        painter.drawPath(path)
        
        # Desenhar o núcleo
        nucleus_radius = cell.get_radius() * 0.4
        nucleus_rect = QRectF(
            cell.position.x - nucleus_radius,
            cell.position.y - nucleus_radius,
            nucleus_radius * 2,
            nucleus_radius * 2
        )
        painter.setBrush(QBrush(QColor(50, 100, 50, 200)))
        painter.setPen(Qt.PenStyle.NoPen)
        painter.drawEllipse(nucleus_rect)
    
    @staticmethod
    def render_cell_simplified(painter: QPainter, cell: Cell, hover: bool = False) -> None:
        """
        Renderiza uma célula com estilo simplificado (círculo).
        Útil para visualizações com muitas células.
        
        Args:
            painter: Objeto QPainter para desenho
            cell: Célula a ser renderizada
            hover: Se o mouse está sobre a célula
        """
        radius = cell.get_radius()
        x = cell.position.x - radius
        y = cell.position.y - radius
        
        # Definir cores com base no estado da célula
        energy_ratio = min(1.0, cell.energy / 100.0)
        
        if hover:
            # Cores para quando o mouse está sobre a célula
            fill_color = QColor(150, 230, 150, 200)
            border_color = QColor(20, 150, 20)
            border_width = 2.0
        else:
            # Cores normais
            green = int(100 + energy_ratio * 155)
            fill_color = QColor(100, green, 100, 180)
            border_color = QColor(20, 100, 20)
            border_width = 1.5
        
        # Desenhar a célula
        painter.setBrush(QBrush(fill_color))
        painter.setPen(QPen(border_color, border_width))
        painter.drawEllipse(x, y, radius * 2, radius * 2)
        
        # Desenhar o núcleo
        nucleus_radius = radius * 0.4
        painter.setBrush(QBrush(QColor(50, 100, 50, 200)))
        painter.setPen(Qt.PenStyle.NoPen)
        painter.drawEllipse(
            cell.position.x - nucleus_radius,
            cell.position.y - nucleus_radius,
            nucleus_radius * 2,
            nucleus_radius * 2
        )
    
    @staticmethod
    def render_cell_detailed(painter: QPainter, cell: Cell, hover: bool = False) -> None:
        """
        Renderiza uma célula com detalhes adicionais.
        
        Args:
            painter: Objeto QPainter para desenho
            cell: Célula a ser renderizada
            hover: Se o mouse está sobre a célula
        """
        # Renderizar a célula básica primeiro
        CellRenderer.render_cell(painter, cell, hover)
        
        # Adicionar detalhes extras se estiver em hover
        if hover:
            # Desenhar um halo ao redor da célula
            radius = cell.get_radius() * 1.2
            x = cell.position.x - radius
            y = cell.position.y - radius
            
            painter.setBrush(Qt.BrushStyle.NoBrush)
            painter.setPen(QPen(QColor(100, 200, 100, 100), 3, Qt.PenStyle.DashLine))
            painter.drawEllipse(x, y, radius * 2, radius * 2)
            
            # Desenhar indicador de energia
            energy_ratio = min(1.0, cell.energy / 100.0)
            bar_width = cell.get_radius() * 1.5
            bar_height = 5
            
            # Fundo da barra
            painter.setBrush(QBrush(QColor(50, 50, 50, 150)))
            painter.setPen(Qt.PenStyle.NoPen)
            painter.drawRect(
                cell.position.x - bar_width / 2,
                cell.position.y - cell.get_radius() - 15,
                bar_width,
                bar_height
            )
            
            # Barra de energia
            painter.setBrush(QBrush(QColor(100, 200, 100, 200)))
            painter.drawRect(
                cell.position.x - bar_width / 2,
                cell.position.y - cell.get_radius() - 15,
                bar_width * energy_ratio,
                bar_height
            )
    
    @staticmethod
    def render_environment_grid(painter: QPainter, width: int, height: int, 
                                grid_size: int = 50) -> None:
        """
        Renderiza uma grade de fundo para o ambiente.
        
        Args:
            painter: Objeto QPainter para desenho
            width: Largura do ambiente
            height: Altura do ambiente
            grid_size: Tamanho das células da grade
        """
        # Configurar caneta para a grade
        painter.setPen(QPen(QColor(200, 200, 220, 100), 1, Qt.PenStyle.DotLine))
        
        # Desenhar linhas horizontais
        for y in range(0, height + 1, grid_size):
            painter.drawLine(0, y, width, y)
        
        # Desenhar linhas verticais
        for x in range(0, width + 1, grid_size):
            painter.drawLine(x, 0, x, height)
    
    @staticmethod
    def render_nutrient_distribution(painter: QPainter, width: int, height: int, 
                                    nutrient_level: float) -> None:
        """
        Renderiza a distribuição de nutrientes no ambiente.
        
        Args:
            painter: Objeto QPainter para desenho
            width: Largura do ambiente
            height: Altura do ambiente
            nutrient_level: Nível de nutrientes (0.0 a 2.0)
        """
        # Criar gradiente de cor para nutrientes
        color_intensity = int(min(255, nutrient_level * 100))
        nutrient_color = QColor(220, 255, 220, color_intensity)
        
        # Desenhar retângulo com cor de nutrientes
        painter.setBrush(QBrush(nutrient_color))
        painter.setPen(Qt.PenStyle.NoPen)
        painter.drawRect(0, 0, width, height)
    
    @staticmethod
    def render_cell_info(painter: QPainter, cell: Cell, x: int, y: int) -> None:
        """
        Renderiza informações sobre uma célula em uma posição específica.
        
        Args:
            painter: Objeto QPainter para desenho
            cell: Célula a ser renderizada
            x: Coordenada X para renderização
            y: Coordenada Y para renderização
        """
        # Configurar fonte
        painter.setPen(QColor(0, 0, 0))
        
        # Informações básicas
        painter.drawText(x, y, f"ID: {cell.id[:8]}...")
        painter.drawText(x, y + 20, f"Energia: {cell.energy:.1f}")
        painter.drawText(x, y + 40, f"Idade: {cell.age:.1f}")
        painter.drawText(x, y + 60, f"Tamanho: {cell.get_radius():.1f}")
        
        # Informações de genes
        y_offset = 80
        for gene in cell.genome.genes:
            painter.drawText(x, y + y_offset, f"{gene.name}")
            y_offset += 20
            
            for name, prop in gene.properties.items():
                painter.drawText(x + 20, y + y_offset, f"{name}: {prop.value:.2f}")
                y_offset += 20
