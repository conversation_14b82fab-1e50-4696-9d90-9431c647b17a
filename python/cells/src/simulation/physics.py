"""
Módulo para cálculos físicos da simulação de células.
"""
import math
from typing import List, Tuple

import numpy as np

from ..models.cell import Cell, Point


def calculate_repulsion(cell1: Cell, cell2: Cell) -> Tuple[float, float]:
    """
    Calcula a força de repulsão entre duas células.
    
    Args:
        cell1: Primeira célula
        cell2: Segunda célula
    
    Returns:
        <PERSON><PERSON> contendo as componentes x e y da força de repulsão
    """
    # Calcular vetor entre os centros das células
    dx = cell2.position.x - cell1.position.x
    dy = cell2.position.y - cell1.position.y
    
    # Calcular distância entre células
    distance = math.sqrt(dx * dx + dy * dy)
    
    # Evitar divisão por zero
    if distance < 0.1:
        return 0.0, 0.0
    
    # Calcular raios das células
    r1 = cell1.get_radius()
    r2 = cell2.get_radius()
    
    # Distância mínima entre células (soma dos raios)
    min_distance = r1 + r2
    
    # Se as células estão se sobrepondo
    if distance < min_distance:
        # Calcular magnitude da força (inversamente proporcional à distância)
        # Quanto mais próximas, maior a força
        magnitude = 0.1 * (min_distance - distance) / min_distance
        
        # Normalizar vetor de direção
        dx /= distance
        dy /= distance
        
        # Retornar componentes da força (negativas para repulsão)
        return -dx * magnitude, -dy * magnitude
    
    return 0.0, 0.0


def apply_forces(cell: Cell, nearby_cells: List[Cell], dt: float) -> None:
    """
    Aplica forças físicas a uma célula.
    
    Args:
        cell: Célula a ser atualizada
        nearby_cells: Lista de células próximas
        dt: Delta de tempo
    """
    # Inicializar forças resultantes
    force_x = 0.0
    force_y = 0.0
    
    # Aplicar forças de repulsão de outras células
    for other in nearby_cells:
        if other.id == cell.id or not other.is_alive:
            continue
        
        # Calcular força de repulsão
        rep_x, rep_y = calculate_repulsion(cell, other)
        force_x += rep_x
        force_y += rep_y
    
    # Aplicar atrito para evitar movimento perpétuo
    friction = 0.02
    force_x -= cell.velocity.x * friction
    force_y -= cell.velocity.y * friction
    
    # Aplicar forças à velocidade da célula
    cell.velocity.x += force_x * dt
    cell.velocity.y += force_y * dt


def handle_collisions(cells: List[Cell], width: int, height: int, dt: float) -> None:
    """
    Trata colisões entre células e com os limites do ambiente.
    
    Args:
        cells: Lista de células
        width: Largura do ambiente
        height: Altura do ambiente
        dt: Delta de tempo
    """
    # Tratar colisões com os limites
    for cell in cells:
        if not cell.is_alive:
            continue
        
        radius = cell.get_radius()
        
        # Colisão com as bordas horizontais
        if cell.position.x - radius < 0:
            cell.position.x = radius
            cell.velocity.x = abs(cell.velocity.x) * 0.8  # Inverter e reduzir velocidade
        elif cell.position.x + radius > width:
            cell.position.x = width - radius
            cell.velocity.x = -abs(cell.velocity.x) * 0.8
        
        # Colisão com as bordas verticais
        if cell.position.y - radius < 0:
            cell.position.y = radius
            cell.velocity.y = abs(cell.velocity.y) * 0.8
        elif cell.position.y + radius > height:
            cell.position.y = height - radius
            cell.velocity.y = -abs(cell.velocity.y) * 0.8
    
    # Tratar colisões entre células (abordagem simplificada)
    for i, cell1 in enumerate(cells):
        if not cell1.is_alive:
            continue
            
        for j in range(i + 1, len(cells)):
            cell2 = cells[j]
            if not cell2.is_alive:
                continue
                
            # Calcular distância entre células
            dx = cell2.position.x - cell1.position.x
            dy = cell2.position.y - cell1.position.y
            distance = math.sqrt(dx * dx + dy * dy)
            
            # Distância mínima entre células (soma dos raios)
            min_distance = cell1.get_radius() + cell2.get_radius()
            
            # Se há colisão
            if distance < min_distance:
                # Evitar divisão por zero
                if distance < 0.1:
                    dx = 0.1
                    dy = 0.1
                    distance = 0.14142  # sqrt(0.1^2 + 0.1^2)
                
                # Normalizar vetor de direção
                dx /= distance
                dy /= distance
                
                # Calcular sobreposição
                overlap = min_distance - distance
                
                # Calcular massa relativa (baseada no raio)
                cell1_radius = cell1.get_radius()
                cell2_radius = cell2.get_radius()
                total_radius = cell1_radius + cell2_radius
                cell1_mass_ratio = cell1_radius / total_radius if total_radius > 0 else 0.5
                cell2_mass_ratio = cell2_radius / total_radius if total_radius > 0 else 0.5
                
                # Aplicar separação
                cell1.position.x -= dx * overlap * cell2_mass_ratio * 0.5
                cell1.position.y -= dy * overlap * cell2_mass_ratio * 0.5
                cell2.position.x += dx * overlap * cell1_mass_ratio * 0.5
                cell2.position.y += dy * overlap * cell1_mass_ratio * 0.5
                
                # Calcular velocidade relativa
                vx = cell2.velocity.x - cell1.velocity.x
                vy = cell2.velocity.y - cell1.velocity.y
                
                # Calcular produto escalar
                dot_product = dx * vx + dy * vy
                
                # Aplicar impulso apenas se as células estão se aproximando
                if dot_product < 0:
                    # Coeficiente de restituição (elasticidade da colisão)
                    restitution = 0.8
                    
                    # Calcular impulso
                    impulse = -(1 + restitution) * dot_product
                    impulse /= cell1_mass_ratio + cell2_mass_ratio
                    
                    # Aplicar impulso às velocidades
                    cell1.velocity.x -= impulse * dx * cell2_mass_ratio
                    cell1.velocity.y -= impulse * dy * cell2_mass_ratio
                    cell2.velocity.x += impulse * dx * cell1_mass_ratio
                    cell2.velocity.y += impulse * dy * cell1_mass_ratio


def calculate_cell_shape(cell: Cell, nearby_cells: List[Cell], bounds: Tuple[int, int]) -> List[Point]:
    """
    Calcula a forma deformada da célula com base nas células próximas e limites.
    
    Args:
        cell: Célula a ser deformada
        nearby_cells: Lista de células próximas
        bounds: Limites do ambiente (width, height)
    
    Returns:
        Lista de pontos que definem a forma da célula
    """
    # Atualizar a forma da célula
    cell.update_shape(nearby_cells, bounds)
    
    # Obter a forma atual
    return cell.get_actual_shape()


def apply_random_forces(cells: List[Cell], magnitude: float = 0.01) -> None:
    """
    Aplica forças aleatórias às células para simular movimento browniano.
    
    Args:
        cells: Lista de células
        magnitude: Magnitude das forças aleatórias
    """
    for cell in cells:
        if not cell.is_alive:
            continue
            
        # Aplicar força aleatória
        force_x = (2 * np.random.random() - 1) * magnitude
        force_y = (2 * np.random.random() - 1) * magnitude
        
        cell.velocity.x += force_x
        cell.velocity.y += force_y
