"""
Módulo para o ambiente de simulação das células.
"""
import random
from typing import Dict, List, Optional, Tuple, Union

import numpy as np

from ..models.cell import Cell, Point


class Environment:
    """
    Ambiente de simulação para as células.
    """
    def __init__(self, width: int = 800, height: int = 600, 
                 initial_cells: int = 5, nutrient_level: float = 1.0,
                 simulation_speed: float = 1.0):
        self.width = width
        self.height = height
        self.cells = []
        self.nutrient_level = nutrient_level  # 0.0 a 2.0
        self.simulation_speed = simulation_speed  # 0.1 a 5.0
        self.time = 0.0
        self.statistics = {
            "population": [],
            "avg_energy": [],
            "avg_size": [],
            "time": []
        }
        
        # Inicializar células
        self.initialize_cells(initial_cells)
        
        # Grade espacial para otimização de colisões
        self.grid_cell_size = 50
        self.spatial_grid = {}
    
    def initialize_cells(self, count: int) -> None:
        """
        Inicializa o ambiente com um número específico de células.
        
        Args:
            count: Número de células iniciais
        """
        for _ in range(count):
            position = Point(
                random.uniform(50, self.width - 50),
                random.uniform(50, self.height - 50)
            )
            cell = Cell(position=position)
            self.cells.append(cell)
    
    def update_spatial_grid(self) -> None:
        """Atualiza a grade espacial para otimização de colisões."""
        self.spatial_grid = {}
        
        for cell in self.cells:
            if not cell.is_alive:
                continue
                
            # Calcular a célula da grade
            grid_x = int(cell.position.x / self.grid_cell_size)
            grid_y = int(cell.position.y / self.grid_cell_size)
            
            # Adicionar a célula à grade
            grid_key = (grid_x, grid_y)
            if grid_key not in self.spatial_grid:
                self.spatial_grid[grid_key] = []
            self.spatial_grid[grid_key].append(cell)
    
    def get_nearby_cells(self, cell: Cell, radius: float) -> List[Cell]:
        """
        Obtém células próximas a uma célula específica.
        
        Args:
            cell: Célula de referência
            radius: Raio de busca
        
        Returns:
            Lista de células próximas
        """
        nearby_cells = []
        
        # Calcular as células da grade a verificar
        grid_radius = int(radius / self.grid_cell_size) + 1
        center_x = int(cell.position.x / self.grid_cell_size)
        center_y = int(cell.position.y / self.grid_cell_size)
        
        # Verificar células nas células da grade próximas
        for dx in range(-grid_radius, grid_radius + 1):
            for dy in range(-grid_radius, grid_radius + 1):
                grid_key = (center_x + dx, center_y + dy)
                
                if grid_key in self.spatial_grid:
                    for other_cell in self.spatial_grid[grid_key]:
                        if other_cell.id != cell.id and other_cell.is_alive:
                            distance = cell.position.distance_to(other_cell.position)
                            if distance <= radius:
                                nearby_cells.append(other_cell)
        
        return nearby_cells
    
    def update(self, dt: float = 1.0) -> None:
        """
        Atualiza o estado do ambiente e todas as células.
        
        Args:
            dt: Delta de tempo
        """
        # Aplicar velocidade de simulação
        effective_dt = dt * self.simulation_speed
        self.time += effective_dt
        
        # Atualizar grade espacial
        self.update_spatial_grid()
        
        # Lista para novas células criadas por divisão
        new_cells = []
        
        # Atualizar cada célula
        for cell in self.cells:
            if not cell.is_alive:
                continue
                
            # Obter células próximas
            nearby_cells = self.get_nearby_cells(cell, cell.get_radius() * 3)
            
            # Atualizar forma da célula
            cell.update_shape(nearby_cells, (self.width, self.height))
            
            # Atualizar estado da célula
            cell.update(self.nutrient_level, effective_dt)
            
            # Verificar divisão celular
            new_cell = cell.check_division()
            if new_cell:
                new_cells.append(new_cell)
            
            # Verificar colisões com os limites do ambiente
            self.handle_boundary_collision(cell)
            
            # Verificar colisões com outras células
            self.handle_cell_collisions(cell, nearby_cells)
        
        # Adicionar novas células
        self.cells.extend(new_cells)
        
        # Remover células mortas
        self.cells = [cell for cell in self.cells if cell.is_alive]
        
        # Atualizar estatísticas
        self.update_statistics()
    
    def handle_boundary_collision(self, cell: Cell) -> None:
        """
        Trata colisões da célula com os limites do ambiente.
        
        Args:
            cell: Célula a verificar
        """
        radius = cell.get_radius()
        
        # Colisão com as bordas horizontais
        if cell.position.x - radius < 0:
            cell.position.x = radius
            cell.velocity.x = abs(cell.velocity.x) * 0.8  # Inverter e reduzir velocidade
        elif cell.position.x + radius > self.width:
            cell.position.x = self.width - radius
            cell.velocity.x = -abs(cell.velocity.x) * 0.8
        
        # Colisão com as bordas verticais
        if cell.position.y - radius < 0:
            cell.position.y = radius
            cell.velocity.y = abs(cell.velocity.y) * 0.8
        elif cell.position.y + radius > self.height:
            cell.position.y = self.height - radius
            cell.velocity.y = -abs(cell.velocity.y) * 0.8
    
    def handle_cell_collisions(self, cell: Cell, nearby_cells: List[Cell]) -> None:
        """
        Trata colisões entre células.
        
        Args:
            cell: Célula a verificar
            nearby_cells: Lista de células próximas
        """
        for other in nearby_cells:
            if other.id == cell.id or not other.is_alive:
                continue
            
            # Calcular distância entre células
            distance = cell.position.distance_to(other.position)
            min_distance = cell.get_radius() + other.get_radius()
            
            # Se há colisão
            if distance < min_distance:
                # Calcular vetor de separação
                overlap = min_distance - distance
                direction = Point(
                    cell.position.x - other.position.x,
                    cell.position.y - other.position.y
                )
                
                # Normalizar direção
                if distance > 0:
                    direction = Point(
                        direction.x / distance,
                        direction.y / distance
                    )
                else:
                    # Evitar divisão por zero
                    direction = Point(
                        random.uniform(-1, 1),
                        random.uniform(-1, 1)
                    )
                
                # Calcular massa relativa (baseada no raio)
                cell_radius = cell.get_radius()
                other_radius = other.get_radius()
                total_radius = cell_radius + other_radius
                cell_mass_ratio = cell_radius / total_radius if total_radius > 0 else 0.5
                other_mass_ratio = other_radius / total_radius if total_radius > 0 else 0.5
                
                # Aplicar separação
                cell.position.x += direction.x * overlap * other_mass_ratio * 0.5
                cell.position.y += direction.y * overlap * other_mass_ratio * 0.5
                
                # Atualizar velocidades (colisão elástica simplificada)
                cell.velocity.x += direction.x * 0.05
                cell.velocity.y += direction.y * 0.05
                
                # Aplicar atrito para evitar movimento perpétuo
                cell.velocity.x *= 0.98
                cell.velocity.y *= 0.98
    
    def update_statistics(self) -> None:
        """Atualiza as estatísticas da simulação."""
        if not self.cells:
            return
            
        # População
        population = len(self.cells)
        
        # Energia média
        total_energy = sum(cell.energy for cell in self.cells)
        avg_energy = total_energy / population if population > 0 else 0
        
        # Tamanho médio
        total_size = sum(cell.get_radius() for cell in self.cells)
        avg_size = total_size / population if population > 0 else 0
        
        # Registrar estatísticas
        self.statistics["population"].append(population)
        self.statistics["avg_energy"].append(avg_energy)
        self.statistics["avg_size"].append(avg_size)
        self.statistics["time"].append(self.time)
        
        # Limitar o tamanho dos dados estatísticos
        max_stats = 1000
        if len(self.statistics["time"]) > max_stats:
            for key in self.statistics:
                self.statistics[key] = self.statistics[key][-max_stats:]
    
    def set_nutrient_level(self, level: float) -> None:
        """
        Define o nível de nutrientes no ambiente.
        
        Args:
            level: Nível de nutrientes (0.0 a 2.0)
        """
        self.nutrient_level = max(0.0, min(2.0, level))
    
    def set_simulation_speed(self, speed: float) -> None:
        """
        Define a velocidade da simulação.
        
        Args:
            speed: Velocidade da simulação (0.1 a 5.0)
        """
        self.simulation_speed = max(0.1, min(5.0, speed))
    
    def add_cell(self, position: Point) -> None:
        """
        Adiciona uma nova célula na posição especificada.
        
        Args:
            position: Posição da nova célula
        """
        cell = Cell(position=position)
        self.cells.append(cell)
    
    def get_cell_at(self, x: float, y: float, radius: float = 10.0) -> Optional[Cell]:
        """
        Obtém a célula na posição especificada, se houver.
        
        Args:
            x: Coordenada X
            y: Coordenada Y
            radius: Raio de busca
        
        Returns:
            Célula encontrada ou None
        """
        position = Point(x, y)
        
        for cell in self.cells:
            if cell.is_alive and cell.position.distance_to(position) <= radius:
                return cell
        
        return None
    
    def to_dict(self) -> Dict:
        """Converte o ambiente para um dicionário."""
        return {
            "width": self.width,
            "height": self.height,
            "nutrient_level": self.nutrient_level,
            "simulation_speed": self.simulation_speed,
            "time": self.time,
            "cells": [cell.to_dict() for cell in self.cells],
            "statistics": self.statistics
        }
    
    @classmethod
    def from_dict(cls, data: Dict) -> 'Environment':
        """Cria um ambiente a partir de um dicionário."""
        env = cls(
            width=data["width"],
            height=data["height"],
            initial_cells=0,  # Não inicializar células
            nutrient_level=data["nutrient_level"],
            simulation_speed=data["simulation_speed"]
        )
        
        env.time = data["time"]
        env.statistics = data["statistics"]
        
        # Carregar células
        for cell_data in data["cells"]:
            env.cells.append(Cell.from_dict(cell_data))
        
        return env
