import pygame
import sys
import random
import os
import math

pygame.init()

largura = 800
altura = 600
tela = pygame.display.set_mode((largura, altura), pygame.DOUBLEBUF | pygame.HWSURFACE)
pygame.display.set_caption("teste")

#cores
branco = (255, 255, 255)
preto = (0, 0, 0)
cores = [(255, 0, 0), (0, 255, 0), (0, 0, 255), (255, 255, 0), (255, 0, 255), (0, 255, 255)]

bloco_w =  30
bloco_h =  30
velocidade_inicial = 2
velocidade_minima = 0.1
velocidade_maxima = 10
quantidade_maxima = 30
mult_velocidade = 1.1
velocidade_atual = velocidade_inicial * mult_velocidade

obstaculo_w = 60
obstaculo_h = 60

font = pygame.font.SysFont("minecraft", 20)

class Bloco(pygame.sprite.Sprite):
    def __init__(self, x, y, velocidade_x, velocidade_y, cor, largura, altura):
        super().__init__()
        self.image = pygame.Surface((largura, altura))
        self.image.fill(cor)
        self.rect = self.image.get_rect(topleft=(x, y))
        self.velocidade_x = velocidade_x
        self.velocidade_y = velocidade_y
        self.id = id(self)
        self.cor = cor
        self.borda_cor = preto
        self.borda_largura = 2
        self.last_update = pygame.time.get_ticks()
    
    def update(self):
        now = pygame.time.get_ticks()
        delta_time = (now - self.last_update) / 1000.0
        self.last_update = now

        if self.velocidade_x == 0:
            self.velocidade_x = random.choice([-0.2, 0.2])
        if self.velocidade_y == 0:
            self.velocidade_y = random.choice([-0.2, 0.2])

        self.rect.x += self.velocidade_x * delta_time * 60 
        self.rect.y += self.velocidade_y * delta_time * 60

        if self.rect.left < 0 or self.rect.right > largura:
            self.velocidade_x *= -1
        if self.rect.top < 0 or self.rect.bottom > altura:
            self.velocidade_y *= -1

        self.rect.left = max(self.rect.left, 0)
        self.rect.right = min(self.rect.right, largura)
        self.rect.top = max(self.rect.top, 0)
        self.rect.bottom = min(self.rect.bottom, altura)

        self.limitar_bordas()

    
    def mudar_velocidade(self):
        self.velocidade_x *= mult_velocidade
        self.velocidade_y *= mult_velocidade

        if abs(self.velocidade_x) > velocidade_maxima:
            self.velocidade_x = velocidade_maxima if self.velocidade_x > 0 else -velocidade_maxima
        if abs(self.velocidade_y) > velocidade_maxima:
            self.velocidade_y = velocidade_maxima if self.velocidade_y > 0 else -velocidade_maxima
        if abs(self.velocidade_x) < velocidade_minima:
            self.velocidade_x = velocidade_minima if self.velocidade_x > 0 else -velocidade_minima
        if abs(self.velocidade_y) < velocidade_minima:
            self.velocidade_y = velocidade_minima if self.velocidade_y > 0 else -velocidade_minima        
        
        self.velocidade_x = max(min(self.velocidade_x, velocidade_maxima), -velocidade_maxima)
        self.velocidade_y = max(min(self.velocidade_y, velocidade_maxima), -velocidade_maxima)

    def draw(self, tela):
        pygame.draw.rect(tela, self.cor, self.rect)
        pygame.draw.rect(tela, self.borda_cor, self.rect, self.borda_largura)
    
    def limitar_bordas(self):
        self.rect.left = max(self.rect.left, 0)
        self.rect.right = min(self.rect.right, largura)
        self.rect.top = max(self.rect.top, 0)
        self.rect.bottom = min(self.rect.bottom, altura)

def tratar_colisao(bloco, obstaculo):
    overlap_left = bloco.rect.right - obstaculo.left
    overlap_right = obstaculo.right - bloco.rect.left
    overlap_top = bloco.rect.bottom - obstaculo.top
    overlap_bottom = obstaculo.bottom - bloco.rect.top

    min_overlap = min(overlap_left, overlap_right, overlap_top, overlap_bottom)
                
    if min_overlap == overlap_left:
        bloco.rect.right = obstaculo.left
        bloco.velocidade_x *= -1
    elif min_overlap == overlap_right:
        bloco.rect.left = obstaculo.right
        bloco.velocidade_x *= -1
    elif min_overlap == overlap_top:
        bloco.rect.bottom = obstaculo.top
        bloco.velocidade_y *= -1
    elif min_overlap == overlap_bottom:
        bloco.rect.top = obstaculo.bottom
        bloco.velocidade_y *= -1

def mudar_cor(bloco):
    bloco.cor = random.choice(cores)
    bloco.image.fill(bloco.cor)

def criar_blocos(quantidade):
    blocos = pygame.sprite.Group()
    for _ in range(quantidade):
        x = random.randint(0, largura - bloco_w)
        y = random.randint(0, altura - bloco_h)
        velocidade_x = random.choice([-velocidade_atual, velocidade_atual])
        velocidade_y = random.choice([-velocidade_atual, velocidade_atual ])
        cor = random.choice(cores)
        bloco = Bloco(x, y, velocidade_x, velocidade_y, cor, bloco_w, bloco_h)
        blocos.add(bloco)
    return blocos

def resetar_blocos():
    blocos = criar_blocos(1)
    quantidade = len(blocos)
    return blocos, quantidade

def mudar_quantidade(blocos, quantidade):
    nova_quantidade = quantidade + 1
    if nova_quantidade > quantidade_maxima:
        nova_quantidade = quantidade_maxima
    
    if len(blocos) < quantidade_maxima:
        novos_blocos = criar_blocos(nova_quantidade - len(blocos))
        blocos.add(novos_blocos)
    
    return blocos, nova_quantidade

def tratar_eventos(event, blocos, quantidade, pausando):
    global mult_velocidade, velocidade_atual
    if event.type == pygame.KEYDOWN:
        if event.key == pygame.K_ESCAPE:
            return False, pausando
        elif event.key == pygame.K_p:
            pausando = not pausando  # Use 'pausando' instead of 'pausado'
        elif event.key == pygame.K_SPACE:
            blocos, quantidade = mudar_quantidade(blocos, quantidade)
        elif event.key == pygame.K_r:
            blocos, quantidade = resetar_blocos()
        elif event.key == pygame.K_EQUALS or event.key == pygame.K_KP_EQUALS:
            mult_velocidade = 1.1
            velocidade_atual *= mult_velocidade
            for bloco in blocos:
                bloco.mudar_velocidade()
        elif event.key == pygame.K_MINUS or event.key == pygame.K_KP_MINUS:
            mult_velocidade = 0.9
            velocidade_atual *= mult_velocidade
            for bloco in blocos:
                bloco.mudar_velocidade()
    return True, pausando

def redenrizar_tela(tela, blocos, obstaculo, clock, pausado):
    tela.fill(branco)
    for bloco in blocos:
        bloco.draw(tela)
    pygame.draw.rect(tela, preto, obstaculo, width=5)
    text_surface = font.render(f"Blocks: {len(blocos)}", True, preto)
    text_rect = font.render(f"Speed X: {round(abs(bloco.velocidade_x), 1)} Speed Y: {round(abs(bloco.velocidade_y), 1)}", True, preto)
    tela.blit(text_rect, (7, 27))
    tela.blit(text_surface, (7, 7))
    fps_text = font.render(f"FPS: {int(clock.get_fps())}", True, preto)
    tela.blit(fps_text, (largura - 100, 7))
    if pausado:
        pause_surface = font.render("PAUSED", True, preto)
        pause_rect = pause_surface.get_rect(center=(largura // 2, altura // 2))
        tela.blit(pause_surface, pause_rect)
    pygame.display.flip()


def main():
    global mult_velocidade, velocidade_atual
    blocos = criar_blocos(1)
    quantidade = len(blocos)
    clock = pygame.time.Clock()
    running = True
    pausado = False

    pygame.key.set_repeat(200, 100)
    pygame.event.set_allowed([pygame.QUIT, pygame.KEYDOWN, pygame.KEYUP])
    
    while running:
        pygame.event.pump()
        for event in pygame.event.get():
            if event.type == pygame.QUIT:
                running = False
                pygame.quit()
                sys.exit()
            running, pausado = tratar_eventos(event, blocos, quantidade, pausado)
                    
        mouse_x, mouse_y = pygame.mouse.get_pos()
        obstaculo_x = (mouse_x - obstaculo_w // 2)
        obstaculo_y = (mouse_y - obstaculo_h // 2)
        obstaculo = pygame.Rect(obstaculo_x, obstaculo_y, obstaculo_w, obstaculo_h)
    
        if not pausado:
            blocos.update()

        #colisão#
        for bloco in blocos:
            if bloco.rect.colliderect(obstaculo):
                tratar_colisao(bloco, obstaculo)

        redenrizar_tela(tela, blocos, obstaculo, clock, pausado)
        clock.tick(60)

if __name__ == "__main__":
    main()