import pygame
import sys
import random
from itertools import combinations

pygame.init()

# tela #
largura = 800
altura = 600
screen = pygame.display.set_mode((largura, altura))
pygame.display.set_caption("teste2")


# cores #
branco = (255, 255, 255)
preto = (0, 0, 0)
cores = [(255, 0, 0), (0, 255, 0), (0, 0, 255), (255, 255, 0), (255, 0, 255), (0, 255, 255)]

# configuração #
quantidade_maxima = 30

# objeto #
bloco_w = 30
bloco_h = 30
max_speed = 20
min_speed = 5

# text #
font = pygame.font.SysFont("minecraft", 20)

class Quadrado(pygame.sprite.Sprite):
    def __init__(self, x, y, speed_x, speed_y, cor, altura, largura):
        super().__init__()
        self.image = pygame.Surface((altura, largura))
        self.image.fill(cor)
        self.rect = self.image.get_rect(topleft=(x, y))
        self.speed_x = speed_x
        self.speed_y = speed_y
        self.cor = cor
        self.borda_cor = preto
        self.borda_lagura = 2
        self.last_valid_position = self.rect.copy()
    
    def update(self):
        self.last_valid_position = self.rect.copy()

        self.rect.x += self.speed_x
        self.rect.y += self.speed_y

        if self.rect.left < 0 or self.rect.right > largura:
            self.rect.left = max(self.rect.left, 0)
            self.rect.right = min(self.rect.right, largura)
        if self.rect.top < 0 or self.rect.bottom > altura:
            self.rect.top = max(self.rect.top, 0)
            self.rect.bottom = min(self.rect.bottom, altura)
        if self.borda_lagura > 0:
            pygame.draw.rect(screen, self.borda_cor, self.rect, self.borda_lagura)
    
    def draw(self, screen):
        pygame.draw.rect(screen, self.cor, self.rect)
        pygame.draw.rect(screen, self.borda_cor, self.rect, self.borda_lagura)
    

# quantidade de objetos #
def criar_blocos(quantidade):
    blocos = pygame.sprite.Group()
    for _ in range(quantidade):
        x = random.randint(0, largura - bloco_w)
        y = random.randint(0, altura - bloco_h)
        cor = random.choice(cores)
        speed_x = 0
        speed_y = 0
        bloco = Quadrado(x, y, speed_x, speed_y, cor, bloco_w, bloco_h)
        blocos.add(bloco)
    return blocos

def mudar_quantidade(blocos, quantidade):
    nova_quantidade = quantidade + 1
    if nova_quantidade > quantidade_maxima:
        nova_quantidade = quantidade_maxima
    
    if len(blocos) < quantidade_maxima:
        novos_blocos = criar_blocos(nova_quantidade - len(blocos))
        blocos.add(novos_blocos)
    
    return blocos, nova_quantidade

def reset_quantidade():
    blocos = criar_blocos(1)
    quantidade = len(blocos)
    return blocos, quantidade

def handle_collision(blocos):
    lista_blocos = list(blocos)
    colididos = set()
    for bloco1, bloco2 in combinations(lista_blocos, 2):
        if bloco1.rect.colliderect(bloco2.rect):
            resolver_colission(bloco1, bloco2)
            colididos.add(bloco1)
            colididos.add(bloco2)
    for bloco in blocos:
        if bloco not in colididos:
            bloco.last_valid_position = bloco.rect.copy()

def resolver_colission(bloco1, bloco2):
    bloco1.rect = bloco1.last_valid_position.copy()
    bloco2.rect = bloco2.last_valid_position.copy()

    dx = bloco2.rect.centerx - bloco1.rect.centerx
    dy = bloco2.rect.centery - bloco1.rect.centery

    if abs(dx) < abs(dy):
        if dx > 0:
            bloco1.speed_x = -abs(bloco1.speed_x)
            bloco2.speed_x = abs(bloco2.speed_x)
        else:
            bloco1.speed_x = abs(bloco1.speed_x)
            bloco2.speed_x = -abs(bloco2.speed_x)
    else:
        if dy > 0:
            bloco1.speed_y = -abs(bloco1.speed_y)
            bloco2.speed_y = abs(bloco2.speed_y)
        else:
            bloco1.speed_y = abs(bloco1.speed_y)
            bloco2.speed_y = -abs(bloco2.speed_y)
    
    separation_force = 0.5
    if abs(dx) > abs(dy):
        if dx > 0:
            bloco1.rect.right = bloco2.rect.left - separation_force
        else:
            bloco1.rect.left = bloco2.rect.right + separation_force
    else:
        if dy > 0:
            bloco1.rect.bottom = bloco2.rect.top - separation_force
        else:
            bloco1.rect.top = bloco2.rect.bottom + separation_force


# dicionario dos estados das teclas #

def main():
    blocos = criar_blocos(1)
    quantidade = len(blocos)
    clock = pygame.time.Clock()
    
    keys = {
        pygame.K_d: False,
        pygame.K_a: False,
        pygame.K_w: False,
        pygame.K_s: False
    }

    running = True
    # loop principal #
    while running:
        for event in pygame.event.get():
            if event.type == pygame.QUIT:
                running = False
            
            # control #
            if event.type == pygame.KEYDOWN:
                if event.key == pygame.K_ESCAPE:
                    running = False
                elif event.key == pygame.K_SPACE and len(blocos) < quantidade_maxima:
                    blocos, quantidade = mudar_quantidade(blocos, quantidade)
                elif event.key == pygame.K_r:
                    blocos, quantidade = reset_quantidade()
            
            if event.type == pygame.KEYDOWN:
                if event.key in keys:
                    keys[event.key] = True
            if event.type == pygame.KEYUP:
                if event.key in keys:
                    keys[event.key] = False
            
            # horizontal dislocation #
            if keys[pygame.K_a] and keys[pygame.K_d]:
                speed_x = 0
            elif keys[pygame.K_a]:
                speed_x = -min_speed
            elif keys[pygame.K_d]:
                speed_x = min_speed
            else:
                speed_x = 0
            # vertical dislocation #
            if keys[pygame.K_w] and keys[pygame.K_s]:
                speed_y = 0
            elif keys[pygame.K_w]:
                speed_y = -min_speed
            elif keys[pygame.K_s]:
                speed_y = min_speed
            else:
                speed_y = 0
            for bloco in blocos:
                bloco.speed_x = speed_x
                bloco.speed_y = speed_y

        for bloco in blocos:
            bloco.update()

        handle_collision(blocos)
                
        screen.fill(branco)
        for bloco in blocos:
            bloco.draw(screen)
        text_surface = font.render(f"Blocks: {len(blocos)}", True, preto)
        screen.blit(text_surface, (7, 7))
        pygame.display.flip()
        clock.tick(60)
    
    pygame.quit()
    sys.exit()

if __name__ == "__main__":
    main()