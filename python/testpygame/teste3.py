import pygame 
import sys
import random

pygame.init()

# screen config
width = 800
height = 600
screen = pygame.display.set_mode((width, height))
pygame.display.set_caption("teste3")

# object config
min_speed = 5
speed_x = 0
speed_y = 0
max_speed = 20

# cores #
branco = (255, 255, 255)
preto = (0, 0, 0)
cores = [(255, 0, 0), (0, 255, 0), (0, 0, 255), (255, 255, 0), (255, 0, 255), (0, 255, 255)]

font = pygame.font.SysFont("minecraft", 25)
text_surface = font.render("teste", True, preto)
text_rect = text_surface.get_rect()

keys = {
    pygame.K_w: False,
    pygame.K_a: False,
    pygame.K_s: False,
    pygame.K_d: False,
}

def main():
    global speed_x, speed_y
    clock = pygame.time.Clock()
    
    running = True

    while running:
        for event in pygame.event.get():
            if event.type == pygame.QUIT:
                running = False
            
            if event.type == pygame.KEYDOWN:
                if event.key == pygame.K_ESCAPE:
                    running = False
            
            if event.type == pygame.KEYDOWN:
                if event.key in keys:
                    keys[event.key] = True
            if event.type == pygame.KEYUP:
                if event.key in keys:
                    keys[event.key] = False
        # horizontal dislocation #
        if keys[pygame.K_a] and keys[pygame.K_d]:
            speed_x = 0
        elif keys[pygame.K_a]:
            speed_x = -min_speed
        elif keys[pygame.K_d]:
            speed_x = min_speed
        else:
            speed_x = 0
        
        # vertical dislocation #
        if keys[pygame.K_w] and keys[pygame.K_s]:
            speed_y = 0
        elif keys[pygame.K_w]:
            speed_y = -min_speed
        elif keys[pygame.K_s]:
            speed_y = min_speed
        else:
            speed_y = 0
        

        screen.fill(branco)
        screen.blit(text_surface, (50, 50))

        pygame.display.flip()
        clock.tick(60)
    pygame.quit()
    sys.exit()
    
if __name__ == "__main__":
    main()