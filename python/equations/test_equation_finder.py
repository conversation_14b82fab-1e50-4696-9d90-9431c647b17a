#!/usr/bin/env python3
"""
Teste do buscador de equações com exemplos predefinidos
"""

from equation_finder import EquationFinder

def test_linear_equation():
    """Testa equação linear: f(x) = 2x + 1"""
    print("=== TESTE 1: Equação Linear f(x) = 2x + 1 ===")
    
    finder = EquationFinder()
    
    # Dados para f(x) = 2x + 1
    inputs = [[1], [2], [3], [4], [5]]
    outputs = [3, 5, 7, 9, 11]
    
    equation, found = finder.find_equation(inputs, outputs)
    
    if found:
        finder.verify_equation(equation, inputs, outputs)
    
    print("\n" + "="*50 + "\n")

def test_quadratic_equation():
    """Testa equação quadrática: f(x) = x²"""
    print("=== TESTE 2: Equação Quadrática f(x) = x² ===")
    
    finder = EquationFinder()
    
    # Dados para f(x) = x²
    inputs = [[1], [2], [3], [4]]
    outputs = [1, 4, 9, 16]
    
    equation, found = finder.find_equation(inputs, outputs)
    
    if found:
        finder.verify_equation(equation, inputs, outputs)
    
    print("\n" + "="*50 + "\n")

def test_two_variables():
    """Testa equação com duas variáveis: f(x,y) = x + y"""
    print("=== TESTE 3: Duas Variáveis f(x,y) = x + y ===")
    
    finder = EquationFinder()
    
    # Dados para f(x,y) = x + y
    inputs = [[1, 2], [3, 4], [5, 6], [2, 3]]
    outputs = [3, 7, 11, 5]
    
    equation, found = finder.find_equation(inputs, outputs)
    
    if found:
        finder.verify_equation(equation, inputs, outputs)
    
    print("\n" + "="*50 + "\n")

def test_memory_system():
    """Testa o sistema de memória"""
    print("=== TESTE 4: Sistema de Memória ===")
    
    finder = EquationFinder()
    
    # Primeira execução
    inputs = [[1], [2], [3]]
    outputs = [2, 4, 6]
    
    print("Primeira execução:")
    equation1, found1 = finder.find_equation(inputs, outputs)
    attempts1 = finder.attempts
    
    # Segunda execução com os mesmos dados
    print("\nSegunda execução (deve usar memória):")
    finder2 = EquationFinder()
    equation2, found2 = finder2.find_equation(inputs, outputs)
    attempts2 = finder2.attempts
    
    print(f"\nComparação:")
    print(f"Primeira execução: {attempts1} tentativas")
    print(f"Segunda execução: {attempts2} tentativas (deve ser 0 se usou memória)")
    
    print("\n" + "="*50 + "\n")

def interactive_test():
    """Teste interativo onde o usuário pode inserir dados"""
    print("=== TESTE INTERATIVO ===")
    print("Digite seus próprios dados para testar!")
    
    finder = EquationFinder()
    
    try:
        # Pergunta quantas variáveis
        num_vars = int(input("Quantas variáveis de entrada? (1 ou 2): "))
        num_points = int(input("Quantos pontos de dados? "))
        
        inputs = []
        outputs = []
        
        for i in range(num_points):
            if num_vars == 1:
                x = float(input(f"Ponto {i+1} - x: "))
                inputs.append([x])
            else:
                x = float(input(f"Ponto {i+1} - x: "))
                y = float(input(f"Ponto {i+1} - y: "))
                inputs.append([x, y])
            
            output = float(input(f"Ponto {i+1} - saída: "))
            outputs.append(output)
        
        equation, found = finder.find_equation(inputs, outputs)
        
        if found:
            finder.verify_equation(equation, inputs, outputs)
            
            # Permite testar novos valores
            print("\nQuer testar a equação com novos valores? (s/n)")
            if input().lower() == 's':
                while True:
                    try:
                        if num_vars == 1:
                            test_x = float(input("Digite x para testar (ou 'q' para sair): "))
                            variables = {'x': test_x}
                            result = finder.evaluate_equation(equation, variables)
                            print(f"f({test_x}) = {result}")
                        else:
                            test_input = input("Digite x,y para testar (ou 'q' para sair): ")
                            if test_input.lower() == 'q':
                                break
                            x, y = map(float, test_input.split(','))
                            variables = {'x': x, 'y': y}
                            result = finder.evaluate_equation(equation, variables)
                            print(f"f({x}, {y}) = {result}")
                    except:
                        break
        
    except Exception as e:
        print(f"Erro no teste interativo: {e}")

def main():
    """Executa todos os testes"""
    print("🧮 TESTADOR DO BUSCADOR DE EQUAÇÕES")
    print("=" * 50)
    
    # Executa testes automáticos
    test_linear_equation()
    test_quadratic_equation()
    test_two_variables()
    test_memory_system()
    
    # Pergunta se quer fazer teste interativo
    print("Deseja fazer um teste interativo? (s/n)")
    if input().lower() == 's':
        interactive_test()
    
    print("Testes concluídos! ✅")

if __name__ == "__main__":
    main()
