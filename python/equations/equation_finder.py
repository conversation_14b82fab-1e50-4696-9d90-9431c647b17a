import json
import os
import math
from typing import List, Tuple, Dict

class EquationFinder:
    def __init__(self, memory_file="equation_memory.json"):
        """
        Inicializa o buscador de equações com sistema de memória
        """
        # Garante que o arquivo de memória seja salvo na pasta do script
        script_dir = os.path.dirname(os.path.abspath(__file__))
        self.memory_file = os.path.join(script_dir, memory_file)
        self.memory = self.load_memory()
        self.attempts = 0
        self.found_equation = None
        self.equation_string = ""
        
        # Operações matemáticas disponíveis
        self.operations = {
            '+': lambda x, y: x + y,
            '-': lambda x, y: x - y,
            '*': lambda x, y: x * y,
            '/': lambda x, y: x / y if y != 0 else float('inf'),
            '**': lambda x, y: x ** y if abs(y) < 10 else float('inf'),
            'sqrt': lambda x: math.sqrt(abs(x)),
            'log': lambda x: math.log(abs(x)) if x > 0 else float('inf'),
            'sin': lambda x: math.sin(x),
            'cos': lambda x: math.cos(x),
            'tan': lambda x: math.tan(x),
            'abs': lambda x: abs(x)
        }
        
    def load_memory(self) -> Dict:
        """Carrega a memória de equações já testadas"""
        if os.path.exists(self.memory_file):
            try:
                with open(self.memory_file, 'r') as f:
                    return json.load(f)
            except:
                return {}
        return {}
    
    def save_memory(self):
        """Salva a memória no arquivo"""
        with open(self.memory_file, 'w') as f:
            json.dump(self.memory, f, indent=2)
    
    def create_data_key(self, inputs: List[float], outputs: List[float]) -> str:
        """Cria uma chave única para os dados de entrada e saída"""
        return f"{inputs}_{outputs}"
    
    def analyze_data_patterns(self, inputs: List[List[float]], outputs: List[float]) -> List[str]:
        """Analisa os dados para gerar equações candidatas inteligentemente"""
        equations = []
        num_vars = len(inputs[0]) if inputs else 1

        # Análise básica dos dados
        if num_vars == 1:
            x_values = [inp[0] for inp in inputs]

            # Testa se é linear: y = ax + b
            if len(x_values) >= 2:
                # Calcula possíveis coeficientes lineares
                for i in range(len(x_values) - 1):
                    for j in range(i + 1, len(x_values)):
                        if x_values[j] != x_values[i]:
                            a = (outputs[j] - outputs[i]) / (x_values[j] - x_values[i])
                            b = outputs[i] - a * x_values[i]

                            # Gera equações baseadas nos coeficientes calculados
                            if abs(b) < 1e-10:  # b ≈ 0
                                if abs(a - 1) < 1e-10:
                                    equations.append("x")
                                elif abs(a + 1) < 1e-10:
                                    equations.append("-x")
                                else:
                                    equations.append(f"{a}*x")
                            else:
                                if abs(a - 1) < 1e-10:
                                    equations.append(f"x + {b}" if b > 0 else f"x - {abs(b)}")
                                elif abs(a + 1) < 1e-10:
                                    equations.append(f"-x + {b}" if b > 0 else f"-x - {abs(b)}")
                                else:
                                    equations.append(f"{a}*x + {b}" if b > 0 else f"{a}*x - {abs(b)}")

            # Testa padrões quadráticos mais sofisticados
            if len(x_values) >= 3:
                # Tenta resolver sistema para ax² + bx + c
                try:
                    # Usa os três primeiros pontos para calcular a, b, c
                    x1, x2, x3 = x_values[0], x_values[1], x_values[2]
                    y1, y2, y3 = outputs[0], outputs[1], outputs[2]

                    # Sistema de equações lineares para ax² + bx + c
                    # y1 = a*x1² + b*x1 + c
                    # y2 = a*x2² + b*x2 + c
                    # y3 = a*x3² + b*x3 + c

                    det = x1*x1*(x2-x3) + x2*x2*(x3-x1) + x3*x3*(x1-x2)
                    if abs(det) > 1e-10:
                        a = (y1*(x2-x3) + y2*(x3-x1) + y3*(x1-x2)) / det
                        b = (y1*(x3*x3-x2*x2) + y2*(x1*x1-x3*x3) + y3*(x2*x2-x1*x1)) / det
                        c = (y1*(x2*x3*x3-x3*x2*x2) + y2*(x3*x1*x1-x1*x3*x3) + y3*(x1*x2*x2-x2*x1*x1)) / det

                        # Gera equações baseadas nos coeficientes
                        if abs(c) < 1e-10 and abs(b) < 1e-10:  # y = ax²
                            equations.append(f"{a}*x**2")
                        elif abs(c) < 1e-10:  # y = ax² + bx
                            equations.append(f"{a}*x**2 + {b}*x")
                        elif abs(b) < 1e-10:  # y = ax² + c
                            equations.append(f"{a}*x**2 + {c}")
                        else:  # y = ax² + bx + c
                            equations.append(f"{a}*x**2 + {b}*x + {c}")
                except:
                    pass

            # Testa padrões simples para cada ponto
            for x, y in zip(x_values, outputs):
                if x != 0:
                    # y = ax²
                    a = y / (x * x)
                    equations.append(f"{a}*x**2")

                    # y = ax² + c (para diferentes valores de c)
                    for c in [-5, -3, -2, -1, 1, 2, 3, 5]:
                        a_adj = (y - c) / (x * x) if x != 0 else 0
                        equations.append(f"{a_adj}*x**2 + {c}")

                    # y = ax³
                    if abs(x) > 1e-10:
                        a_cubic = y / (x * x * x)
                        equations.append(f"{a_cubic}*x**3")

        elif num_vars == 2:
            # Para duas variáveis, testa combinações básicas
            for inp, out in zip(inputs, outputs):
                x, y = inp[0], inp[1]

                # Testa se é soma/subtração simples
                if abs(x + y - out) < 1e-10:
                    equations.append("x + y")
                if abs(x - y - out) < 1e-10:
                    equations.append("x - y")
                if abs(y - x - out) < 1e-10:
                    equations.append("y - x")

                # Testa multiplicação/divisão
                if abs(x * y - out) < 1e-10:
                    equations.append("x * y")
                if y != 0 and abs(x / y - out) < 1e-10:
                    equations.append("x / y")
                if x != 0 and abs(y / x - out) < 1e-10:
                    equations.append("y / x")

        return list(set(equations))  # Remove duplicatas

    def generate_systematic_equations(self, num_vars: int) -> List[str]:
        """Gera equações de forma sistemática"""
        equations = []

        if num_vars == 1:
            # Constantes e lineares
            for a in range(-5, 6):
                if a != 0:
                    equations.append(f"{a}*x")
                for b in range(-5, 6):
                    if a == 0:
                        equations.append(str(b))
                    elif a == 1:
                        if b == 0:
                            equations.append("x")
                        elif b > 0:
                            equations.append(f"x + {b}")
                        else:
                            equations.append(f"x - {abs(b)}")
                    elif a == -1:
                        if b == 0:
                            equations.append("-x")
                        elif b > 0:
                            equations.append(f"-x + {b}")
                        else:
                            equations.append(f"-x - {abs(b)}")
                    else:
                        if b == 0:
                            equations.append(f"{a}*x")
                        elif b > 0:
                            equations.append(f"{a}*x + {b}")
                        else:
                            equations.append(f"{a}*x - {abs(b)}")

            # Quadráticas
            for a in [-3, -2, -1, 1, 2, 3]:
                equations.append(f"{a}*x**2")
                for b in [-2, -1, 1, 2]:
                    equations.append(f"{a}*x**2 + {b}*x")
                    for c in [-2, -1, 1, 2]:
                        equations.append(f"{a}*x**2 + {b}*x + {c}")

            # Outras funções
            equations.extend([
                "x**3", "sqrt(x)", "abs(x)",
                "sin(x)", "cos(x)", "tan(x)",
                "log(x)", "x**0.5", "1/x"
            ])

        elif num_vars == 2:
            # Combinações básicas
            equations.extend([
                "x + y", "x - y", "y - x", "x * y",
                "x / y", "y / x", "(x + y)/2"
            ])

            # Com coeficientes
            for a in [-3, -2, -1, 1, 2, 3]:
                for b in [-3, -2, -1, 1, 2, 3]:
                    equations.append(f"{a}*x + {b}*y")
                    equations.append(f"{a}*x * {b}*y")

            # Quadráticas
            equations.extend([
                "x**2 + y**2", "x**2 - y**2", "x**2 + y", "x + y**2",
                "x**2 * y", "x * y**2", "sqrt(x**2 + y**2)"
            ])

        return equations

    def generate_equations(self, inputs: List[List[float]], outputs: List[float]) -> List[str]:
        """Gera todas as equações candidatas"""
        num_vars = len(inputs[0]) if inputs else 1

        # Combina análise inteligente com geração sistemática
        smart_equations = self.analyze_data_patterns(inputs, outputs)
        systematic_equations = self.generate_systematic_equations(num_vars)

        # Prioriza equações inteligentes
        all_equations = smart_equations + systematic_equations

        # Remove duplicatas mantendo a ordem
        seen = set()
        unique_equations = []
        for eq in all_equations:
            if eq not in seen:
                seen.add(eq)
                unique_equations.append(eq)

        return unique_equations
    
    def evaluate_equation(self, equation_str: str, variables: Dict[str, float]) -> float:
        """Avalia uma equação com as variáveis fornecidas"""
        try:
            # Substitui funções matemáticas
            equation = equation_str.replace('sqrt', 'math.sqrt')
            equation = equation.replace('sin', 'math.sin')
            equation = equation.replace('cos', 'math.cos')
            equation = equation.replace('tan', 'math.tan')
            equation = equation.replace('log', 'math.log')
            equation = equation.replace('abs', 'abs')
            
            # Substitui variáveis
            for var, value in variables.items():
                equation = equation.replace(var, str(value))
            
            result = eval(equation)
            return float(result) if not math.isnan(result) and math.isfinite(result) else float('inf')
        except:
            return float('inf')
    
    def test_equation(self, equation_str: str, inputs: List[List[float]], outputs: List[float], tolerance: float = 1e-6) -> bool:
        """Testa se uma equação funciona para todos os dados"""
        self.attempts += 1
        
        for i, input_vals in enumerate(inputs):
            if len(input_vals) == 1:
                variables = {'x': input_vals[0]}
            elif len(input_vals) == 2:
                variables = {'x': input_vals[0], 'y': input_vals[1]}
            else:
                # Para mais variáveis, usa x1, x2, x3, etc.
                variables = {f'x{j+1}' if j > 0 else 'x': val for j, val in enumerate(input_vals)}
            
            result = self.evaluate_equation(equation_str, variables)
            
            if abs(result - outputs[i]) > tolerance:
                return False
        
        return True
    
    def find_equation(self, inputs: List[List[float]], outputs: List[float], max_attempts: int = 1000) -> Tuple[str, bool]:
        """
        Encontra uma equação que mapeia as entradas para as saídas
        """
        # Verifica se já temos essa combinação na memória
        data_key = self.create_data_key(inputs, outputs)
        if data_key in self.memory:
            print(f"Equação encontrada na memória: {self.memory[data_key]}")
            return self.memory[data_key], True
        
        self.attempts = 0
        num_vars = len(inputs[0]) if inputs else 1
        
        print(f"Procurando equação para {len(inputs)} pontos com {num_vars} variável(is)...")
        print(f"Dados de entrada: {inputs}")
        print(f"Dados de saída: {outputs}")
        print("-" * 50)
        
        # Gera equações candidatas de forma inteligente
        candidate_equations = self.generate_equations(inputs, outputs)

        print(f"Gerando {len(candidate_equations)} equações candidatas baseadas na análise dos dados...")
        
        # Testa cada equação
        for equation in candidate_equations:
            if self.attempts >= max_attempts:
                break
                
            if self.test_equation(equation, inputs, outputs):
                self.found_equation = equation
                self.equation_string = equation
                
                # Salva na memória
                self.memory[data_key] = equation
                self.save_memory()
                
                print(f"✅ Equação encontrada após {self.attempts} tentativas!")
                print(f"Equação: f(x) = {equation}")
                return equation, True
        
        print(f"❌ Nenhuma equação encontrada após {self.attempts} tentativas.")
        return None, False
    
    def verify_equation(self, equation: str, inputs: List[List[float]], outputs: List[float]) -> bool:
        """Verifica se a equação encontrada realmente funciona"""
        print("\n🔍 Verificando a equação encontrada:")
        print(f"Equação: f(x) = {equation}")
        print("-" * 30)
        
        all_correct = True
        for i, (input_vals, expected_output) in enumerate(zip(inputs, outputs)):
            if len(input_vals) == 1:
                variables = {'x': input_vals[0]}
                input_str = f"x = {input_vals[0]}"
            elif len(input_vals) == 2:
                variables = {'x': input_vals[0], 'y': input_vals[1]}
                input_str = f"x = {input_vals[0]}, y = {input_vals[1]}"
            else:
                variables = {f'x{j+1}' if j > 0 else 'x': val for j, val in enumerate(input_vals)}
                input_str = ", ".join([f"x{j+1 if j > 0 else ''} = {val}" for j, val in enumerate(input_vals)])
            
            calculated = self.evaluate_equation(equation, variables)
            is_correct = abs(calculated - expected_output) < 1e-6
            
            status = "✅" if is_correct else "❌"
            print(f"{status} {input_str} → Esperado: {expected_output}, Calculado: {calculated:.6f}")
            
            if not is_correct:
                all_correct = False
        
        print(f"\n{'✅ Verificação bem-sucedida!' if all_correct else '❌ Verificação falhou!'}")
        return all_correct

def main():
    """Função principal para demonstrar o uso"""
    finder = EquationFinder()
    
    print("=== BUSCADOR DE EQUAÇÕES ===")
    print("Digite os dados de entrada e saída para encontrar uma equação.")
    print("Exemplo: para f(x) = 2x + 1, use entradas [1,2,3] e saídas [3,5,7]")
    print()
    
    try:
        # Entrada dos dados
        print("Digite as entradas (uma por linha, termine com linha vazia):")
        inputs = []
        while True:
            line = input().strip()
            if not line:
                break
            # Permite entrada como lista ou valor único
            if ',' in line:
                inputs.append([float(x.strip()) for x in line.split(',')])
            else:
                inputs.append([float(line)])
        
        print("Digite as saídas correspondentes (uma por linha):")
        outputs = []
        for i in range(len(inputs)):
            output = float(input(f"Saída para entrada {inputs[i]}: "))
            outputs.append(output)
        
        # Busca a equação
        equation, found = finder.find_equation(inputs, outputs)
        
        if found:
            # Verifica a equação
            finder.verify_equation(equation, inputs, outputs)
            
            print(f"\n📊 Estatísticas:")
            print(f"Tentativas realizadas: {finder.attempts}")
            print(f"Equação final: f(x) = {equation}")
        else:
            print("Não foi possível encontrar uma equação simples para esses dados.")
            print("Tente com dados que sigam um padrão matemático mais simples.")
    
    except KeyboardInterrupt:
        print("\nPrograma interrompido pelo usuário.")
    except Exception as e:
        print(f"Erro: {e}")

if __name__ == "__main__":
    main()
