#!/usr/bin/env python3
"""
Exemplo final demonstrando todas as funcionalidades do buscador de equações
"""

from equation_finder import EquationFinder

def demonstracao_completa():
    print("🎯 DEMONSTRAÇÃO COMPLETA - Buscador de Equações Dinâmico")
    print("=" * 65)
    print("✨ Funcionalidades:")
    print("  • Geração dinâmica de equações baseada nos dados")
    print("  • Sistema de memória para evitar repetições")
    print("  • Contador de tentativas")
    print("  • Verificação automática dos resultados")
    print("  • Suporte a 1 ou 2 variáveis")
    print("=" * 65)
    
    finder = EquationFinder()
    
    # Exemplo 1: Equação linear personalizada
    print("\n📈 Exemplo 1: Equação Linear Personalizada")
    print("Vamos descobrir f(x) para os dados: (1→8), (2→13), (3→18), (4→23)")
    
    inputs1 = [[1], [2], [3], [4]]
    outputs1 = [8, 13, 18, 23]  # f(x) = 5x + 3
    
    equation1, found1 = finder.find_equation(inputs1, outputs1)
    if found1:
        print(f"🎉 Descobriu: {equation1}")
        print(f"⚡ Tentativas: {finder.attempts}")
        
        # Testa com novo valor
        test_x = 10
        result = finder.evaluate_equation(equation1, {'x': test_x})
        print(f"🧪 Teste: f({test_x}) = {result}")
    
    print("\n" + "-" * 65)
    
    # Exemplo 2: Equação quadrática
    print("\n📊 Exemplo 2: Equação Quadrática")
    print("Descobrindo f(x) para: (1→4), (2→9), (3→16), (4→25)")
    
    inputs2 = [[1], [2], [3], [4]]
    outputs2 = [4, 9, 16, 25]  # f(x) = (x+1)² = x² + 2x + 1
    
    equation2, found2 = finder.find_equation(inputs2, outputs2)
    if found2:
        print(f"🎉 Descobriu: {equation2}")
        print(f"⚡ Tentativas: {finder.attempts}")
        
        # Verifica se funciona
        finder.verify_equation(equation2, inputs2, outputs2)
    
    print("\n" + "-" * 65)
    
    # Exemplo 3: Duas variáveis
    print("\n🔢 Exemplo 3: Duas Variáveis")
    print("Descobrindo f(x,y) para: (2,1→7), (3,2→13), (1,4→9), (4,3→19)")
    
    inputs3 = [[2, 1], [3, 2], [1, 4], [4, 3]]
    outputs3 = [7, 13, 9, 19]  # f(x,y) = 2x + 3y + 1
    
    equation3, found3 = finder.find_equation(inputs3, outputs3)
    if found3:
        print(f"🎉 Descobriu: {equation3}")
        print(f"⚡ Tentativas: {finder.attempts}")
        
        # Testa com novos valores
        test_x, test_y = 5, 2
        result = finder.evaluate_equation(equation3, {'x': test_x, 'y': test_y})
        print(f"🧪 Teste: f({test_x}, {test_y}) = {result}")
    
    print("\n" + "-" * 65)
    
    # Exemplo 4: Demonstração da memória
    print("\n💾 Exemplo 4: Sistema de Memória")
    print("Testando os mesmos dados do Exemplo 1 novamente...")
    
    # Cria novo finder para simular nova execução
    finder2 = EquationFinder()
    equation4, found4 = finder2.find_equation(inputs1, outputs1)
    
    if found4:
        print(f"🎉 Encontrou na memória: {equation4}")
        print(f"⚡ Tentativas: {finder2.attempts} (deve ser 0)")
        print("✅ Sistema de memória funcionando!")
    
    print("\n" + "-" * 65)
    
    # Exemplo 5: Equação mais complexa
    print("\n🚀 Exemplo 5: Equação Mais Complexa")
    print("Descobrindo f(x) para: (1→-1), (2→3), (3→13)")
    
    inputs5 = [[1], [2], [3]]
    outputs5 = [-1, 3, 13]  # f(x) = 2x² - 3
    
    equation5, found5 = finder.find_equation(inputs5, outputs5)
    if found5:
        print(f"🎉 Descobriu: {equation5}")
        print(f"⚡ Tentativas: {finder.attempts}")
        
        # Verifica
        finder.verify_equation(equation5, inputs5, outputs5)
    else:
        print("❌ Não conseguiu encontrar (equação muito complexa)")

def exemplo_interativo():
    """Permite ao usuário testar com seus próprios dados"""
    print(f"\n{'='*65}")
    print("🎮 MODO INTERATIVO")
    print("Agora você pode testar com seus próprios dados!")
    print("-" * 65)
    
    finder = EquationFinder()
    
    try:
        # Pergunta quantas variáveis
        print("Quantas variáveis de entrada?")
        print("1 - Uma variável (x)")
        print("2 - Duas variáveis (x, y)")
        num_vars = int(input("Escolha (1 ou 2): "))
        
        if num_vars not in [1, 2]:
            print("❌ Número inválido de variáveis!")
            return
        
        # Pergunta quantos pontos
        num_points = int(input("Quantos pontos de dados? "))
        
        if num_points < 2:
            print("❌ Precisa de pelo menos 2 pontos!")
            return
        
        inputs = []
        outputs = []
        
        print(f"\n📥 Digite os {num_points} pontos:")
        
        for i in range(num_points):
            if num_vars == 1:
                x = float(input(f"Ponto {i+1} - x: "))
                inputs.append([x])
            else:
                x = float(input(f"Ponto {i+1} - x: "))
                y = float(input(f"Ponto {i+1} - y: "))
                inputs.append([x, y])
            
            output = float(input(f"Ponto {i+1} - saída: "))
            outputs.append(output)
        
        print(f"\n🔍 Buscando equação para seus dados...")
        equation, found = finder.find_equation(inputs, outputs)
        
        if found:
            print(f"\n🎉 SUCESSO! Equação encontrada: {equation}")
            print(f"⚡ Tentativas necessárias: {finder.attempts}")
            
            # Verifica
            finder.verify_equation(equation, inputs, outputs)
            
            # Permite testar novos valores
            print(f"\n🧪 Quer testar a equação com novos valores? (s/n)")
            if input().lower().startswith('s'):
                while True:
                    try:
                        if num_vars == 1:
                            test_input = input("Digite x (ou 'sair'): ")
                            if test_input.lower() == 'sair':
                                break
                            x = float(test_input)
                            result = finder.evaluate_equation(equation, {'x': x})
                            print(f"f({x}) = {result}")
                        else:
                            test_input = input("Digite x,y (ou 'sair'): ")
                            if test_input.lower() == 'sair':
                                break
                            x, y = map(float, test_input.split(','))
                            result = finder.evaluate_equation(equation, {'x': x, 'y': y})
                            print(f"f({x}, {y}) = {result}")
                    except:
                        print("❌ Formato inválido!")
        else:
            print(f"\n😞 Não foi possível encontrar uma equação.")
            print(f"⚡ Tentativas realizadas: {finder.attempts}")
            print("\n💡 Dicas:")
            print("  • Verifique se os dados seguem um padrão matemático")
            print("  • Tente com equações mais simples")
            print("  • Use menos pontos de dados")
    
    except Exception as e:
        print(f"❌ Erro: {e}")

def main():
    """Função principal"""
    demonstracao_completa()
    
    print(f"\n{'='*65}")
    print("🤔 Quer testar com seus próprios dados? (s/n)")
    if input().lower().startswith('s'):
        exemplo_interativo()
    
    print(f"\n{'='*65}")
    print("🎉 DEMONSTRAÇÃO CONCLUÍDA!")
    print("💾 Todas as equações foram salvas na memória")
    print("📁 Localização: python/equations/equation_memory.json")
    print("🔄 Execute novamente para ver o sistema de memória em ação")

if __name__ == "__main__":
    main()
