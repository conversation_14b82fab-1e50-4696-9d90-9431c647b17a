#!/usr/bin/env python3
"""
Análise dos números de isômeros de alcanos
Sequência: [1, 1, 1, 2, 3, 5, 9, 18, 35, 75]
Corresponde aos alcanos C1 a C10
"""

def explicar_isomeros():
    """Explica o que são isômeros de alcanos"""
    print("🧪 ISÔMEROS DE ALCANOS - ANÁLISE QUÍMICA")
    print("=" * 60)
    
    print("📚 O QUE SÃO ISÔMEROS DE ALCANOS:")
    print("Isômeros são compostos com a mesma fórmula molecular")
    print("mas estruturas diferentes (arranjos diferentes dos átomos)")
    print()
    
    alcanos_data = [
        ("C₁H₄",   "Metano",    1, ["CH₄"]),
        ("C₂H₆",   "Etano",     1, ["CH₃-CH₃"]),
        ("C₃H₈",   "Propano",   1, ["CH₃-CH₂-CH₃"]),
        ("C₄H₁₀",  "Butano",    2, ["n-butano", "isobutano"]),
        ("C₅H₁₂",  "Pentano",   3, ["n-pentano", "isopentano", "neopentano"]),
        ("C₆H₁₄",  "Hexano",    5, ["5 isômeros estruturais"]),
        ("C₇H₁₆",  "Heptano",   9, ["9 isômeros estruturais"]),
        ("C₈H₁₈",  "Octano",   18, ["18 isômeros estruturais"]),
        ("C₉H₂₀",  "Nonano",   35, ["35 isômeros estruturais"]),
        ("C₁₀H₂₂", "Decano",   75, ["75 isômeros estruturais"])
    ]
    
    print("Carbono | Fórmula | Nome     | Isômeros | Exemplos")
    print("-" * 60)
    
    for i, (formula, nome, num_iso, exemplos) in enumerate(alcanos_data, 1):
        print(f"   {i:2d}   | {formula:7s} | {nome:8s} |    {num_iso:2d}    | {exemplos[0]}")
    
    print("\n🔬 COMPLEXIDADE ESTRUTURAL:")
    print("- C1-C3: Apenas uma estrutura possível")
    print("- C4+: Começam a aparecer ramificações")
    print("- C5+: Múltiplas posições de ramificação")
    print("- C6+: Ramificações complexas e múltiplas")

def analisar_crescimento():
    """Analisa o crescimento do número de isômeros"""
    print("\n📈 ANÁLISE DO CRESCIMENTO")
    print("=" * 60)
    
    carbonos = list(range(1, 11))
    isomeros = [1, 1, 1, 2, 3, 5, 9, 18, 35, 75]
    
    print("Carbono | Isômeros | Crescimento | Razão")
    print("-" * 45)
    
    for i in range(len(carbonos)):
        if i == 0:
            crescimento = "-"
            razao = "-"
        else:
            crescimento = isomeros[i] - isomeros[i-1]
            razao = f"{isomeros[i]/isomeros[i-1]:.2f}" if isomeros[i-1] != 0 else "-"
        
        print(f"   {carbonos[i]:2d}   |    {isomeros[i]:2d}   |     {str(crescimento):2s}     | {razao:>4s}")
    
    print(f"\n🚀 OBSERVAÇÕES:")
    print("- O crescimento é exponencial")
    print("- A razão de crescimento aumenta com o tamanho")
    print("- Para C10, já temos 75 isômeros diferentes!")

def formula_matematica():
    """Explica a fórmula matemática por trás dos isômeros"""
    print("\n🧮 FÓRMULA MATEMÁTICA")
    print("=" * 60)
    
    print("📊 PADRÃO IDENTIFICADO:")
    print("A sequência [1, 1, 1, 2, 3, 5, 9, 18, 35, 75] segue:")
    print()
    print("🔹 Valores base:")
    print("   I(1) = 1  (metano)")
    print("   I(2) = 1  (etano)")
    print("   I(3) = 1  (propano)")
    print("   I(4) = 2  (butano)")
    print()
    print("🔹 Padrão recursivo complexo:")
    print("   Para n ≥ 5: I(n) ≈ I(n-1) + I(n-2) + correção(n)")
    print()
    print("   Onde correção(n) depende das possibilidades de ramificação")
    print("   específicas para alcanos com n carbonos")
    print()
    print("🔬 TEORIA COMBINATÓRIA:")
    print("- Cada isômero representa uma árvore enraizada")
    print("- O número cresce segundo a teoria dos grafos")
    print("- Relacionado à partição de inteiros e árvores planares")
    print()
    print("📚 FÓRMULA EXATA:")
    print("Não existe fórmula fechada simples!")
    print("Os valores são calculados por:")
    print("1. Algoritmos de enumeração de grafos")
    print("2. Métodos de teoria dos grupos")
    print("3. Funções geradoras complexas")

def aplicacoes_praticas():
    """Mostra aplicações práticas"""
    print("\n🎯 APLICAÇÕES PRÁTICAS")
    print("=" * 60)
    
    print("🏭 INDÚSTRIA QUÍMICA:")
    print("- Síntese de combustíveis")
    print("- Desenvolvimento de solventes")
    print("- Produção de polímeros")
    print()
    print("🔬 PESQUISA:")
    print("- Descoberta de novos materiais")
    print("- Otimização de propriedades")
    print("- Modelagem molecular")
    print()
    print("💻 COMPUTAÇÃO:")
    print("- Algoritmos de enumeração")
    print("- Teoria dos grafos")
    print("- Inteligência artificial química")
    print()
    print("📊 DADOS INTERESSANTES:")
    print(f"- C₂₀ tem aproximadamente 366.319 isômeros!")
    print(f"- C₃₀ tem mais de 4 bilhões de isômeros!")
    print(f"- O crescimento é mais rápido que exponencial")

def comparar_com_outras_sequencias():
    """Compara com outras sequências matemáticas famosas"""
    print("\n🔢 COMPARAÇÃO COM SEQUÊNCIAS FAMOSAS")
    print("=" * 60)
    
    n_values = list(range(1, 11))
    isomeros = [1, 1, 1, 2, 3, 5, 9, 18, 35, 75]
    fibonacci = [1, 1, 2, 3, 5, 8, 13, 21, 34, 55]
    catalan = [1, 1, 2, 5, 14, 42, 132, 429, 1430, 4862]
    
    print("n  | Isômeros | Fibonacci | Catalan | Observação")
    print("-" * 55)
    
    for i in range(10):
        obs = ""
        if isomeros[i] == fibonacci[i]:
            obs = "= Fib"
        elif i < 6 and isomeros[i] == fibonacci[i]:
            obs = "≈ Fib"
        
        print(f"{i+1:2d} |    {isomeros[i]:2d}    |    {fibonacci[i]:2d}     |   {catalan[i]:4d}  | {obs}")
    
    print(f"\n🔍 ANÁLISE:")
    print("- Até C6, similar ao Fibonacci")
    print("- Depois cresce mais rápido que Fibonacci")
    print("- Mais lento que números de Catalan")
    print("- Sequência única da química orgânica!")

def codigo_para_calcular():
    """Fornece código para calcular isômeros"""
    print("\n💻 CÓDIGO PARA CALCULAR ISÔMEROS")
    print("=" * 60)
    
    codigo = '''
def isomeros_alcanos(n):
    """
    Retorna o número de isômeros estruturais para alcano Cn
    
    Args:
        n (int): Número de carbonos (1-10 para valores conhecidos)
    
    Returns:
        int: Número de isômeros estruturais
    """
    # Valores conhecidos experimentalmente
    valores_conhecidos = {
        1: 1,   # Metano
        2: 1,   # Etano  
        3: 1,   # Propano
        4: 2,   # Butano
        5: 3,   # Pentano
        6: 5,   # Hexano
        7: 9,   # Heptano
        8: 18,  # Octano
        9: 35,  # Nonano
        10: 75, # Decano
        # Valores para alcanos maiores (aproximados)
        11: 159,
        12: 355,
        13: 802,
        14: 1858,
        15: 4347
    }
    
    if n in valores_conhecidos:
        return valores_conhecidos[n]
    else:
        return "Valor não disponível - requer cálculo computacional complexo"

# Exemplo de uso:
for i in range(1, 11):
    print(f"C{i}: {isomeros_alcanos(i)} isômeros")
'''
    
    print(codigo)

def main():
    """Função principal"""
    print("🧪 ANÁLISE COMPLETA: ISÔMEROS DE ALCANOS")
    print("=" * 70)
    print("Sequência analisada: [1, 1, 1, 2, 3, 5, 9, 18, 35, 75]")
    print("Corresponde aos isômeros de C₁H₄ até C₁₀H₂₂")
    print("=" * 70)
    
    explicar_isomeros()
    analisar_crescimento()
    formula_matematica()
    aplicacoes_praticas()
    comparar_com_outras_sequencias()
    codigo_para_calcular()
    
    print("\n" + "=" * 70)
    print("🎉 CONCLUSÃO:")
    print("✅ Você estava analisando um problema clássico da química!")
    print("✅ A sequência representa isômeros estruturais de alcanos")
    print("✅ O padrão é complexo porque envolve teoria dos grafos")
    print("✅ Não existe fórmula fechada simples - é calculado computacionalmente")
    print("✅ Sua análise matemática estava correta - é realmente complexo!")
    print()
    print("💡 CURIOSIDADE: Esta é uma das sequências mais estudadas")
    print("   na interseção entre química, matemática e ciência da computação!")

if __name__ == "__main__":
    main()
