#!/usr/bin/env python3
"""
Demonstração simples do buscador de equações
"""

from equation_finder import EquationFinder

def demo():
    print("🧮 DEMONSTRAÇÃO SIMPLES - Buscador de Equações")
    print("=" * 55)
    
    # Criar o buscador
    finder = EquationFinder()
    
    # Exemplo 1: Equação linear simples
    print("📈 Exemplo 1: f(x) = 3x + 2")
    print("Dados: x=1→5, x=2→8, x=3→11, x=4→14")
    
    entradas = [[1], [2], [3], [4]]
    saidas = [5, 8, 11, 14]
    
    equacao, encontrada = finder.find_equation(entradas, saidas)
    
    if encontrada:
        print(f"✅ Equação descoberta: {equacao}")
        print(f"🔢 Tentativas necessárias: {finder.attempts}")
        
        # Testar com um novo valor
        novo_x = 5
        resultado = finder.evaluate_equation(equacao, {'x': novo_x})
        print(f"🧪 Teste: f({novo_x}) = {resultado}")
        
        # Verificar se está correto (3*5 + 2 = 17)
        esperado = 3 * novo_x + 2
        print(f"✅ Correto! Esperado: {esperado}, Calculado: {resultado}")
    
    print("\n" + "-" * 55)
    
    # Exemplo 2: Equação com duas variáveis
    print("\n📊 Exemplo 2: f(x,y) = x² + y")
    print("Dados: (1,2)→3, (2,1)→5, (3,0)→9")
    
    entradas2 = [[1, 2], [2, 1], [3, 0]]
    saidas2 = [3, 5, 9]  # 1²+2=3, 2²+1=5, 3²+0=9
    
    equacao2, encontrada2 = finder.find_equation(entradas2, saidas2)
    
    if encontrada2:
        print(f"✅ Equação descoberta: {equacao2}")
        print(f"🔢 Tentativas necessárias: {finder.attempts}")
        
        # Testar com novos valores
        novo_x, novo_y = 4, 3
        resultado2 = finder.evaluate_equation(equacao2, {'x': novo_x, 'y': novo_y})
        print(f"🧪 Teste: f({novo_x}, {novo_y}) = {resultado2}")
        
        # Verificar se está correto (4² + 3 = 19)
        esperado2 = novo_x**2 + novo_y
        print(f"✅ Correto! Esperado: {esperado2}, Calculado: {resultado2}")
    
    print("\n" + "=" * 55)
    print("💾 Todas as equações foram salvas na memória!")
    print("🔄 Execute novamente para ver o sistema de memória em ação.")

if __name__ == "__main__":
    demo()
