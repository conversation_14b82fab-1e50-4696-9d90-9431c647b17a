#!/usr/bin/env python3
"""
Teste do sistema dinâmico de geração de equações
"""

from equation_finder import EquationFinder

def teste_equacao_personalizada():
    """Testa equações que não estavam pré-definidas"""
    print("🧪 TESTE DE GERAÇÃO DINÂMICA DE EQUAÇÕES")
    print("=" * 60)
    
    finder = EquationFinder()
    
    # Teste 1: Equação linear com coeficientes específicos: f(x) = 7x - 4
    print("📊 Teste 1: f(x) = 7x - 4")
    inputs1 = [[1], [2], [3], [4]]
    outputs1 = [3, 10, 17, 24]  # 7*1-4=3, 7*2-4=10, 7*3-4=17, 7*4-4=24
    
    equation1, found1 = finder.find_equation(inputs1, outputs1)
    if found1:
        print(f"✅ Equação encontrada: {equation1}")
        finder.verify_equation(equation1, inputs1, outputs1)
    else:
        print("❌ Não encontrou a equação")
    
    print("\n" + "-" * 60 + "\n")
    
    # Teste 2: Equação quadrática: f(x) = 2x² + 3
    print("📊 Teste 2: f(x) = 2x² + 3")
    inputs2 = [[1], [2], [3]]
    outputs2 = [5, 11, 21]  # 2*1²+3=5, 2*2²+3=11, 2*3²+3=21
    
    equation2, found2 = finder.find_equation(inputs2, outputs2)
    if found2:
        print(f"✅ Equação encontrada: {equation2}")
        finder.verify_equation(equation2, inputs2, outputs2)
    else:
        print("❌ Não encontrou a equação")
    
    print("\n" + "-" * 60 + "\n")
    
    # Teste 3: Duas variáveis: f(x,y) = 3x + 2y
    print("📊 Teste 3: f(x,y) = 3x + 2y")
    inputs3 = [[1, 1], [2, 3], [4, 1], [3, 2]]
    outputs3 = [5, 12, 14, 13]  # 3*1+2*1=5, 3*2+2*3=12, 3*4+2*1=14, 3*3+2*2=13
    
    equation3, found3 = finder.find_equation(inputs3, outputs3)
    if found3:
        print(f"✅ Equação encontrada: {equation3}")
        finder.verify_equation(equation3, inputs3, outputs3)
    else:
        print("❌ Não encontrou a equação")
    
    print("\n" + "-" * 60 + "\n")
    
    # Teste 4: Equação mais complexa: f(x) = x³ - 2x + 1
    print("📊 Teste 4: f(x) = x³ - 2x + 1")
    inputs4 = [[1], [2], [3]]
    outputs4 = [0, 5, 22]  # 1³-2*1+1=0, 2³-2*2+1=5, 3³-2*3+1=22
    
    equation4, found4 = finder.find_equation(inputs4, outputs4, max_attempts=2000)
    if found4:
        print(f"✅ Equação encontrada: {equation4}")
        finder.verify_equation(equation4, inputs4, outputs4)
    else:
        print("❌ Não encontrou a equação (muito complexa)")
    
    print("\n" + "-" * 60 + "\n")
    
    # Teste 5: Padrão não-matemático (deve falhar)
    print("📊 Teste 5: Dados sem padrão matemático")
    inputs5 = [[1], [2], [3], [4]]
    outputs5 = [7, 15, 2, 99]  # Números aleatórios
    
    equation5, found5 = finder.find_equation(inputs5, outputs5)
    if found5:
        print(f"✅ Equação encontrada: {equation5}")
        finder.verify_equation(equation5, inputs5, outputs5)
    else:
        print("❌ Não encontrou equação (esperado - dados sem padrão)")

def teste_analise_inteligente():
    """Testa a análise inteligente dos dados"""
    print("\n🧠 TESTE DE ANÁLISE INTELIGENTE")
    print("=" * 60)
    
    finder = EquationFinder()
    
    # Dados para f(x) = 5x + 2
    inputs = [[1], [3], [5]]
    outputs = [7, 17, 27]
    
    print("📈 Analisando dados para f(x) = 5x + 2")
    print(f"Entradas: {inputs}")
    print(f"Saídas: {outputs}")
    
    # Mostra as equações geradas pela análise
    smart_equations = finder.analyze_data_patterns(inputs, outputs)
    print(f"\n🎯 Equações geradas pela análise inteligente:")
    for i, eq in enumerate(smart_equations[:10], 1):  # Mostra apenas as primeiras 10
        print(f"  {i}. {eq}")
    
    print(f"\n📊 Total de equações inteligentes: {len(smart_equations)}")
    
    # Busca a equação
    equation, found = finder.find_equation(inputs, outputs)
    if found:
        print(f"\n✅ Equação encontrada: {equation}")
        print(f"🔢 Tentativas necessárias: {finder.attempts}")

def main():
    """Executa todos os testes"""
    teste_equacao_personalizada()
    teste_analise_inteligente()
    
    print(f"\n{'='*60}")
    print("🎉 TESTES CONCLUÍDOS!")
    print("💡 O programa agora gera equações dinamicamente baseado nos dados")
    print("📁 Arquivo de memória salvo em: python/equations/equation_memory.json")

if __name__ == "__main__":
    main()
