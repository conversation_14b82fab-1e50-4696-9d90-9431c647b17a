#!/usr/bin/env python3
"""
Exemplo simples de uso do buscador de equações
"""

from equation_finder import EquationFinder

def exemplo_basico():
    """Exemplo básico de uso"""
    print("🔍 EXEMPLO BÁSICO - Buscador de Equações")
    print("=" * 50)
    
    # Cria o buscador
    finder = EquationFinder()
    
    # Exemplo 1: f(x) = 2x + 3
    print("📊 Exemplo 1: Encontrando equação para f(x) = 2x + 3")
    inputs1 = [[1], [2], [3], [4]]  # x = 1, 2, 3, 4
    outputs1 = [5, 7, 9, 11]        # f(x) = 5, 7, 9, 11
    
    equation1, found1 = finder.find_equation(inputs1, outputs1)
    if found1:
        print(f"✅ Equação encontrada: {equation1}")
        finder.verify_equation(equation1, inputs1, outputs1)
    
    print("\n" + "-" * 50 + "\n")
    
    # Exemplo 2: f(x) = x²
    print("📊 Exemplo 2: Encontrando equação para f(x) = x²")
    inputs2 = [[1], [2], [3], [4], [5]]
    outputs2 = [1, 4, 9, 16, 25]
    
    equation2, found2 = finder.find_equation(inputs2, outputs2)
    if found2:
        print(f"✅ Equação encontrada: {equation2}")
        finder.verify_equation(equation2, inputs2, outputs2)
    
    print("\n" + "-" * 50 + "\n")
    
    # Exemplo 3: Duas variáveis f(x,y) = x * y
    print("📊 Exemplo 3: Duas variáveis f(x,y) = x * y")
    inputs3 = [[2, 3], [4, 5], [1, 6], [3, 2]]
    outputs3 = [6, 20, 6, 6]
    
    equation3, found3 = finder.find_equation(inputs3, outputs3)
    if found3:
        print(f"✅ Equação encontrada: {equation3}")
        finder.verify_equation(equation3, inputs3, outputs3)

def exemplo_personalizado():
    """Permite ao usuário inserir seus próprios dados"""
    print("\n🎯 EXEMPLO PERSONALIZADO")
    print("=" * 50)
    print("Agora você pode inserir seus próprios dados!")
    
    finder = EquationFinder()
    
    try:
        # Coleta dados do usuário
        print("\nFormatos aceitos:")
        print("- Uma variável: digite apenas o valor (ex: 5)")
        print("- Duas variáveis: digite separado por vírgula (ex: 3,4)")
        print("- Para terminar a entrada, digite uma linha vazia")
        
        inputs = []
        print("\n📥 Digite os valores de entrada:")
        while True:
            entrada = input("Entrada: ").strip()
            if not entrada:
                break
            
            if ',' in entrada:
                # Duas variáveis
                valores = [float(x.strip()) for x in entrada.split(',')]
                inputs.append(valores)
            else:
                # Uma variável
                inputs.append([float(entrada)])
        
        if not inputs:
            print("❌ Nenhuma entrada fornecida!")
            return
        
        outputs = []
        print(f"\n📤 Digite as {len(inputs)} saídas correspondentes:")
        for i, inp in enumerate(inputs):
            if len(inp) == 1:
                saida = float(input(f"Saída para x={inp[0]}: "))
            else:
                saida = float(input(f"Saída para x={inp[0]}, y={inp[1]}: "))
            outputs.append(saida)
        
        # Busca a equação
        print(f"\n🔍 Buscando equação para {len(inputs)} pontos...")
        equation, found = finder.find_equation(inputs, outputs)
        
        if found:
            print(f"\n🎉 Sucesso! Equação encontrada: {equation}")
            finder.verify_equation(equation, inputs, outputs)
            
            # Oferece teste com novos valores
            print(f"\n🧪 Quer testar a equação com novos valores? (s/n)")
            if input().lower().startswith('s'):
                testar_novos_valores(finder, equation, len(inputs[0]))
        else:
            print("\n😞 Não foi possível encontrar uma equação simples.")
            print("Dicas:")
            print("- Verifique se os dados seguem um padrão matemático")
            print("- Tente com menos pontos ou padrões mais simples")
            print("- O programa busca equações básicas (+, -, *, /, ², √, etc.)")
    
    except Exception as e:
        print(f"❌ Erro: {e}")

def testar_novos_valores(finder, equation, num_vars):
    """Permite testar a equação encontrada com novos valores"""
    print(f"\n🧪 Testando equação: {equation}")
    print("Digite 'sair' para terminar")
    
    while True:
        try:
            if num_vars == 1:
                entrada = input("Digite x: ").strip()
                if entrada.lower() == 'sair':
                    break
                x = float(entrada)
                variables = {'x': x}
                resultado = finder.evaluate_equation(equation, variables)
                print(f"f({x}) = {resultado}")
            
            else:  # num_vars == 2
                entrada = input("Digite x,y: ").strip()
                if entrada.lower() == 'sair':
                    break
                x, y = map(float, entrada.split(','))
                variables = {'x': x, 'y': y}
                resultado = finder.evaluate_equation(equation, variables)
                print(f"f({x}, {y}) = {resultado}")
        
        except Exception as e:
            print(f"Erro: {e}. Tente novamente.")

def main():
    """Função principal"""
    print("🧮 BUSCADOR DE EQUAÇÕES")
    print("Programa que encontra equações matemáticas a partir de dados")
    print("=" * 60)
    
    # Executa exemplo básico
    exemplo_basico()
    
    # Pergunta se quer fazer exemplo personalizado
    print(f"\n{'='*60}")
    print("🤔 Quer tentar com seus próprios dados? (s/n)")
    if input().lower().startswith('s'):
        exemplo_personalizado()
    
    print(f"\n✅ Programa finalizado!")
    print("💾 As equações encontradas foram salvas na memória para uso futuro.")

if __name__ == "__main__":
    main()
