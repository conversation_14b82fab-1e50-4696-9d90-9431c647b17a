{"[[1], [2], [3], [4], [5]]_[1, 4, 9, 16, 25]": "x**2", "[[2, 3], [4, 5], [1, 6], [3, 2]]_[6, 20, 6, 6]": "x * y", "[[1], [2], [3], [4]]_[5, 7, 9, 11]": "2*x + 3", "[[1], [2], [3], [4], [5]]_[3, 5, 7, 9, 11]": "2*x + 1", "[[1], [2], [3], [4]]_[1, 4, 9, 16]": "x**2", "[[1, 2], [3, 4], [5, 6], [2, 3]]_[3, 7, 11, 5]": "x + y", "[[1], [2], [3]]_[2, 4, 6]": "2*x", "[[1], [2], [3], [4]]_[5, 8, 11, 14]": "3*x + 2", "[[1, 2], [2, 1], [3, 0]]_[3, 5, 9]": "x**2 + y", "[[1], [2], [3], [4]]_[3, 10, 17, 24]": "7.0*x - 4.0", "[[1, 1], [2, 3], [4, 1], [3, 2]]_[5, 12, 14, 13]": "3*x + 2*y", "[[1], [3], [5]]_[7, 17, 27]": "5.0*x + 2.0", "[[1], [2], [3]]_[5, 11, 21]": "2.0*x**2 + 3", "[[1], [2], [3], [4]]_[8, 13, 18, 23]": "5.0*x + 3.0", "[[1], [2], [3], [4]]_[4, 9, 16, 25]": "1*x**2 + 2*x + 1", "[[1.0], [2.0], [3.0]]_[1.0, 1.0, 1.0]": "0.0*x**2 + 1"}