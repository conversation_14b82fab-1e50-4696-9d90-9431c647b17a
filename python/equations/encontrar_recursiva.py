#!/usr/bin/env python3
"""
Programa especializado em encontrar equações recursivas
"""

def analisar_fibonacci_modificado(dados_y):
    """Analisa se é uma sequência tipo Fibonacci modificada"""
    print("🔍 Analisando padrão Fibonacci modificado...")
    
    # Verifica diferentes pontos de início para Fibonacci
    for inicio in range(len(dados_y) - 2):
        print(f"\nTestando Fibonacci a partir da posição {inicio + 1}:")
        
        # Verifica se a partir de 'inicio', segue F(n) = F(n-1) + F(n-2)
        is_fibonacci = True
        for i in range(inicio + 2, len(dados_y)):
            esperado = dados_y[i-1] + dados_y[i-2]
            real = dados_y[i]
            print(f"  Posição {i+1}: {dados_y[i-2]} + {dados_y[i-1]} = {esperado}, real = {real}")
            
            if esperado != real:
                is_fibonacci = False
        
        if is_fibonacci and inicio < len(dados_y) - 2:
            print(f"✅ FIBONACCI detectado a partir da posição {inicio + 3}!")
            
            # Cria a fórmula
            if inicio == 0:
                formula = f"F(1)={dados_y[0]}, F(2)={dados_y[1]}, F(n)=F(n-1)+F(n-2) para n≥3"
            elif inicio == 1:
                formula = f"F(1)={dados_y[0]}, F(2)={dados_y[1]}, F(3)={dados_y[2]}, F(n)=F(n-1)+F(n-2) para n≥4"
            else:
                formula = f"Valores iniciais: {dados_y[:inicio+2]}, F(n)=F(n-1)+F(n-2) para n≥{inicio+3}"
            
            return True, formula
    
    return False, None

def analisar_tribonacci(dados_y):
    """Analisa se é uma sequência tipo Tribonacci: F(n) = F(n-1) + F(n-2) + F(n-3)"""
    print("\n🔍 Analisando padrão Tribonacci...")
    
    if len(dados_y) < 4:
        return False, None
    
    for inicio in range(len(dados_y) - 3):
        print(f"\nTestando Tribonacci a partir da posição {inicio + 1}:")
        
        is_tribonacci = True
        for i in range(inicio + 3, len(dados_y)):
            esperado = dados_y[i-1] + dados_y[i-2] + dados_y[i-3]
            real = dados_y[i]
            print(f"  Posição {i+1}: {dados_y[i-3]} + {dados_y[i-2]} + {dados_y[i-1]} = {esperado}, real = {real}")
            
            if esperado != real:
                is_tribonacci = False
        
        if is_tribonacci:
            print(f"✅ TRIBONACCI detectado a partir da posição {inicio + 4}!")
            formula = f"F(1)={dados_y[0]}, F(2)={dados_y[1]}, F(3)={dados_y[2]}, F(n)=F(n-1)+F(n-2)+F(n-3) para n≥4"
            return True, formula
    
    return False, None

def analisar_recursiva_linear(dados_y):
    """Analisa padrões do tipo F(n) = a*F(n-1) + b*F(n-2) + c"""
    print("\n🔍 Analisando recursão linear geral...")
    
    if len(dados_y) < 3:
        return False, None
    
    # Testa diferentes coeficientes
    for a in [1, 2, 3, 0.5, 1.5]:
        for b in [1, 2, 3, 0.5, 1.5, -1, -0.5]:
            for c in [0, 1, -1, 2, -2]:
                funciona = True
                for i in range(2, len(dados_y)):
                    esperado = a * dados_y[i-1] + b * dados_y[i-2] + c
                    if abs(esperado - dados_y[i]) > 0.001:
                        funciona = False
                        break
                
                if funciona:
                    print(f"✅ Recursão linear encontrada!")
                    if c == 0:
                        formula = f"F(n) = {a}*F(n-1) + {b}*F(n-2)"
                    else:
                        formula = f"F(n) = {a}*F(n-1) + {b}*F(n-2) + {c}"
                    
                    print(f"Fórmula: {formula}")
                    print(f"Valores iniciais: F(1)={dados_y[0]}, F(2)={dados_y[1]}")
                    return True, formula
    
    return False, None

def verificar_formula_recursiva(dados_y, formula_func, nome):
    """Verifica se uma fórmula recursiva funciona"""
    print(f"\n🧪 Verificando {nome}:")
    
    calculados = []
    for i in range(len(dados_y)):
        calc = formula_func(i + 1, dados_y)
        calculados.append(calc)
        status = "✅" if calc == dados_y[i] else "❌"
        print(f"  F({i+1}) = {calc}, real = {dados_y[i]} {status}")
    
    return calculados == dados_y

def criar_programa_personalizado(dados_x, dados_y):
    """Cria um programa personalizado para os dados específicos"""
    
    # Analisando os dados específicos: [1, 1, 1, 2, 3, 5, 9, 18, 35, 75]
    print("🎯 Análise específica dos seus dados:")
    print("Dados: [1, 1, 1, 2, 3, 5, 9, 18, 35, 75]")
    
    # Vou analisar manualmente o padrão
    print("\nAnálise manual:")
    print("Posição 1: 1")
    print("Posição 2: 1") 
    print("Posição 3: 1")
    print("Posição 4: 2 = ?")
    print("Posição 5: 3 = 1 + 2 (posições 3 + 4)")
    print("Posição 6: 5 = 2 + 3 (posições 4 + 5)")
    print("Posição 7: 9 = 3 + 5 + 1 = ?")
    
    # Vou testar uma hipótese: F(n) = F(n-1) + F(n-2) + algo
    print("\n🔬 Testando hipóteses:")
    
    # Hipótese 1: A partir da posição 4, F(n) = F(n-1) + F(n-2)
    print("Hipótese 1: Fibonacci a partir da posição 4")
    for i in range(4, len(dados_y)):
        esperado = dados_y[i-1] + dados_y[i-2]
        real = dados_y[i]
        print(f"  F({i+1}) = F({i}) + F({i-1}) = {dados_y[i-1]} + {dados_y[i-2]} = {esperado}, real = {real}")
    
    # Hipótese 2: F(n) = F(n-1) + F(n-2) + F(n-3) para n >= 7
    print("\nHipótese 2: Tribonacci a partir da posição 7")
    for i in range(6, len(dados_y)):
        esperado = dados_y[i-1] + dados_y[i-2] + dados_y[i-3]
        real = dados_y[i]
        print(f"  F({i+1}) = F({i}) + F({i-1}) + F({i-2}) = {dados_y[i-1]} + {dados_y[i-2]} + {dados_y[i-3]} = {esperado}, real = {real}")
    
    # Hipótese 3: Padrão misto
    print("\nHipótese 3: Padrão misto")
    print("F(1) = 1, F(2) = 1, F(3) = 1")
    print("F(4) = 2 (valor especial)")
    print("Para n ≥ 5: F(n) = F(n-1) + F(n-2)")
    
    # Verifica hipótese 3
    correto = True
    for i in range(4, len(dados_y)):  # A partir da posição 5 (índice 4)
        esperado = dados_y[i-1] + dados_y[i-2]
        real = dados_y[i]
        status = "✅" if esperado == real else "❌"
        print(f"  F({i+1}) = {dados_y[i-1]} + {dados_y[i-2]} = {esperado}, real = {real} {status}")
        if esperado != real:
            correto = False
    
    if correto:
        print("\n🎉 PADRÃO ENCONTRADO!")
        print("Fórmula:")
        print("  F(1) = 1")
        print("  F(2) = 1") 
        print("  F(3) = 1")
        print("  F(4) = 2")
        print("  F(n) = F(n-1) + F(n-2) para n ≥ 5")
        
        return True, "F(1)=1, F(2)=1, F(3)=1, F(4)=2, F(n)=F(n-1)+F(n-2) para n≥5"
    
    return False, None

def main():
    """Função principal"""
    print("🔍 BUSCADOR DE EQUAÇÕES RECURSIVAS")
    print("=" * 60)
    
    # Dados do arquivo testes.py
    dados_x = [1, 2, 3, 4, 5, 6, 7, 8, 9, 10]
    dados_y = [1, 1, 1, 2, 3, 5, 9, 18, 35, 75]
    
    print(f"Dados X: {dados_x}")
    print(f"Dados Y: {dados_y}")
    print("=" * 60)
    
    # Tenta diferentes análises
    encontrou = False
    
    # Análise Fibonacci modificado
    fib_encontrado, fib_formula = analisar_fibonacci_modificado(dados_y)
    if fib_encontrado:
        encontrou = True
        print(f"\n🎉 SOLUÇÃO: {fib_formula}")
    
    # Análise Tribonacci
    if not encontrou:
        trib_encontrado, trib_formula = analisar_tribonacci(dados_y)
        if trib_encontrado:
            encontrou = True
            print(f"\n🎉 SOLUÇÃO: {trib_formula}")
    
    # Análise recursiva linear
    if not encontrou:
        rec_encontrado, rec_formula = analisar_recursiva_linear(dados_y)
        if rec_encontrado:
            encontrou = True
            print(f"\n🎉 SOLUÇÃO: {rec_formula}")
    
    # Análise personalizada
    if not encontrou:
        pers_encontrado, pers_formula = criar_programa_personalizado(dados_x, dados_y)
        if pers_encontrado:
            encontrou = True
            print(f"\n🎉 SOLUÇÃO: {pers_formula}")
    
    if not encontrou:
        print("\n😞 Nenhum padrão recursivo simples foi encontrado.")
        print("Os dados podem seguir uma fórmula muito específica ou complexa.")

if __name__ == "__main__":
    main()
