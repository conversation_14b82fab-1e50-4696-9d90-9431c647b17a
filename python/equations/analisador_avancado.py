#!/usr/bin/env python3
"""
Analisador avançado de padrões para encontrar equações complexas
"""

import math
from typing import List, Tuple, Optional

class AnalisadorAvancado:
    def __init__(self):
        self.dados_x = []
        self.dados_y = []
    
    def analisar_dados(self, inputs: List[List[float]], outputs: List[float]):
        """Analisa os dados fornecidos"""
        self.dados_x = [inp[0] for inp in inputs]
        self.dados_y = outputs
        
        print("🔍 ANÁLISE AVANÇADA DE PADRÕES")
        print("=" * 50)
        print(f"Dados X: {self.dados_x}")
        print(f"Dados Y: {self.dados_y}")
        print("-" * 50)
        
        # Análises diferentes
        self.analisar_diferencas()
        self.analisar_razoes()
        self.analisar_fibonacci()
        self.analisar_exponencial()
        self.analisar_fatorial()
        self.analisar_recursivo()
        self.analisar_polinomial()
    
    def analisar_diferencas(self):
        """Analisa diferenças entre termos consecutivos"""
        print("\n📊 Análise de Diferenças:")
        
        if len(self.dados_y) < 2:
            return
        
        # Primeira diferença
        diff1 = [self.dados_y[i+1] - self.dados_y[i] for i in range(len(self.dados_y)-1)]
        print(f"1ª diferença: {diff1}")
        
        # Segunda diferença
        if len(diff1) > 1:
            diff2 = [diff1[i+1] - diff1[i] for i in range(len(diff1)-1)]
            print(f"2ª diferença: {diff2}")
            
            # Se segunda diferença é constante, é quadrática
            if len(set(diff2)) == 1:
                print("✅ Padrão QUADRÁTICO detectado!")
        
        # Terceira diferença
        if len(diff1) > 2:
            diff3 = [diff2[i+1] - diff2[i] for i in range(len(diff2)-1)]
            print(f"3ª diferença: {diff3}")
            
            if len(set(diff3)) == 1:
                print("✅ Padrão CÚBICO detectado!")
    
    def analisar_razoes(self):
        """Analisa razões entre termos consecutivos"""
        print("\n📈 Análise de Razões:")
        
        if len(self.dados_y) < 2:
            return
        
        razoes = []
        for i in range(len(self.dados_y)-1):
            if self.dados_y[i] != 0:
                razao = self.dados_y[i+1] / self.dados_y[i]
                razoes.append(razao)
        
        print(f"Razões: {[f'{r:.3f}' for r in razoes]}")
        
        # Verifica se é geométrica (razão constante)
        if len(set([round(r, 3) for r in razoes])) == 1:
            print(f"✅ Progressão GEOMÉTRICA detectada! Razão = {razoes[0]:.3f}")
    
    def analisar_fibonacci(self):
        """Verifica se segue padrão de Fibonacci"""
        print("\n🌀 Análise de Fibonacci:")
        
        if len(self.dados_y) < 3:
            return
        
        is_fibonacci = True
        for i in range(2, len(self.dados_y)):
            if self.dados_y[i] != self.dados_y[i-1] + self.dados_y[i-2]:
                is_fibonacci = False
                break
        
        if is_fibonacci:
            print("✅ Sequência de FIBONACCI detectada!")
            print(f"Fórmula: F(n) = F(n-1) + F(n-2), F(1)={self.dados_y[0]}, F(2)={self.dados_y[1]}")
        else:
            # Verifica Fibonacci modificado
            print("Verificando Fibonacci modificado...")
            for offset in range(-5, 6):
                is_modified_fib = True
                for i in range(2, len(self.dados_y)):
                    if self.dados_y[i] != self.dados_y[i-1] + self.dados_y[i-2] + offset:
                        is_modified_fib = False
                        break
                
                if is_modified_fib and offset != 0:
                    print(f"✅ Fibonacci MODIFICADO detectado! F(n) = F(n-1) + F(n-2) + {offset}")
                    break
    
    def analisar_exponencial(self):
        """Analisa padrões exponenciais"""
        print("\n🚀 Análise Exponencial:")
        
        # Testa y = a * b^x
        for base in [2, 3, 4, 5, 1.5, 2.5]:
            try:
                # Calcula 'a' usando o primeiro ponto
                if self.dados_x[0] != 0:
                    a = self.dados_y[0] / (base ** self.dados_x[0])
                    
                    # Verifica se funciona para todos os pontos
                    funciona = True
                    for i, (x, y) in enumerate(zip(self.dados_x, self.dados_y)):
                        esperado = a * (base ** x)
                        if abs(esperado - y) > 0.1:
                            funciona = False
                            break
                    
                    if funciona:
                        print(f"✅ Padrão EXPONENCIAL: y = {a:.3f} * {base}^x")
            except:
                pass
    
    def analisar_fatorial(self):
        """Analisa padrões relacionados a fatorial"""
        print("\n🔢 Análise Fatorial:")
        
        # Verifica se y = x!
        funciona_fatorial = True
        for x, y in zip(self.dados_x, self.dados_y):
            if x > 0 and math.factorial(int(x)) != y:
                funciona_fatorial = False
                break
        
        if funciona_fatorial:
            print("✅ Padrão FATORIAL: y = x!")
        
        # Verifica padrões relacionados a fatorial
        for coef in [1, 2, 0.5]:
            funciona = True
            for x, y in zip(self.dados_x, self.dados_y):
                if x > 0:
                    esperado = coef * math.factorial(int(x))
                    if abs(esperado - y) > 0.1:
                        funciona = False
                        break
            
            if funciona and coef != 1:
                print(f"✅ Fatorial MODIFICADO: y = {coef} * x!")
    
    def analisar_recursivo(self):
        """Analisa padrões recursivos mais complexos"""
        print("\n🔄 Análise Recursiva:")
        
        if len(self.dados_y) < 3:
            return
        
        # Verifica y(n) = a*y(n-1) + b*y(n-2)
        for a in [1, 2, 3, 0.5, 1.5]:
            for b in [1, 2, 3, 0.5, 1.5, -1]:
                funciona = True
                for i in range(2, len(self.dados_y)):
                    esperado = a * self.dados_y[i-1] + b * self.dados_y[i-2]
                    if abs(esperado - self.dados_y[i]) > 0.1:
                        funciona = False
                        break
                
                if funciona:
                    print(f"✅ Recursão LINEAR: y(n) = {a}*y(n-1) + {b}*y(n-2)")
                    return
        
        # Verifica padrões mais complexos
        print("Verificando padrões recursivos complexos...")
        
        # y(n) = y(n-1) + y(n-2) + f(n)
        for i in range(2, min(6, len(self.dados_y))):
            soma_anterior = self.dados_y[i-1] + self.dados_y[i-2]
            diferenca = self.dados_y[i] - soma_anterior
            print(f"y({i+1}) - [y({i}) + y({i-1})] = {diferenca}")
    
    def analisar_polinomial(self):
        """Tenta encontrar polinômios de grau alto"""
        print("\n📐 Análise Polinomial:")
        
        # Tenta ajustar polinômios de diferentes graus
        for grau in [3, 4, 5]:
            if len(self.dados_x) >= grau + 1:
                try:
                    # Usa os primeiros pontos para calcular coeficientes
                    # Isso é uma simplificação - um método real usaria mínimos quadrados
                    print(f"Tentando polinômio de grau {grau}...")
                    
                    # Para grau 3: ax³ + bx² + cx + d
                    if grau == 3 and len(self.dados_x) >= 4:
                        # Verifica alguns padrões cúbicos simples
                        for a in [0.1, 0.2, 0.5, 1, 2]:
                            for b in [-2, -1, 0, 1, 2]:
                                for c in [-2, -1, 0, 1, 2]:
                                    for d in [-2, -1, 0, 1, 2]:
                                        funciona = True
                                        for x, y in zip(self.dados_x[:4], self.dados_y[:4]):
                                            esperado = a*x**3 + b*x**2 + c*x + d
                                            if abs(esperado - y) > 0.1:
                                                funciona = False
                                                break
                                        
                                        if funciona:
                                            print(f"✅ Polinômio cúbico: y = {a}x³ + {b}x² + {c}x + {d}")
                                            return
                except:
                    pass

def main():
    """Analisa os dados do arquivo testes.py"""
    print("🔬 ANALISADOR AVANÇADO DE PADRÕES")
    print("=" * 60)
    
    # Dados do arquivo testes.py
    inputs = [[1], [2], [3], [4], [5], [6], [7], [8], [9], [10]]
    outputs = [1, 1, 1, 2, 3, 5, 9, 18, 35, 75]
    
    analisador = AnalisadorAvancado()
    analisador.analisar_dados(inputs, outputs)
    
    print("\n" + "=" * 60)
    print("🎯 CONCLUSÕES E SUGESTÕES:")
    print("- Se nenhum padrão foi detectado, os dados podem seguir uma")
    print("  fórmula muito específica ou complexa")
    print("- Tente reduzir o número de pontos para encontrar padrões mais simples")
    print("- Considere que pode haver ruído nos dados")

if __name__ == "__main__":
    main()
