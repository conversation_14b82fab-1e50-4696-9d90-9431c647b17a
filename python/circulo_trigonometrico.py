import matplotlib.pyplot as plt
import matplotlib.patches as patches
import numpy as np
import math
from matplotlib.animation import FuncAnimation
import tkinter as tk
from tkinter import ttk
from matplotlib.backends.backend_tkagg import FigureCanvasTkAgg

class CirculoTrigonometrico:
    def __init__(self):
        self.root = tk.Tk()
        self.root.title("Interactive Trigonometric Circle")
        self.root.geometry("1200x800")

        # Variáveis
        self.angle = 0
        self.animation_running = False

        # Variáveis de controle para mostrar/ocultar funções
        self.show_sin = tk.BooleanVar(value=True)
        self.show_cos = tk.BooleanVar(value=True)
        self.show_tan = tk.BooleanVar(value=False)
        self.show_csc = tk.BooleanVar(value=False)
        self.show_sec = tk.BooleanVar(value=False)
        self.show_cot = tk.BooleanVar(value=False)

        # Configurar interface
        self.setup_interface()
        self.setup_plot()
        self.update_plot()

    def setup_interface(self):
        # Frame principal
        main_frame = ttk.Frame(self.root)
        main_frame.pack(fill=tk.BOTH, expand=True, padx=10, pady=10)

        # Frame de controles
        control_frame = ttk.LabelFrame(main_frame, text="Controls", padding=10)
        control_frame.pack(side=tk.TOP, fill=tk.X, pady=(0, 10))

        # Entrada de ângulo
        ttk.Label(control_frame, text="Angle (degrees):").grid(row=0, column=0, padx=5)
        self.angle_var = tk.StringVar(value="0")
        self.angle_entry = ttk.Entry(control_frame, textvariable=self.angle_var, width=10)
        self.angle_entry.grid(row=0, column=1, padx=5)
        self.angle_entry.bind('<Return>', self.update_angle)

        # Botões
        ttk.Button(control_frame, text="Update", command=self.update_angle).grid(row=0, column=2, padx=5)
        ttk.Button(control_frame, text="Animate", command=self.toggle_animation).grid(row=0, column=3, padx=5)
        ttk.Button(control_frame, text="Reset", command=self.reset_angle).grid(row=0, column=4, padx=5)

        # Slider para ângulo
        self.angle_scale = ttk.Scale(control_frame, from_=0, to=360, orient=tk.HORIZONTAL,
                                   length=300, command=self.update_from_scale)
        self.angle_scale.grid(row=1, column=0, columnspan=5, pady=10, sticky="ew")

        # Frame para checkboxes de visibilidade
        visibility_frame = ttk.LabelFrame(main_frame, text="Show/Hide Functions", padding=10)
        visibility_frame.pack(side=tk.TOP, fill=tk.X, pady=(0, 10))

        # Checkboxes para funções principais
        ttk.Checkbutton(visibility_frame, text="sin(θ)", variable=self.show_sin,
                       command=self.update_plot).grid(row=0, column=0, padx=10, sticky="w")
        ttk.Checkbutton(visibility_frame, text="cos(θ)", variable=self.show_cos,
                       command=self.update_plot).grid(row=0, column=1, padx=10, sticky="w")
        ttk.Checkbutton(visibility_frame, text="tan(θ)", variable=self.show_tan,
                       command=self.update_plot).grid(row=0, column=2, padx=10, sticky="w")

        # Checkboxes para funções recíprocas
        ttk.Checkbutton(visibility_frame, text="csc(θ)", variable=self.show_csc,
                       command=self.update_plot).grid(row=1, column=0, padx=10, sticky="w")
        ttk.Checkbutton(visibility_frame, text="sec(θ)", variable=self.show_sec,
                       command=self.update_plot).grid(row=1, column=1, padx=10, sticky="w")
        ttk.Checkbutton(visibility_frame, text="cot(θ)", variable=self.show_cot,
                       command=self.update_plot).grid(row=1, column=2, padx=10, sticky="w")

        # Frame de valores
        values_frame = ttk.LabelFrame(main_frame, text="Trigonometric Values", padding=10)
        values_frame.pack(side=tk.TOP, fill=tk.X, pady=(0, 10))

        # Labels para valores - Primeira linha (funções principais)
        self.sin_label = ttk.Label(values_frame, text="sin(θ) = 0.000", font=("Arial", 12))
        self.sin_label.grid(row=0, column=0, padx=15)

        self.cos_label = ttk.Label(values_frame, text="cos(θ) = 1.000", font=("Arial", 12))
        self.cos_label.grid(row=0, column=1, padx=15)

        self.tan_label = ttk.Label(values_frame, text="tan(θ) = 0.000", font=("Arial", 12))
        self.tan_label.grid(row=0, column=2, padx=15)

        # Labels para valores - Segunda linha (funções recíprocas)
        self.csc_label = ttk.Label(values_frame, text="csc(θ) = ∞", font=("Arial", 12), foreground="lightblue")
        self.csc_label.grid(row=1, column=0, padx=15, pady=5)

        self.sec_label = ttk.Label(values_frame, text="sec(θ) = 1.000", font=("Arial", 12), foreground="lightblue")
        self.sec_label.grid(row=1, column=1, padx=15, pady=5)

        self.cot_label = ttk.Label(values_frame, text="cot(θ) = ∞", font=("Arial", 12), foreground="lightblue")
        self.cot_label.grid(row=1, column=2, padx=15, pady=5)

        # Frame do gráfico
        self.plot_frame = ttk.Frame(main_frame)
        self.plot_frame.pack(fill=tk.BOTH, expand=True)

    def setup_plot(self):
        # Criar figura
        self.fig, self.ax = plt.subplots(figsize=(10, 8))
        self.fig.patch.set_facecolor('white')

        # Configurar canvas
        self.canvas = FigureCanvasTkAgg(self.fig, self.plot_frame)
        self.canvas.get_tk_widget().pack(fill=tk.BOTH, expand=True)

        # Configurar eixos
        self.ax.set_xlim(-1.5, 1.5)
        self.ax.set_ylim(-1.5, 1.5)
        self.ax.set_aspect('equal')
        self.ax.grid(True, alpha=0.3)
        self.ax.axhline(y=0, color='k', linewidth=0.8)
        self.ax.axvline(x=0, color='k', linewidth=0.8)

        # Título
        self.ax.set_title('Trigonometric Circle', fontsize=16, fontweight='bold')

    def draw_circle(self):
        # Limpar gráfico anterior
        self.ax.clear()
        self.ax.set_xlim(-1.5, 1.5)
        self.ax.set_ylim(-1.5, 1.5)
        self.ax.set_aspect('equal')
        self.ax.grid(True, alpha=0.3)
        self.ax.axhline(y=0, color='k', linewidth=0.8)
        self.ax.axvline(x=0, color='k', linewidth=0.8)
        self.ax.set_title('Trigonometric Circle', fontsize=16, fontweight='bold')

        # Desenhar círculo unitário
        circle = patches.Circle((0, 0), 1, fill=False, color='blue', linewidth=2)
        self.ax.add_patch(circle)

        # Marcar ângulos principais
        main_angles = [0, 30, 45, 60, 90, 120, 135, 150, 180, 210, 225, 240, 270, 300, 315, 330]
        for angle_deg in main_angles:
            angle_rad = math.radians(angle_deg)
            x = math.cos(angle_rad)
            y = math.sin(angle_rad)

            # Ponto no círculo
            self.ax.plot(x, y, 'ko', markersize=4)

            # Label do ângulo
            label_x = x * 1.15
            label_y = y * 1.15
            self.ax.text(label_x, label_y, f'{angle_deg}°',
                        ha='center', va='center', fontsize=8)

        # Calcular posição atual
        angle_rad = math.radians(self.angle)
        x = math.cos(angle_rad)
        y = math.sin(angle_rad)

        # Ponto atual (menor)
        # Vulgo ponto vermelho
        self.ax.plot(x, y, 'ro', markersize=3, label=f'θ = {self.angle}°')

        # Linha do centro ao ponto
        self.ax.plot([0, x], [0, y], 'r-', linewidth=2)

        # Projeções (condicionais)
        if self.show_sin.get():
            self.ax.plot([x, x], [0, y], 'g--', linewidth=1.5, alpha=0.7, label=f'sin(θ) = {y:.3f}')
        if self.show_cos.get():
            self.ax.plot([0, x], [y, y], 'b--', linewidth=1.5, alpha=0.7, label=f'cos(θ) = {x:.3f}')

        # Arco do ângulo
        if self.angle != 0:
            arc_angles = np.linspace(0, angle_rad, 50)
            arc_x = 0.2 * np.cos(arc_angles)
            arc_y = 0.2 * np.sin(arc_angles)
            self.ax.plot(arc_x, arc_y, 'm-', linewidth=2)

        # Desenhar linha da tangente (condicional)
        if self.show_tan.get():
            if abs(x) > 1e-10:  # Evitar divisão por zero quando x ≈ 0
                # Linha tangente vertical em x = 1
                tan_value = y / x  # tan(θ) = sin(θ)/cos(θ)

                # Linha da tangente (vertical em x=1)
                self.ax.plot([1, 1], [0, tan_value], 'orange', linewidth=2, alpha=0.8, label=f'tan(θ) = {tan_value:.3f}')

                # Linha do centro até o ponto da tangente
                self.ax.plot([0, 1], [0, tan_value], 'orange', linewidth=1, alpha=0.5, linestyle='--')

                # Ponto na linha da tangente
                self.ax.plot(1, tan_value, 'o', color='orange', markersize=3)
            else:
                # Quando cos(θ) ≈ 0, tangente é infinita
                self.ax.axvline(x=1, ymin=0, ymax=1, color='orange', linewidth=2, alpha=0.8, label='tan(θ) = ∞')

        # Desenhar funções recíprocas (condicionais)
        # Cossecante (csc = 1/sin) - linha horizontal em y = 1/sin
        if self.show_csc.get() and abs(y) > 1e-10:
            csc_value = 1 / y
            if abs(csc_value) <= 5:  # Limitar valores muito grandes
                self.ax.plot([0, csc_value], [1, 1], 'purple', linewidth=2, alpha=0.7, label=f'csc(θ) = {csc_value:.3f}')
                self.ax.plot(csc_value, 1, 'o', color='purple', markersize=4)

        # Secante (sec = 1/cos) - linha vertical em x = 1/cos
        if self.show_sec.get() and abs(x) > 1e-10:
            sec_value = 1 / x
            if abs(sec_value) <= 5:  # Limitar valores muito grandes
                self.ax.plot([1, 1], [0, sec_value], 'darkviolet', linewidth=2, alpha=0.7, label=f'sec(θ) = {sec_value:.3f}')
                self.ax.plot(1, sec_value, 'o', color='darkviolet', markersize=4)

        # Cotangente (cot = cos/sin) - linha horizontal em y = cos/sin
        if self.show_cot.get() and abs(y) > 1e-10:
            cot_value = x / y
            if abs(cot_value) <= 5:  # Limitar valores muito grandes
                self.ax.plot([0, 1], [cot_value, cot_value], 'indigo', linewidth=2, alpha=0.7, label=f'cot(θ) = {cot_value:.3f}')
                self.ax.plot(1, cot_value, 'o', color='indigo', markersize=4)

        # Labels dos eixos
        self.ax.set_xlabel('cos(θ)', fontsize=12)
        self.ax.set_ylabel('sin(θ)', fontsize=12)

        # Legenda (mais à direita)
        self.ax.legend(loc='upper right', bbox_to_anchor=(1.755, 1))

    def update_values(self):
        # Calcular valores trigonométricos
        angle_rad = math.radians(self.angle)
        sin_val = math.sin(angle_rad)
        cos_val = math.cos(angle_rad)

        # Calcular tangente (cuidado com divisão por zero)
        if abs(cos_val) < 1e-10:
            tan_val = "∞" if sin_val > 0 else "-∞"
            tan_text = f"tan(θ) = {tan_val}"
        else:
            tan_val = math.tan(angle_rad)
            tan_text = f"tan(θ) = {tan_val:.3f}"

        # Calcular funções recíprocas
        # Cossecante (csc = 1/sin)
        if abs(sin_val) < 1e-10:
            csc_text = "csc(θ) = ∞"
        else:
            csc_val = 1 / sin_val
            csc_text = f"csc(θ) = {csc_val:.3f}"

        # Secante (sec = 1/cos)
        if abs(cos_val) < 1e-10:
            sec_text = "sec(θ) = ∞"
        else:
            sec_val = 1 / cos_val
            sec_text = f"sec(θ) = {sec_val:.3f}"

        # Cotangente (cot = cos/sin = 1/tan)
        if abs(sin_val) < 1e-10:
            cot_text = "cot(θ) = ∞"
        else:
            cot_val = cos_val / sin_val
            cot_text = f"cot(θ) = {cot_val:.3f}"

        # Atualizar labels das funções principais
        self.sin_label.config(text=f"sin(θ) = {sin_val:.3f}")
        self.cos_label.config(text=f"cos(θ) = {cos_val:.3f}")
        self.tan_label.config(text=tan_text)

        # Atualizar labels das funções recíprocas
        self.csc_label.config(text=csc_text)
        self.sec_label.config(text=sec_text)
        self.cot_label.config(text=cot_text)

    def update_plot(self):
        self.draw_circle()
        self.update_values()
        self.canvas.draw()

    def update_angle(self, event=None):
        try:
            self.angle = float(self.angle_var.get()) % 360
            self.angle_scale.set(self.angle)
            self.update_plot()
        except ValueError:
            pass

    def update_from_scale(self, value):
        self.angle = float(value)
        self.angle_var.set(f"{self.angle:.1f}")
        self.update_plot()

    def reset_angle(self):
        self.angle = 0
        self.angle_var.set("0")
        self.angle_scale.set(0)
        self.update_plot()

    def toggle_animation(self):
        if not self.animation_running:
            self.start_animation()
        else:
            self.stop_animation()

    def start_animation(self):
        self.animation_running = True
        self.animate()

    def stop_animation(self):
        self.animation_running = False

    def animate(self):
        if self.animation_running:
            self.angle = (self.angle + 2) % 360
            self.angle_var.set(f"{self.angle:.1f}")
            self.angle_scale.set(self.angle)
            self.update_plot()
            self.root.after(50, self.animate)

    def run(self):
        self.root.mainloop()

if __name__ == "__main__":
    app = CirculoTrigonometrico()
    app.run()
