import pandas as pd
import numpy as np
from sklearn.preprocessing import LabelEncoder
from tensorflow.keras.preprocessing.text import Tokenizer
from tensorflow.keras.preprocessing.sequence import pad_sequences
from sklearn.model_selection import train_test_split
import pickle
import os
import time
import argparse
import sys

# Obter o diretório do script atual
script_dir = os.path.dirname(os.path.abspath(__file__))
# Definir caminhos absolutos baseados na localização do script
data_dir = os.path.join(script_dir, "..", "data")
processed_dir = os.path.join(data_dir, "processed")
models_dir = os.path.join(script_dir, "..", "models")

# Load the dataset
def preprocess_data(sample_size=None, max_name_length=None, save_processed=True, balanced_sample=True, samples_per_country=1000, use_raw_files=True):
    """
    Pré-processa os dados para treinamento do modelo.

    Args:
        sample_size (int, optional): Número de amostras a serem usadas (para testes)
        max_name_length (int, optional): Comprimento máximo dos nomes (para limitar o tamanho do modelo)
        save_processed (bool): Se True, salva os dados processados em arquivos
        balanced_sample (bool): Se True, cria uma amostra balanceada com o mesmo número de exemplos por país
        samples_per_country (int): Número de amostras por país quando balanced_sample=True
        use_raw_files (bool): Se True, usa os arquivos CSV originais em vez do arquivo consolidado

    Returns:
        tuple: (x_train, x_val, x_test, y_train, y_val, y_test, max_len, num_classes)
    """
    try:
        print("Iniciando pré-processamento dos dados...")
        start_time = time.time()

        # Verificar se queremos usar os arquivos CSV originais
        if use_raw_files:
            print("Usando arquivos CSV originais...")
            raw_dir = os.path.join(data_dir, "raw")

            # Listar todos os arquivos CSV na pasta raw
            csv_files = [f for f in os.listdir(raw_dir) if f.endswith('.csv')]
            print(f"Encontrados {len(csv_files)} arquivos CSV na pasta raw.")

            # Inicializar um DataFrame vazio para armazenar a amostra balanceada
            balanced_df = pd.DataFrame()

            # Dicionário para rastrear quantos exemplos já temos de cada país
            country_counts = {}

            # Processar cada arquivo CSV
            for csv_file in csv_files:
                country_code = csv_file.replace('.csv', '')
                print(f"Processando {csv_file}...")

                # Verificar se já temos amostras suficientes deste país
                if balanced_sample and country_counts.get(country_code, 0) >= samples_per_country:
                    print(f"  Já temos amostras suficientes de {country_code}. Pulando...")
                    continue

                # Calcular quantos exemplos ainda precisamos
                samples_needed = samples_per_country if balanced_sample else None
                if balanced_sample:
                    samples_needed = samples_per_country - country_counts.get(country_code, 0)

                try:
                    # Ler o arquivo CSV em chunks para economizar memória
                    file_path = os.path.join(raw_dir, csv_file)

                    # Determinar o número de linhas a ler
                    if balanced_sample and samples_needed is not None:
                        # Ler um pouco mais para ter margem de segurança
                        nrows = min(samples_needed * 2, 10000)
                    else:
                        # Ler um número fixo de linhas para teste
                        nrows = 10000 if sample_size else None

                    # Ler o arquivo CSV
                    try:
                        # Tentar ler com cabeçalho
                        df_country = pd.read_csv(file_path,
                                                encoding='utf-8',
                                                nrows=nrows)

                        # Verificar se as colunas esperadas estão presentes
                        if 'First Name' in df_country.columns and 'Last Name' in df_country.columns:
                            # Renomear colunas para o formato esperado
                            df_country = df_country.rename(columns={
                                'First Name': 'first_name',
                                'Last Name': 'last_name',
                                'Gender': 'gender',
                                'Country': 'country'
                            })
                        elif 'first_name' not in df_country.columns:
                            # Se não tiver as colunas esperadas, tentar ler sem cabeçalho
                            df_country = pd.read_csv(file_path,
                                                    encoding='utf-8',
                                                    header=None,
                                                    names=['first_name', 'last_name', 'gender', 'country'],
                                                    nrows=nrows)
                    except Exception as e:
                        print(f"  Erro ao ler {csv_file}: {e}")
                        print("  Tentando ler sem cabeçalho...")
                        df_country = pd.read_csv(file_path,
                                                encoding='utf-8',
                                                header=None,
                                                names=['first_name', 'last_name', 'gender', 'country'],
                                                nrows=nrows)

                    # Adicionar coluna de nacionalidade
                    df_country['nacionality'] = country_code

                    # Criar coluna 'name'
                    df_country['name'] = df_country['first_name'] + ' ' + df_country['last_name']

                    # Limpar dados
                    df_country = df_country.dropna(subset=['name'])
                    df_country['name'] = df_country['name'].str.strip()

                    # Selecionar amostras aleatórias se necessário
                    if balanced_sample and len(df_country) > samples_needed:
                        df_country = df_country.sample(samples_needed, random_state=42)

                    # Adicionar à amostra balanceada
                    balanced_df = pd.concat([balanced_df, df_country])

                    # Atualizar o contador
                    country_counts[country_code] = country_counts.get(country_code, 0) + len(df_country)

                    print(f"  Adicionados {len(df_country)} exemplos de {country_code}. Total: {country_counts.get(country_code, 0)}")

                except Exception as e:
                    print(f"  Erro ao processar {csv_file}: {e}")

            # Usar a amostra balanceada
            df = balanced_df
            print(f"Amostra criada com {len(df)} registros de {len(country_counts)} países.")
            for country, count in sorted(country_counts.items()):
                print(f"  {country}: {count} exemplos")
        else:
            # Carregar dados consolidados
            print("Carregando dados consolidados...")
            input_file = os.path.join(processed_dir, "nomes_consolidado.csv")
            print(f"Arquivo de entrada: {input_file}")

            # Verificar se queremos uma amostra balanceada
            if balanced_sample:
                print(f"Criando amostra balanceada com {samples_per_country} exemplos por país...")

                # Ler apenas as colunas necessárias para economizar memória
                df_chunks = pd.read_csv(input_file, encoding='utf-8', chunksize=1000000)

                # Inicializar um DataFrame vazio para armazenar a amostra balanceada
                balanced_df = pd.DataFrame()

                # Dicionário para rastrear quantos exemplos já temos de cada país
                country_counts = {}

                # Processar o arquivo em chunks para economizar memória
                for chunk in df_chunks:
                    # Para cada país no chunk
                    for country in chunk['nacionality'].unique():
                        # Se ainda não temos amostras suficientes deste país
                        if country_counts.get(country, 0) < samples_per_country:
                            # Selecionar exemplos deste país
                            country_samples = chunk[chunk['nacionality'] == country]

                            # Calcular quantos exemplos ainda precisamos
                            samples_needed = samples_per_country - country_counts.get(country, 0)

                            # Selecionar o número necessário de exemplos (ou todos, se houver menos)
                            if len(country_samples) <= samples_needed:
                                selected_samples = country_samples
                            else:
                                selected_samples = country_samples.sample(samples_needed, random_state=42)

                            # Adicionar à amostra balanceada
                            balanced_df = pd.concat([balanced_df, selected_samples])

                            # Atualizar o contador
                            country_counts[country] = country_counts.get(country, 0) + len(selected_samples)

                    # Verificar se já temos amostras suficientes de todos os países
                    if all(count >= samples_per_country for count in country_counts.values()) and len(country_counts) > 0:
                        break

                # Usar a amostra balanceada
                df = balanced_df
                print(f"Amostra balanceada criada com {len(df)} registros de {len(country_counts)} países.")
                for country, count in sorted(country_counts.items()):
                    print(f"  {country}: {count} exemplos")
            else:
                # Carregar todo o dataset
                df = pd.read_csv(input_file, encoding='utf-8')

        # Informações sobre o dataset
        print(f"Dataset carregado com {len(df)} registros e {df['nacionality'].nunique()} nacionalidades.")

        # Validar dados
        if df.empty:
            raise ValueError("Dataset está vazio")

        if df['name'].isnull().any():
            print("Aviso: Removendo registros com nomes nulos")
            df = df.dropna(subset=['name'])

        # Usar apenas uma amostra para testes, se especificado e não estamos usando amostra balanceada
        if not balanced_sample and sample_size and isinstance(sample_size, int) and sample_size > 0:
            if sample_size < len(df):
                df = df.sample(sample_size, random_state=42)
                print(f"Usando amostra de {sample_size} registros para teste.")

        # Preprocessamento dos nomes
        print("Pré-processando nomes...")
        df['name'] = df['name'].str.lower()  # Converter para minúsculas
        df['name'] = df['name'].str.strip()  # Remover espaços extras

        # Limitar o comprimento dos nomes, se especificado
        if max_name_length and isinstance(max_name_length, int) and max_name_length > 0:
            df['name'] = df['name'].str[:max_name_length]
            print(f"Limitando nomes a {max_name_length} caracteres.")

        # Encoding das nacionalidades
        print("Codificando nacionalidades...")
        label_encoder = LabelEncoder()
        df['nacionality_encoded'] = label_encoder.fit_transform(df['nacionality'])
        num_classes = len(label_encoder.classes_)
        print(f"Total de classes (nacionalidades): {num_classes}")

        # Tokenização melhorada
        print("Tokenizando nomes...")
        tokenizer = Tokenizer(char_level=True, filters='!"#$%&()*+,-./:;<=>?@[\\]^_`{|}~\t\n')
        tokenizer.fit_on_texts(df['name'])
        sequences = tokenizer.texts_to_sequences(df['name'])

        # Padding com comprimento máximo dinâmico
        name_lengths = [len(name) for name in df['name']]
        max_len = max(name_lengths)
        avg_len = sum(name_lengths) / len(name_lengths)
        print(f"Comprimento máximo dos nomes: {max_len}")
        print(f"Comprimento médio dos nomes: {avg_len:.2f}")

        print("Aplicando padding às sequências...")
        x = pad_sequences(sequences, maxlen=max_len)
        y = df['nacionality_encoded'].values

        # Dividir em conjuntos de treino, validação e teste
        print("Dividindo em conjuntos de treino, validação e teste...")

        # Verificar se temos exemplos suficientes para estratificação
        # Contar o número de exemplos por classe
        class_counts = np.bincount(y)
        min_samples_per_class = np.min(class_counts[class_counts > 0])

        if min_samples_per_class >= 3:
            # Se tivermos pelo menos 3 exemplos por classe, podemos usar stratify
            print("Usando divisão estratificada...")
            x_train, x_temp, y_train, y_temp = train_test_split(x, y, test_size=0.3, random_state=42, stratify=y)
            x_val, x_test, y_val, y_test = train_test_split(x_temp, y_temp, test_size=0.5, random_state=42, stratify=y_temp)
        else:
            # Caso contrário, usamos divisão aleatória
            print("Usando divisão aleatória (algumas classes têm poucos exemplos)...")
            x_train, x_temp, y_train, y_temp = train_test_split(x, y, test_size=0.3, random_state=42)
            x_val, x_test, y_val, y_test = train_test_split(x_temp, y_temp, test_size=0.5, random_state=42)

        print(f"Conjunto de treino: {len(x_train)} amostras")
        print(f"Conjunto de validação: {len(x_val)} amostras")
        print(f"Conjunto de teste: {len(x_test)} amostras")

        # Salvar preprocessadores e dados processados
        if save_processed:
            print("Salvando preprocessadores e dados processados...")
            os.makedirs(models_dir, exist_ok=True)
            os.makedirs(processed_dir, exist_ok=True)

            # Salvar preprocessadores
            tokenizer_file = os.path.join(models_dir, "tokenizer.pkl")
            with open(tokenizer_file, 'wb') as f:
                pickle.dump(tokenizer, f)

            label_encoder_file = os.path.join(models_dir, "label_encoder.pkl")
            with open(label_encoder_file, 'wb') as f:
                pickle.dump(label_encoder, f)

            # Salvar informações sobre o modelo
            model_info = {
                'max_len': max_len,
                'num_classes': num_classes,
                'vocab_size': len(tokenizer.word_index) + 1,
                'nacionalities': list(label_encoder.classes_)
            }
            model_info_file = os.path.join(models_dir, "model_info.pkl")
            with open(model_info_file, 'wb') as f:
                pickle.dump(model_info, f)

            # Salvar dados processados (opcional, pode consumir muito espaço)
            print("Salvando dados processados...")
            np.save(os.path.join(processed_dir, "x_train.npy"), x_train)
            np.save(os.path.join(processed_dir, "y_train.npy"), y_train)
            np.save(os.path.join(processed_dir, "x_val.npy"), x_val)
            np.save(os.path.join(processed_dir, "y_val.npy"), y_val)
            np.save(os.path.join(processed_dir, "x_test.npy"), x_test)
            np.save(os.path.join(processed_dir, "y_test.npy"), y_test)

        total_time = time.time() - start_time
        print(f"Pré-processamento concluído em {total_time:.2f} segundos")

        return x_train, x_val, x_test, y_train, y_val, y_test, max_len, num_classes

    except Exception as e:
        print(f"Erro no pré-processamento: {e}")
        raise

if __name__ == "__main__":
    # Configurar argumentos de linha de comando
    parser = argparse.ArgumentParser(description='Pré-processar dados para treinamento do modelo.')
    parser.add_argument('--sample', type=int, help='Número de amostras a serem usadas (para testes)')
    parser.add_argument('--max-length', type=int, help='Comprimento máximo dos nomes (para limitar o tamanho do modelo)')
    parser.add_argument('--no-save', action='store_true', help='Não salvar os dados processados')
    parser.add_argument('--no-balanced', action='store_true', help='Não usar amostra balanceada')
    parser.add_argument('--samples-per-country', type=int, default=1000,
                        help='Número de amostras por país quando usando amostra balanceada')
    parser.add_argument('--no-raw-files', action='store_true',
                        help='Não usar arquivos CSV originais (usar arquivo consolidado)')

    args = parser.parse_args()

    # Processar dados com base nos argumentos fornecidos
    x_train, x_val, x_test, y_train, y_val, y_test, max_len, num_classes = preprocess_data(
        sample_size=args.sample,
        max_name_length=args.max_length,
        save_processed=not args.no_save,
        balanced_sample=not args.no_balanced,
        samples_per_country=args.samples_per_country,
        use_raw_files=not args.no_raw_files
    )

    print("Pré-processamento concluído com sucesso.")
    print(f"Dimensões dos dados de treino: {x_train.shape}")
    print(f"Número de classes: {num_classes}")
    print(f"Comprimento máximo dos nomes: {max_len}")
