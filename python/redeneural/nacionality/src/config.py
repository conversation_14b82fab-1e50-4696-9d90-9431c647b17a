import os

CONFIG = {
    'data': {
        'raw_dir': '../data/raw',
        'processed_dir': '../data/processed',
        'encoding': 'utf-8',
    },
    'model': {
        'embedding_dim': 32,
        'lstm_units': [128, 64],
        'dense_units': [128],
        'dropout_rates': [0.3, 0.3, 0.5],
    },
    'training': {
        'batch_size': 32,
        'epochs': 50,
        'validation_split': 0.2,
        'random_state': 42,
    },
    'paths': {
        'model_dir': '../models',
        'tokenizer_path': '../models/tokenizer.pkl',
        'label_encoder_path': '../models/label_encoder.pkl',
        'model_path': '../models/model.h5',
    }
}

# Criar diretórios necessários
for dir_path in [CONFIG['data']['raw_dir'], CONFIG['data']['processed_dir'], CONFIG['paths']['model_dir']]:
    os.makedirs(dir_path, exist_ok=True)