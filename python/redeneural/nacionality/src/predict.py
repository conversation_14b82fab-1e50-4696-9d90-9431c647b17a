#!/usr/bin/env python3
# predict.py - Fazer previsões com o modelo treinado
import numpy as np
import os
import pickle
import argparse
import time
import tensorflow as tf
from tensorflow.keras.models import load_model
from tensorflow.keras.preprocessing.sequence import pad_sequences

# Configurar TensorFlow para inferência eficiente
def configure_tensorflow_for_inference():
    """Configura o TensorFlow para inferência eficiente no MacBook."""
    # Desabilitar XLA para evitar erros
    try:
        tf.config.optimizer.set_jit(False)
        print("Compilador XLA desabilitado para evitar erros")
    except:
        print("Não foi possível configurar o compilador XLA")

    # Verificar se estamos em um Mac com Apple Silicon
    try:
        import platform
        if platform.system() == "Darwin" and platform.processor() == "arm":
            print("Detectado Mac com Apple Silicon, aplicando otimizações específicas")
            os.environ['TF_ENABLE_ONEDNN_OPTS'] = '0'
    except:
        pass

# Obter o diretório do script atual
script_dir = os.path.dirname(os.path.abspath(__file__))
# Definir caminhos absolutos baseados na localização do script
models_dir = os.path.join(script_dir, "..", "models")

# Dicionário de mapeamento de códigos de país para nomes completos
COUNTRY_NAMES = {
    'AF': 'Afghanistan',
    'AL': 'Albania',
    'DZ': 'Algeria',
    'AO': 'Angola',
    'AR': 'Argentina',
    'AT': 'Austria',
    'AZ': 'Azerbaijan',
    'BD': 'Bangladesh',
    'BE': 'Belgium',
    'BF': 'Burkina Faso',
    'BG': 'Bulgaria',
    'BH': 'Bahrain',
    'BI': 'Burundi',
    'BN': 'Brunei',
    'BO': 'Bolivia',
    'BR': 'Brazil',
    'BW': 'Botswana',
    'CA': 'Canada',
    'CH': 'Switzerland',
    'CL': 'Chile',
    'CM': 'Cameroon',
    'CN': 'China',
    'CO': 'Colombia',
    'CR': 'Costa Rica',
    'CY': 'Cyprus',
    'CZ': 'Czech Republic',
    'DE': 'Germany',
    'DJ': 'Djibouti',
    'DK': 'Denmark',
    'EC': 'Ecuador',
    'EE': 'Estonia',
    'EG': 'Egypt',
    'ES': 'Spain',
    'ET': 'Ethiopia',
    'FI': 'Finland',
    'FJ': 'Fiji',
    'FR': 'France',
    'GB': 'United Kingdom',
    'GE': 'Georgia',
    'GH': 'Ghana',
    'GR': 'Greece',
    'GT': 'Guatemala',
    'HK': 'Hong Kong',
    'HN': 'Honduras',
    'HR': 'Croatia',
    'HT': 'Haiti',
    'HU': 'Hungary',
    'ID': 'Indonesia',
    'IE': 'Ireland',
    'IL': 'Israel',
    'IN': 'India',
    'IQ': 'Iraq',
    'IR': 'Iran',
    'IS': 'Iceland',
    'IT': 'Italy',
    'JM': 'Jamaica',
    'JO': 'Jordan',
    'JP': 'Japan',
    'KH': 'Cambodia',
    'KR': 'South Korea',
    'KW': 'Kuwait',
    'KZ': 'Kazakhstan',
    'LB': 'Lebanon',
    'LT': 'Lithuania',
    'LU': 'Luxembourg',
    'LY': 'Libya',
    'MA': 'Morocco',
    'MD': 'Moldova',
    'MO': 'Macau',
    'MT': 'Malta',
    'MU': 'Mauritius',
    'MV': 'Maldives',
    'MX': 'Mexico',
    'MY': 'Malaysia',
    'NA': 'Namibia',
    'NG': 'Nigeria',
    'NL': 'Netherlands',
    'NO': 'Norway',
    'OM': 'Oman',
    'PA': 'Panama',
    'PE': 'Peru',
    'PH': 'Philippines',
    'PL': 'Poland',
    'PR': 'Puerto Rico',
    'PS': 'Palestine',
    'PT': 'Portugal',
    'QA': 'Qatar',
    'RS': 'Serbia',
    'RU': 'Russia',
    'SA': 'Saudi Arabia',
    'SD': 'Sudan',
    'SE': 'Sweden',
    'SG': 'Singapore',
    'SI': 'Slovenia',
    'SV': 'El Salvador',
    'SY': 'Syria',
    'TM': 'Turkmenistan',
    'TN': 'Tunisia',
    'TR': 'Turkey',
    'TW': 'Taiwan',
    'US': 'United States',
    'UY': 'Uruguay',
    'YE': 'Yemen',
    'ZA': 'South Africa',
    'AE': 'United Arab Emirates'
}

def load_preprocessors():
    """
    Carrega os preprocessadores salvos.
    """
    tokenizer_path = os.path.join(models_dir, "tokenizer.pkl")
    label_encoder_path = os.path.join(models_dir, "label_encoder.pkl")
    model_info_path = os.path.join(models_dir, "model_info.pkl")

    if not os.path.exists(tokenizer_path) or not os.path.exists(label_encoder_path):
        raise FileNotFoundError("Arquivos de preprocessamento não encontrados. Execute preprocess.py e train.py primeiro.")

    with open(tokenizer_path, 'rb') as f:
        tokenizer = pickle.load(f)

    with open(label_encoder_path, 'rb') as f:
        label_encoder = pickle.load(f)

    # Tentar carregar model_info se existir
    model_info = None
    if os.path.exists(model_info_path):
        with open(model_info_path, 'rb') as f:
            model_info = pickle.load(f)

    return tokenizer, label_encoder, model_info

def load_trained_model():
    """
    Carrega o modelo treinado com otimizações para inferência no MacBook.
    """
    start_time = time.time()

    # Configurar TensorFlow para inferência eficiente
    configure_tensorflow_for_inference()

    # Caminhos possíveis para o modelo
    best_model_path = os.path.join(models_dir, "best_model.h5")
    final_model_path = os.path.join(models_dir, "final_model.h5")
    model_path = os.path.join(models_dir, "model.h5")

    # Verificar qual modelo usar
    if os.path.exists(best_model_path):
        model_file = best_model_path
        model_name = "best_model.h5"
    elif os.path.exists(final_model_path):
        model_file = final_model_path
        model_name = "final_model.h5"
    elif os.path.exists(model_path):
        model_file = model_path
        model_name = "model.h5"
    else:
        raise FileNotFoundError("Modelo treinado não encontrado. Execute train.py primeiro.")

    # Carregar o modelo com otimizações
    try:
        # Carregar sem compilação para inferência mais rápida
        model = load_model(model_file, compile=False)

        # Pré-compilar a função de previsão
        try:
            model.make_predict_function()
        except:
            print("Aviso: Não foi possível pré-compilar a função de previsão")

        load_time = time.time() - start_time
        print(f"Modelo carregado: {model_name} em {load_time:.2f} segundos")

    except Exception as e:
        print(f"Erro ao carregar o modelo: {e}")
        model = load_model(model_file)
        print(f"Modelo carregado: {model_name} (modo padrão)")

    return model

def preprocess_name(name, tokenizer, max_len=None):
    """
    Pré-processa um nome para previsão.
    """
    # Converter para minúsculas e remover espaços extras
    name = name.lower().strip()

    # Tokenizar
    sequence = tokenizer.texts_to_sequences([name])

    # Padding
    if max_len is None:
        # Se max_len não for fornecido, usar o tamanho do modelo
        model = load_trained_model()
        max_len = model.input_shape[1]

    padded = pad_sequences(sequence, maxlen=max_len)

    return padded

def predict_nationality(name, model=None, tokenizer=None, label_encoder=None, max_len=None,
                     top_k=3, use_full_names=True, show_all=False, normalize=True):
    """
    Prevê a nacionalidade de um nome com otimizações para MacBook.

    Args:
        name (str): Nome para prever a nacionalidade
        model: Modelo treinado (opcional)
        tokenizer: Tokenizer para pré-processamento (opcional)
        label_encoder: Label encoder para decodificar as previsões (opcional)
        max_len (int): Comprimento máximo para padding (opcional)
        top_k (int): Número de previsões a retornar
        use_full_names (bool): Se True, retorna os nomes completos dos países
        show_all (bool): Se True, retorna todas as nacionalidades
        normalize (bool): Se True, normaliza as probabilidades das top-k nacionalidades para somarem 100%

    Returns:
        dict: Dicionário com nacionalidades e probabilidades
        float: Soma total das probabilidades originais
        float: Soma das probabilidades mostradas (100% se normalize=True)
    """
    start_time = time.time()

    # Carregar recursos se não fornecidos
    if tokenizer is None or label_encoder is None:
        tokenizer, label_encoder, model_info = load_preprocessors()
        if model_info and max_len is None:
            max_len = model_info.get('max_len')

    if model is None:
        model = load_trained_model()

    # Pré-processar o nome
    processed_name = preprocess_name(name, tokenizer, max_len)

    # Fazer previsão com otimizações
    # Usar predict_on_batch para inferência mais rápida de um único exemplo
    prediction = model.predict_on_batch(processed_name)[0]

    # Calcular a soma total das probabilidades
    total_prob = float(np.sum(prediction)) * 100

    # Determinar quantas classes mostrar
    num_classes = len(prediction) if show_all else min(top_k, len(prediction))

    # Obter as top-k classes
    if show_all:
        # Mostrar todas as classes
        indices = np.arange(len(prediction))
        probs = prediction
    else:
        # Obter apenas as top-k classes
        indices = prediction.argsort()[-num_classes:][::-1]
        probs = prediction[indices]

    # Calcular a soma das probabilidades selecionadas
    selected_probs_sum = float(np.sum(probs)) * 100

    # Normalizar as probabilidades para somarem 100% se solicitado
    if normalize and not show_all and not np.isclose(selected_probs_sum, 0):
        # Normalizar apenas se não estiver mostrando todas as classes
        normalized_probs = probs / np.sum(probs)
        probs = normalized_probs

    # Decodificar as nacionalidades
    nationalities = label_encoder.inverse_transform(indices)

    # Formatar resultados
    resultados = {}
    for nationality, prob in zip(nationalities, probs):
        # Usar nome completo do país se disponível e solicitado
        if use_full_names and nationality in COUNTRY_NAMES:
            country_name = COUNTRY_NAMES[nationality]
            # Multiplicar por 100 se não normalizado, ou por 100 se normalizado
            prob_value = float(prob) * 100
            resultados[f"{country_name} ({nationality})"] = prob_value
        else:
            prob_value = float(prob) * 100
            resultados[nationality] = prob_value

    # Medir tempo de previsão
    prediction_time = time.time() - start_time
    if prediction_time > 0.1:  # Mostrar tempo apenas se for significativo
        print(f"Previsão realizada em {prediction_time:.3f} segundos")

    # Retornar também a soma das probabilidades mostradas
    shown_prob_sum = 100.0 if normalize and not show_all else selected_probs_sum

    return resultados, total_prob, shown_prob_sum

def predict_batch(names, model=None, tokenizer=None, label_encoder=None, max_len=None,
                top_k=1, use_full_names=True, show_all=False, batch_size=32):
    """
    Prevê a nacionalidade de vários nomes com otimizações para MacBook.

    Args:
        names (list): Lista de nomes para prever a nacionalidade
        model: Modelo treinado (opcional)
        tokenizer: Tokenizer para pré-processamento (opcional)
        label_encoder: Label encoder para decodificar as previsões (opcional)
        max_len (int): Comprimento máximo para padding (opcional)
        top_k (int): Número de previsões a retornar
        use_full_names (bool): Se True, retorna os nomes completos dos países
        show_all (bool): Se True, retorna todas as nacionalidades
        batch_size (int): Tamanho do lote para previsões em batch

    Returns:
        list: Lista de tuplas (nome, previsões, soma_total)
    """
    start_time = time.time()

    # Verificar se há nomes para processar
    if not names:
        return []

    # Carregar recursos se não fornecidos
    if tokenizer is None or label_encoder is None:
        tokenizer, label_encoder, model_info = load_preprocessors()
        if model_info and max_len is None:
            max_len = model_info.get('max_len')

    if model is None:
        model = load_trained_model()

    # Determinar o número de nomes a processar
    num_names = len(names)
    print(f"Processando {num_names} nomes...")

    # Processamento em lote para melhor desempenho
    if num_names > 1:
        # Pré-processar todos os nomes de uma vez
        processed_names = []
        for name in names:
            name = name.lower().strip()
            sequence = tokenizer.texts_to_sequences([name])
            padded = pad_sequences(sequence, maxlen=max_len)
            processed_names.append(padded[0])

        # Converter para array numpy
        processed_batch = np.array(processed_names)

        # Fazer previsões em lotes para melhor desempenho
        results = []
        for i in range(0, num_names, batch_size):
            batch = processed_batch[i:i+batch_size]

            # Mostrar progresso
            print(f"Processando lote {i//batch_size + 1}/{(num_names + batch_size - 1)//batch_size}...", end="\r")

            # Fazer previsões para o lote atual
            batch_predictions = model.predict(batch, verbose=0)

            # Processar cada previsão no lote
            for j, prediction in enumerate(batch_predictions):
                name_index = i + j
                if name_index >= num_names:
                    break

                name = names[name_index]

                # Calcular a soma total das probabilidades
                total_prob = float(np.sum(prediction)) * 100

                # Determinar quantas classes mostrar
                num_classes = len(prediction) if show_all else min(top_k, len(prediction))

                # Obter as top-k classes
                if show_all:
                    indices = np.arange(len(prediction))
                    probs = prediction
                else:
                    indices = prediction.argsort()[-num_classes:][::-1]
                    probs = prediction[indices]

                # Decodificar as nacionalidades
                nationalities = label_encoder.inverse_transform(indices)

                # Formatar resultados
                resultados = {}
                for nationality, prob in zip(nationalities, probs):
                    if use_full_names and nationality in COUNTRY_NAMES:
                        country_name = COUNTRY_NAMES[nationality]
                        resultados[f"{country_name} ({nationality})"] = float(prob) * 100
                    else:
                        resultados[nationality] = float(prob) * 100

                results.append((name, resultados, total_prob))

        # Limpar a linha de progresso
        print(" " * 80, end="\r")
    else:
        # Processamento individual para um único nome
        results = []
        prediction, total_prob = predict_nationality(
            names[0], model, tokenizer, label_encoder, max_len,
            top_k, use_full_names, show_all
        )
        results.append((names[0], prediction, total_prob))

    # Mostrar tempo total
    total_time = time.time() - start_time
    print(f"Processamento concluído em {total_time:.2f} segundos ({total_time/num_names:.4f} segundos por nome)")

    return results

def interactive_mode(use_full_names=True, show_all=False, top_k=3, normalize=True):
    """
    Modo interativo para previsão de nacionalidade.

    Args:
        use_full_names (bool): Se True, mostra os nomes completos dos países
        show_all (bool): Se True, mostra todas as nacionalidades
        top_k (int): Número de previsões a mostrar
        normalize (bool): Se True, normaliza as probabilidades das top-k nacionalidades para somarem 100%
    """
    print("Carregando recursos...")
    tokenizer, label_encoder, model_info = load_preprocessors()
    model = load_trained_model()
    max_len = model_info.get('max_len') if model_info else None

    print("\nModo interativo iniciado. Digite 'sair' para terminar.")

    while True:
        nome = input("\nDigite um nome (ou 'sair' para terminar): ")
        if nome.lower() == 'sair':
            break

        resultados, total_prob, shown_prob_sum = predict_nationality(
            nome, model, tokenizer, label_encoder, max_len,
            top_k=top_k, use_full_names=use_full_names, show_all=show_all, normalize=normalize
        )

        print(f"\nPrevisões para '{nome}':")
        for nacionalidade, prob in sorted(resultados.items(), key=lambda x: x[1], reverse=True):
            print(f"  {nacionalidade}: {prob:.2f}%")

        # Mostrar a soma das probabilidades exibidas
        if normalize and not show_all:
            print(f"\nSoma das probabilidades mostradas: {shown_prob_sum:.2f}% (normalizado para 100%)")
        else:
            print(f"\nSoma das probabilidades mostradas: {shown_prob_sum:.2f}%")

        # Mostrar a soma total original apenas se for diferente da soma mostrada
        if not np.isclose(total_prob, shown_prob_sum):
            print(f"Soma total de todas as probabilidades (incluindo não mostradas): {total_prob:.2f}%")

def main():
    parser = argparse.ArgumentParser(description='Prever nacionalidade a partir de nomes.')

    # Opções básicas
    parser.add_argument('--name', type=str, help='Nome para prever a nacionalidade')
    parser.add_argument('--file', type=str, help='Arquivo com nomes (um por linha)')
    parser.add_argument('--top-k', type=int, default=3, help='Número de previsões a retornar')
    parser.add_argument('--interactive', action='store_true', help='Modo interativo')
    parser.add_argument('--no-full-names', action='store_true', help='Não mostrar nomes completos dos países')
    parser.add_argument('--show-all', action='store_true', help='Mostrar todas as nacionalidades')
    parser.add_argument('--no-normalize', action='store_true', help='Não normalizar as probabilidades para somarem 100%')
    parser.add_argument('--batch-size', type=int, default=32, help='Tamanho do lote para processamento em batch')
    parser.add_argument('--output', type=str, help='Arquivo para salvar os resultados')

    args = parser.parse_args()

    # Determinar se deve usar nomes completos dos países
    use_full_names = not args.no_full_names
    # Determinar se deve normalizar as probabilidades
    normalize = not args.no_normalize

    # Modo interativo
    if args.interactive or (not args.name and not args.file):
        print("\n" + "="*50)
        print("MODO INTERATIVO DE PREVISÃO DE NACIONALIDADE")
        print("="*50)
        interactive_mode(use_full_names=use_full_names, show_all=args.show_all, top_k=args.top_k, normalize=normalize)
        return

    # Carregar preprocessadores e modelo
    print("Carregando recursos...")
    tokenizer, label_encoder, model_info = load_preprocessors()
    model = load_trained_model()
    max_len = model_info.get('max_len') if model_info else None

    # Preparar arquivo de saída se especificado
    output_file = None
    if args.output:
        try:
            output_file = open(args.output, 'w', encoding='utf-8')
            output_file.write("nome,nacionalidade,probabilidade,soma_mostrada,soma_total\n")
            print(f"Resultados serão salvos em: {args.output}")
        except Exception as e:
            print(f"Erro ao abrir arquivo de saída: {e}")
            output_file = None

    try:
        # Fazer previsões
        if args.name:
            # Prever um único nome
            print("\n" + "="*50)
            print(f"PREVISÃO PARA: {args.name}")
            print("="*50)

            resultados, total_prob, shown_prob_sum = predict_nationality(
                args.name, model, tokenizer, label_encoder, max_len,
                args.top_k, use_full_names, args.show_all, normalize
            )

            print(f"\nPrevisões para '{args.name}':")
            for nacionalidade, prob in sorted(resultados.items(), key=lambda x: x[1], reverse=True):
                print(f"  {nacionalidade}: {prob:.2f}%")

                # Salvar no arquivo de saída se especificado
                if output_file:
                    output_file.write(f"{args.name},{nacionalidade},{prob:.2f},{shown_prob_sum:.2f},{total_prob:.2f}\n")

            # Mostrar a soma das probabilidades exibidas
            if normalize and not args.show_all:
                print(f"\nSoma das probabilidades mostradas: {shown_prob_sum:.2f}% (normalizado para 100%)")
            else:
                print(f"\nSoma das probabilidades mostradas: {shown_prob_sum:.2f}%")

            # Mostrar a soma total original apenas se for diferente da soma mostrada
            if not np.isclose(total_prob, shown_prob_sum):
                print(f"Soma total de todas as probabilidades (incluindo não mostradas): {total_prob:.2f}%")

        if args.file:
            # Prever nomes de um arquivo
            if not os.path.exists(args.file):
                print(f"Arquivo {args.file} não encontrado.")
                return

            with open(args.file, 'r', encoding='utf-8') as f:
                names = [line.strip() for line in f if line.strip()]

            print("\n" + "="*50)
            print(f"PROCESSANDO {len(names)} NOMES DO ARQUIVO: {args.file}")
            print("="*50)

            # Precisamos atualizar a função predict_batch para suportar normalização
            # Por enquanto, vamos processar cada nome individualmente
            results = []
            for name in names:
                resultados, total_prob, shown_prob_sum = predict_nationality(
                    name, model, tokenizer, label_encoder, max_len,
                    args.top_k, use_full_names, args.show_all, normalize
                )
                results.append((name, resultados, total_prob, shown_prob_sum))

            # Mostrar resultados
            for name, predictions, total_prob, shown_prob_sum in results:
                print(f"\nPrevisões para '{name}':")
                for nacionalidade, prob in sorted(predictions.items(), key=lambda x: x[1], reverse=True):
                    print(f"  {nacionalidade}: {prob:.2f}%")

                # Mostrar a soma das probabilidades exibidas
                if normalize and not args.show_all:
                    print(f"Soma das probabilidades mostradas: {shown_prob_sum:.2f}% (normalizado para 100%)")
                else:
                    print(f"Soma das probabilidades mostradas: {shown_prob_sum:.2f}%")

                # Mostrar a soma total original apenas se for diferente da soma mostrada
                if not np.isclose(total_prob, shown_prob_sum):
                    print(f"Soma total de todas as probabilidades: {total_prob:.2f}%")

            # Salvar resultados no arquivo de saída se especificado
            if output_file:
                for name, predictions, total_prob, shown_prob_sum in results:
                    for nacionalidade, prob in sorted(predictions.items(), key=lambda x: x[1], reverse=True):
                        output_file.write(f"{name},{nacionalidade},{prob:.2f},{shown_prob_sum:.2f},{total_prob:.2f}\n")

                print(f"Resultados salvos em: {args.output}")

    finally:
        # Fechar arquivo de saída se foi aberto
        if output_file:
            output_file.close()

if __name__ == "__main__":
    main()