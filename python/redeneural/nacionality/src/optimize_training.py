#!/usr/bin/env python3
# optimize_training.py - Encontrar a melhor configuração para treinamento rápido
import os
import time
import subprocess
import argparse
import pandas as pd
from datetime import datetime

def run_training(model_type, batch_size, rnn_units, epochs=5, use_cache=True, use_early_stopping=True):
    """
    Executa o treinamento com uma configuração específica e retorna o tempo de execução.
    
    Args:
        model_type (str): Tipo de modelo ('lstm', 'gru', 'simple')
        batch_size (int): Tamanho do batch
        rnn_units (int): Número de unidades RNN
        epochs (int): Número de épocas
        use_cache (bool): Se True, usa cache para acelerar o carregamento de dados
        use_early_stopping (bool): Se True, usa early stopping
        
    Returns:
        dict: Resultados do treinamento (tempo, acurácia, etc.)
    """
    # Construir comando
    cmd = [
        "python3", "python/redeneural/nacionality/src/train.py",
        f"--model-type={model_type}",
        f"--batch-size={batch_size}",
        f"--rnn-units={rnn_units}",
        f"--epochs={epochs}",
        "--no-tensorboard"  # Desabilitar TensorBoard para testes
    ]
    
    if not use_cache:
        cmd.append("--no-cache")
    
    if not use_early_stopping:
        cmd.append("--no-early-stopping")
    
    # Registrar início
    start_time = time.time()
    
    # Executar treinamento
    print(f"\nExecutando: {' '.join(cmd)}")
    process = subprocess.Popen(
        cmd, 
        stdout=subprocess.PIPE, 
        stderr=subprocess.PIPE,
        universal_newlines=True
    )
    
    # Capturar saída
    stdout, stderr = process.communicate()
    
    # Calcular tempo
    training_time = time.time() - start_time
    
    # Extrair métricas da saída
    accuracy = None
    val_accuracy = None
    
    for line in stdout.split('\n'):
        if "Acurácia no conjunto de teste:" in line:
            try:
                accuracy = float(line.split(":")[1].strip())
            except:
                pass
        elif "Acurácia final de validação:" in line:
            try:
                val_accuracy = float(line.split(":")[1].strip())
            except:
                pass
    
    # Retornar resultados
    return {
        "model_type": model_type,
        "batch_size": batch_size,
        "rnn_units": rnn_units,
        "epochs": epochs,
        "use_cache": use_cache,
        "use_early_stopping": use_early_stopping,
        "training_time": training_time,
        "training_time_min": training_time / 60,
        "test_accuracy": accuracy,
        "val_accuracy": val_accuracy
    }

def optimize_training():
    """
    Testa diferentes configurações para encontrar a mais rápida.
    """
    # Configurações a testar
    configs = [
        # Modelo simples com diferentes batch sizes
        {"model_type": "simple", "batch_size": 64, "rnn_units": 64},
        {"model_type": "simple", "batch_size": 128, "rnn_units": 64},
        {"model_type": "simple", "batch_size": 256, "rnn_units": 64},
        {"model_type": "simple", "batch_size": 512, "rnn_units": 64},
        
        # Modelo GRU com diferentes unidades
        {"model_type": "gru", "batch_size": 256, "rnn_units": 32},
        {"model_type": "gru", "batch_size": 256, "rnn_units": 64},
        {"model_type": "gru", "batch_size": 256, "rnn_units": 128},
        
        # Modelo LSTM para comparação
        {"model_type": "lstm", "batch_size": 256, "rnn_units": 64},
    ]
    
    # Resultados
    results = []
    
    # Executar cada configuração
    for i, config in enumerate(configs):
        print(f"\n[{i+1}/{len(configs)}] Testando configuração: {config}")
        result = run_training(**config)
        results.append(result)
        
        # Exibir resultado parcial
        print(f"Tempo de treinamento: {result['training_time']:.2f} segundos ({result['training_time_min']:.2f} minutos)")
        if result['val_accuracy']:
            print(f"Acurácia de validação: {result['val_accuracy']:.4f}")
    
    # Criar DataFrame com resultados
    df = pd.DataFrame(results)
    
    # Ordenar por tempo de treinamento
    df_sorted = df.sort_values('training_time')
    
    # Salvar resultados
    timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
    results_dir = os.path.join("python/redeneural/nacionality/results")
    os.makedirs(results_dir, exist_ok=True)
    
    results_file = os.path.join(results_dir, f"optimization_results_{timestamp}.csv")
    df.to_csv(results_file, index=False)
    
    # Exibir resultados
    print("\n=== RESULTADOS DA OTIMIZAÇÃO ===")
    print(f"Resultados salvos em: {results_file}")
    print("\nConfiguração mais rápida:")
    fastest = df_sorted.iloc[0]
    print(f"  Modelo: {fastest['model_type']}")
    print(f"  Batch Size: {fastest['batch_size']}")
    print(f"  Unidades RNN: {fastest['rnn_units']}")
    print(f"  Tempo: {fastest['training_time']:.2f} segundos ({fastest['training_time_min']:.2f} minutos)")
    if fastest['val_accuracy']:
        print(f"  Acurácia de validação: {fastest['val_accuracy']:.4f}")
    
    print("\nTodas as configurações (ordenadas por tempo):")
    pd.set_option('display.max_rows', None)
    pd.set_option('display.max_columns', None)
    pd.set_option('display.width', 1000)
    print(df_sorted[['model_type', 'batch_size', 'rnn_units', 'training_time_min', 'val_accuracy']])
    
    return df_sorted

if __name__ == "__main__":
    parser = argparse.ArgumentParser(description='Otimizar configurações de treinamento.')
    parser.add_argument('--epochs', type=int, default=5, help='Número de épocas para cada teste')
    
    args = parser.parse_args()
    
    # Executar otimização
    optimize_training()
