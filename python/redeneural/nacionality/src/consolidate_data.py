# src/consolidate_data.py
import pandas as pd
import os
import time
import json
import sys

def consolidate_datasets(limit_files=None, sample_rows=None, start_from=None, checkpoint_interval=5, resume=True, save_on_checkpoint=False):
    """
    Consolida arquivos CSV de nomes por nacionalidade.

    Args:
        limit_files (int, optional): Limita o número de arquivos a processar (para testes)
        sample_rows (int, optional): Limita o número de linhas por arquivo (para testes)
        start_from (str, optional): Nome do arquivo a partir do qual continuar o processamento
        checkpoint_interval (int, optional): Intervalo para salvar checkpoints (a cada X arquivos)
        resume (bool, optional): Se True, tenta retomar o processamento de onde parou
        save_on_checkpoint (bool, optional): Se True, salva os dados durante os checkpoints (mais lento)
    """
    print("Iniciando consolidação de dados...")
    start_time = time.time()

    # Obter o diretório do script atual
    script_dir = os.path.dirname(os.path.abspath(__file__))
    # Definir caminhos absolutos baseados na localização do script
    data_dir = os.path.join(script_dir, "..", "data", "raw")
    processed_dir = os.path.join(script_dir, "..", "data", "processed")
    output_file = os.path.join(processed_dir, "nomes_consolidado.csv")
    checkpoint_file = os.path.join(processed_dir, "checkpoint.json")
    temp_dir = os.path.join(processed_dir, "temp")

    # Criar diretórios necessários
    os.makedirs(processed_dir, exist_ok=True)
    os.makedirs(temp_dir, exist_ok=True)

    # Melhorar tratamento de erros
    if not os.path.exists(data_dir):
        raise FileNotFoundError(f"Diretório {data_dir} não encontrado")

    print(f"Lendo arquivos de {data_dir}")
    csv_files = sorted([f for f in os.listdir(data_dir) if f.endswith('.csv')])
    if not csv_files:
        raise ValueError(f"Nenhum arquivo CSV encontrado em {data_dir}")

    # Verificar se existe um checkpoint para continuar o processamento
    last_processed_file = None
    processed_files = []

    # Arquivo para rastrear o progresso em tempo real
    progress_file = os.path.join(processed_dir, "progress.txt")

    # Limpar o arquivo de progresso no início da execução
    if not start_from and not last_processed_file:
        # Se não estamos continuando de um ponto específico, começar com um arquivo de progresso limpo
        if os.path.exists(progress_file):
            os.remove(progress_file)

    if os.path.exists(checkpoint_file):
        try:
            with open(checkpoint_file, 'r') as f:
                checkpoint_data = json.load(f)
                last_processed_file = checkpoint_data.get('last_processed_file')
                processed_files = checkpoint_data.get('processed_files', [])
                print(f"Checkpoint encontrado. Último arquivo processado: {last_processed_file}")
                print(f"Total de arquivos já processados: {len(processed_files)}")
        except Exception as e:
            print(f"Erro ao ler checkpoint: {e}")

    # Verificar se existe um arquivo de progresso (para recuperação de interrupções abruptas)
    if resume and os.path.exists(progress_file):
        try:
            with open(progress_file, 'r') as f:
                progress_data = f.read().strip().split('\n')
                if progress_data:
                    # O último arquivo listado é o que estava sendo processado quando ocorreu a interrupção
                    interrupted_file = progress_data[-1].strip()

                    if interrupted_file and interrupted_file in csv_files:
                        print(f"Detectada interrupção durante o processamento de: {interrupted_file}")

                        # Encontrar o índice do arquivo interrompido
                        interrupted_index = csv_files.index(interrupted_file)

                        # Começar a partir do arquivo interrompido
                        csv_files = csv_files[interrupted_index:]
                        print(f"Retomando o processamento a partir de: {interrupted_file}")
                        print(f"Pulando {interrupted_index} arquivos já processados.")

                        # Limpar o arquivo de progresso para começar de novo
                        open(progress_file, 'w').close()
        except Exception as e:
            print(f"Erro ao ler arquivo de progresso: {e}")

    # Se start_from for especificado, usar esse valor em vez do checkpoint
    if start_from:
        if start_from in csv_files:
            last_processed_file = None  # Ignorar checkpoint
            start_index = csv_files.index(start_from)
            csv_files = csv_files[start_index:]
            print(f"Iniciando a partir do arquivo especificado: {start_from}")
        else:
            print(f"Arquivo {start_from} não encontrado. Iniciando do início ou do último checkpoint.")
    elif last_processed_file:
        # Encontrar o índice do último arquivo processado
        try:
            last_index = csv_files.index(last_processed_file)
            # Começar a partir do próximo arquivo
            csv_files = csv_files[last_index + 1:]
            print(f"Continuando a partir do arquivo: {csv_files[0] if csv_files else 'Nenhum arquivo restante'}")
        except ValueError:
            print(f"Arquivo {last_processed_file} não encontrado na lista. Processando todos os arquivos.")

    # Limitar número de arquivos para teste, se especificado
    if limit_files and isinstance(limit_files, int) and limit_files > 0:
        csv_files = csv_files[:limit_files]
        print(f"Modo de teste: processando apenas {limit_files} arquivos")

    print(f"Total de arquivos a processar: {len(csv_files)}")

    # Carregar dados já processados, se existirem
    dfs = []
    existing_df = None
    if os.path.exists(output_file):
        try:
            print("Carregando dados já processados do arquivo consolidado...")
            existing_df = pd.read_csv(output_file)
            print(f"Carregados {len(existing_df)} registros de dados já processados.")

            # Não adicionar o DataFrame existente à lista dfs ainda
            # Vamos anexá-lo apenas no final para evitar duplicação de memória
        except Exception as e:
            print(f"Erro ao carregar dados existentes: {e}")
            print("Iniciando processamento do zero.")
            processed_files = []

    total_files = len(csv_files)

    for i, file in enumerate(csv_files, 1):
        try:
            print(f"Processando arquivo {i}/{total_files}: {file}")

            # Registrar o arquivo atual no arquivo de progresso
            with open(progress_file, 'a') as f:
                f.write(f"{file}\n")

            file_start_time = time.time()

            # Extrair nacionalidade do código do país (ex: 'SV.csv' -> 'Salvadorian')
            country_code = file.replace('.csv', '')

            # Ler CSV com tratamento de encoding
            # Definir nomes de colunas com base na estrutura observada
            file_path = os.path.join(data_dir, file)

            # Se sample_rows for especificado, ler apenas as primeiras linhas
            if sample_rows and isinstance(sample_rows, int) and sample_rows > 0:
                df = pd.read_csv(file_path,
                                encoding='utf-8',
                                names=['first_name', 'last_name', 'gender', 'country'],
                                header=None,
                                low_memory=False,
                                nrows=sample_rows)
                print(f"  Modo de teste: lendo apenas {sample_rows} linhas")
            else:
                df = pd.read_csv(file_path,
                                encoding='utf-8',
                                names=['first_name', 'last_name', 'gender', 'country'],
                                header=None,
                                low_memory=False)

            # Verificar se o arquivo tem cabeçalho e pular a primeira linha se necessário
            if df.iloc[0, 0] == 'First Name' or df.iloc[0, 0] == 'first_name' or df.iloc[0, 0] == 'First' or df.iloc[0, 0] == 'FirstName':
                df = df.iloc[1:]

            # Adicionar coluna de nacionalidade baseada no código do país
            df['nacionality'] = country_code

            # Criar coluna 'name' combinando primeiro e último nome
            df['name'] = df['first_name'] + ' ' + df['last_name']

            # Limpar e padronizar dados
            df = df.dropna()  # Remover linhas com valores nulos
            df['name'] = df['name'].str.strip()  # Remover espaços extras

            # Adicionar ao conjunto de dados
            dfs.append(df)

            # Salvar arquivo individual processado no diretório temporário
            temp_file = os.path.join(temp_dir, f"processed_{file}")
            df.to_csv(temp_file, index=False, encoding='utf-8')

            # Atualizar lista de arquivos processados
            processed_files.append(file)

            # Salvar checkpoint a cada checkpoint_interval arquivos
            if i % checkpoint_interval == 0 or i == total_files:
                # Salvar checkpoint
                checkpoint_data = {
                    'last_processed_file': file,
                    'processed_files': processed_files,
                    'timestamp': time.time(),
                    'files_remaining': len(csv_files) - i
                }

                with open(checkpoint_file, 'w') as f:
                    json.dump(checkpoint_data, f)

                # Salvar checkpoint
                print(f"Salvando checkpoint após processar {i}/{total_files} arquivos...")

                # Calcular estatísticas para o log
                total_processed = sum(len(df) for df in dfs)

                # Decidir se salva os dados durante o checkpoint
                if save_on_checkpoint:
                    try:
                        # Concatenar apenas os novos dados
                        new_data_df = pd.concat(dfs, ignore_index=True)

                        if existing_df is not None:
                            # Concatenar com os dados existentes
                            combined_df = pd.concat([existing_df, new_data_df], ignore_index=True)
                            # Salvar o arquivo completo
                            combined_df.to_csv(output_file, index=False, encoding='utf-8')
                            print(f"  Checkpoint salvo com {len(combined_df)} registros totais ({len(new_data_df)} novos).")
                        else:
                            # Se não houver dados existentes, salvar apenas os novos
                            new_data_df.to_csv(output_file, index=False, encoding='utf-8')
                            print(f"  Checkpoint salvo com {len(new_data_df)} registros.")
                    except Exception as e:
                        print(f"  Erro ao salvar dados no checkpoint: {e}")
                else:
                    # Apenas registrar as estatísticas sem salvar os dados
                    if existing_df is not None:
                        total_records = len(existing_df) + total_processed
                        print(f"  Checkpoint registrado: {total_records} registros totais ({total_processed} novos)")
                    else:
                        print(f"  Checkpoint registrado: {total_processed} registros")

            file_time = time.time() - file_start_time
            print(f"  Concluído em {file_time:.2f} segundos. Linhas processadas: {len(df)}")

        except Exception as e:
            print(f"Erro ao processar {file}: {e}")
            # Registrar erro no log
            with open(os.path.join(processed_dir, "error_log.txt"), "a") as error_log:
                error_log.write(f"{time.strftime('%Y-%m-%d %H:%M:%S')} - Erro ao processar {file}: {e}\n")

    if not dfs:
        print("Nenhum dado foi processado com sucesso. Verifique os erros acima.")
        return

    print("Concatenando todos os dataframes...")
    new_data_df = pd.concat(dfs, ignore_index=True)

    # Garantir diretório de saída
    os.makedirs(os.path.dirname(output_file), exist_ok=True)

    # Verificar se temos dados existentes para anexar
    if existing_df is not None:
        print(f"Anexando {len(new_data_df)} novos registros aos {len(existing_df)} existentes...")
        consolidated_df = pd.concat([existing_df, new_data_df], ignore_index=True)
        print(f"Salvando arquivo consolidado com {len(consolidated_df)} linhas totais...")
    else:
        consolidated_df = new_data_df
        print(f"Salvando arquivo consolidado com {len(consolidated_df)} linhas...")

    consolidated_df.to_csv(output_file, index=False, encoding='utf-8')

    # Limpar arquivos de controle após conclusão bem-sucedida
    if len(csv_files) == i:  # Se processou todos os arquivos
        try:
            # Remover checkpoint
            if os.path.exists(checkpoint_file):
                os.remove(checkpoint_file)
                print("Checkpoint removido após conclusão bem-sucedida.")

            # Remover arquivo de progresso
            if os.path.exists(progress_file):
                os.remove(progress_file)
                print("Arquivo de progresso removido após conclusão bem-sucedida.")
        except Exception as e:
            print(f"Erro ao remover arquivos de controle: {e}")

    total_time = time.time() - start_time
    print(f"Processamento concluído em {total_time:.2f} segundos")
    print(f"Dados consolidados salvos em {output_file}")

if __name__ == "__main__":
    import argparse

    parser = argparse.ArgumentParser(description='Consolidar arquivos CSV de nomes por nacionalidade.')
    parser.add_argument('--limit', type=int, help='Limitar o número de arquivos a processar')
    parser.add_argument('--sample', type=int, help='Limitar o número de linhas por arquivo')
    parser.add_argument('--start-from', type=str, help='Arquivo a partir do qual iniciar o processamento')
    parser.add_argument('--checkpoint-interval', type=int, default=5,
                        help='Intervalo para salvar checkpoints (a cada X arquivos)')
    parser.add_argument('--no-resume', action='store_true',
                        help='Não tentar retomar o processamento de onde parou')
    parser.add_argument('--save-on-checkpoint', action='store_true',
                        help='Salvar dados durante os checkpoints (mais lento)')

    args = parser.parse_args()

    # Processar arquivos com base nos argumentos fornecidos
    consolidate_datasets(
        limit_files=args.limit,
        sample_rows=args.sample,
        start_from=args.start_from,
        checkpoint_interval=args.checkpoint_interval,
        resume=not args.no_resume,
        save_on_checkpoint=args.save_on_checkpoint
    )
