#!/usr/bin/env python3
# monitor.py - Monitorar uso de CPU e memória durante o treinamento
import psutil
import time
import argparse
import os
import sys
import datetime

def monitor_resources(interval=1.0, duration=None, output_file=None):
    """
    Monitora o uso de CPU e memória.
    
    Args:
        interval (float): Intervalo em segundos entre as medições
        duration (float): Duração total do monitoramento em segundos (None para infinito)
        output_file (str): Arquivo para salvar os resultados (opcional)
    """
    # Abrir arquivo de saída se especificado
    f = None
    if output_file:
        try:
            f = open(output_file, 'w')
            f.write("timestamp,cpu_percent,memory_percent,memory_mb\n")
        except Exception as e:
            print(f"Erro ao abrir arquivo de saída: {e}")
            output_file = None
    
    # Iniciar monitoramento
    start_time = time.time()
    try:
        print("Monitorando uso de recursos. Pressione Ctrl+C para parar.")
        print("Timestamp | CPU (%) | Memória (%) | Memória (MB)")
        print("-" * 60)
        
        while True:
            # Obter uso de CPU e memória
            cpu_percent = psutil.cpu_percent(interval=0.1)
            memory = psutil.virtual_memory()
            memory_percent = memory.percent
            memory_mb = memory.used / (1024 * 1024)
            
            # Calcular tempo decorrido
            current_time = time.time()
            elapsed = current_time - start_time
            timestamp = datetime.datetime.now().strftime("%H:%M:%S")
            
            # Exibir informações
            print(f"{timestamp} | {cpu_percent:6.1f}% | {memory_percent:8.1f}% | {memory_mb:8.1f} MB", end="\r")
            
            # Salvar no arquivo se especificado
            if f:
                f.write(f"{elapsed:.1f},{cpu_percent:.1f},{memory_percent:.1f},{memory_mb:.1f}\n")
                f.flush()
            
            # Verificar se atingiu a duração especificada
            if duration and elapsed >= duration:
                break
            
            # Aguardar próxima medição
            time.sleep(interval)
    
    except KeyboardInterrupt:
        print("\nMonitoramento interrompido pelo usuário.")
    
    finally:
        # Fechar arquivo de saída
        if f:
            f.close()
        
        # Exibir estatísticas finais
        print("\n" + "=" * 60)
        print("Estatísticas de uso de recursos:")
        print(f"Duração: {time.time() - start_time:.1f} segundos")
        print("=" * 60)

def main():
    parser = argparse.ArgumentParser(description='Monitorar uso de CPU e memória.')
    parser.add_argument('--interval', type=float, default=1.0, help='Intervalo em segundos entre as medições')
    parser.add_argument('--duration', type=float, default=None, help='Duração total do monitoramento em segundos')
    parser.add_argument('--output', type=str, default=None, help='Arquivo para salvar os resultados')
    
    args = parser.parse_args()
    
    monitor_resources(
        interval=args.interval,
        duration=args.duration,
        output_file=args.output
    )

if __name__ == "__main__":
    main()
