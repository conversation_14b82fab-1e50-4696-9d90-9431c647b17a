#!/usr/bin/env python3
# train.py - Treinar modelo de classificação de nacionalidade por nome
import numpy as np
import os
import time
import pickle
import argparse
import matplotlib.pyplot as plt
import tensorflow as tf
import multiprocessing
from tensorflow.keras.models import Sequential
from tensorflow.keras.layers import Embedding, LSTM, Dense, Dropout, Bidirectional, GRU
from tensorflow.keras.callbacks import ModelCheckpoint, EarlyStopping, ReduceLROnPlateau, TensorBoard
from tensorflow.keras.utils import to_categorical
from tensorflow.keras.optimizers import Adam
from tensorflow.keras.optimizers.schedules import ExponentialDecay
from tensorflow.keras.mixed_precision import set_global_policy

# Configurações para otimizar o desempenho do TensorFlow
def configure_tensorflow(mixed_precision=False, memory_growth=True):
    """
    Configura o TensorFlow para melhor desempenho no MacBook.

    Args:
        mixed_precision (bool): Se <PERSON>, usa precisão mista para acelerar o treinamento
        memory_growth (bool): Se <PERSON>, permite o crescimento de memória da GPU conforme necessário
    """
    # Detectar número de CPUs disponíveis
    cpu_count = multiprocessing.cpu_count()
    print(f"Número de CPUs disponíveis: {cpu_count}")

    # Permitir crescimento de memória da GPU
    if memory_growth:
        gpus = tf.config.list_physical_devices('GPU')
        if gpus:
            try:
                for gpu in gpus:
                    tf.config.experimental.set_memory_growth(gpu, True)
                print(f"Crescimento de memória habilitado para {len(gpus)} GPU(s)")
            except RuntimeError as e:
                print(f"Erro ao configurar crescimento de memória da GPU: {e}")

    # Usar precisão mista para acelerar o treinamento
    if mixed_precision:
        try:
            set_global_policy('mixed_float16')
            print("Precisão mista habilitada (float16)")
        except Exception as e:
            print(f"Não foi possível habilitar a precisão mista: {e}")

    # Desabilitar compilador XLA para evitar erros em alguns MacBooks
    try:
        tf.config.optimizer.set_jit(False)
        print("Compilador XLA desabilitado para evitar erros")
    except Exception as e:
        print(f"Não foi possível configurar o compilador XLA: {e}")

    # Otimizações específicas para MacBooks
    try:
        # Verificar se estamos em um Mac com Apple Silicon
        import platform
        if platform.system() == 'Darwin':
            if platform.processor() == 'arm':
                # Configurações específicas para Apple Silicon
                print("Detectado Apple Silicon, aplicando otimizações específicas")
                os.environ['TF_ENABLE_ONEDNN_OPTS'] = '0'  # Desabilitar oneDNN que pode causar problemas
            else:
                print("Detectado Intel Mac, aplicando otimizações específicas")
    except Exception as e:
        print(f"Erro ao aplicar otimizações para Mac: {e}")

    # Configurar paralelismo de threads (automático)
    tf.config.threading.set_inter_op_parallelism_threads(0)
    tf.config.threading.set_intra_op_parallelism_threads(0)

    # Informações sobre a configuração
    print(f"TensorFlow versão: {tf.__version__}")
    print(f"Dispositivos disponíveis: {tf.config.list_physical_devices()}")

# Obter o diretório do script atual
script_dir = os.path.dirname(os.path.abspath(__file__))
# Definir caminhos absolutos baseados na localização do script
data_dir = os.path.join(script_dir, "..", "data")
processed_dir = os.path.join(data_dir, "processed")
models_dir = os.path.join(script_dir, "..", "models")

def load_data():
    """
    Carrega os dados pré-processados com otimizações para desempenho no MacBook.
    """
    print("Carregando dados pré-processados...")
    start_time = time.time()

    # Verificar se os arquivos existem
    required_files = [
        "x_train.npy", "y_train.npy",
        "x_val.npy", "y_val.npy",
        "x_test.npy", "y_test.npy"
    ]

    for file in required_files:
        file_path = os.path.join(processed_dir, file)
        if not os.path.exists(file_path):
            raise FileNotFoundError(f"Arquivo {file_path} não encontrado. Execute preprocess.py primeiro.")

    # Carregar dados com memory-mapping para arquivos grandes
    # Isso permite que o NumPy carregue apenas as partes necessárias do arquivo na memória
    mmap_mode = 'r'  # Usar memory-mapping por padrão para economizar memória
    print("Usando memory-mapping para economizar memória")

    # Carregar dados
    x_train = np.load(os.path.join(processed_dir, "x_train.npy"), mmap_mode=mmap_mode)
    y_train = np.load(os.path.join(processed_dir, "y_train.npy"), mmap_mode=mmap_mode)
    x_val = np.load(os.path.join(processed_dir, "x_val.npy"), mmap_mode=mmap_mode)
    y_val = np.load(os.path.join(processed_dir, "y_val.npy"), mmap_mode=mmap_mode)
    x_test = np.load(os.path.join(processed_dir, "x_test.npy"), mmap_mode=mmap_mode)
    y_test = np.load(os.path.join(processed_dir, "y_test.npy"), mmap_mode=mmap_mode)

    # Carregar informações do modelo
    model_info_path = os.path.join(models_dir, "model_info.pkl")
    if not os.path.exists(model_info_path):
        raise FileNotFoundError(f"Arquivo {model_info_path} não encontrado. Execute preprocess.py primeiro.")

    with open(model_info_path, 'rb') as f:
        model_info = pickle.load(f)

    max_len = model_info['max_len']
    num_classes = model_info['num_classes']
    vocab_size = model_info['vocab_size']

    # Otimizar uso de memória
    # Verificar se podemos usar tipos de dados menores para economizar memória
    if x_train.dtype == np.int32 and np.max(x_train) < 32767:
        print("Otimizando tipos de dados para economizar memória")
        x_train = x_train.astype(np.int16)
        x_val = x_val.astype(np.int16)
        x_test = x_test.astype(np.int16)

    # Converter para one-hot encoding
    print("Convertendo para one-hot encoding...")
    y_train = to_categorical(y_train, num_classes=num_classes)
    y_val = to_categorical(y_val, num_classes=num_classes)
    y_test = to_categorical(y_test, num_classes=num_classes)

    load_time = time.time() - start_time
    print(f"Dados carregados em {load_time:.2f} segundos")
    print(f"Dados carregados: {x_train.shape[0]} amostras de treino, {x_val.shape[0]} de validação, {x_test.shape[0]} de teste")
    print(f"Comprimento máximo: {max_len}, Tamanho do vocabulário: {vocab_size}, Número de classes: {num_classes}")

    # Exibir informações de uso de memória
    try:
        import psutil
        process = psutil.Process(os.getpid())
        memory_info = process.memory_info()
        print(f"Uso de memória atual: {memory_info.rss / (1024 * 1024):.2f} MB")
    except ImportError:
        print("Pacote psutil não disponível para monitoramento de memória")

    return x_train, y_train, x_val, y_val, x_test, y_test, max_len, vocab_size, num_classes

def create_model(max_len, vocab_size, num_classes, embedding_dim=64, rnn_units=128,
               model_type='gru', learning_rate=0.001, use_decay=True, dropout_rate=0.2):
    """
    Cria o modelo de rede neural com diferentes opções de arquitetura.

    Args:
        max_len (int): Comprimento máximo da sequência
        vocab_size (int): Tamanho do vocabulário
        num_classes (int): Número de classes
        embedding_dim (int): Dimensão do embedding
        rnn_units (int): Número de unidades RNN (LSTM ou GRU)
        model_type (str): Tipo de modelo ('lstm', 'gru', ou 'simple')
        learning_rate (float): Taxa de aprendizado inicial
        use_decay (bool): Se True, usa decaimento da taxa de aprendizado
        dropout_rate (float): Taxa de dropout
    """
    # Configurar otimizador com decaimento de taxa de aprendizado
    if use_decay:
        lr_schedule = ExponentialDecay(
            initial_learning_rate=learning_rate,
            decay_steps=1000,
            decay_rate=0.9,
            staircase=True
        )
        optimizer = Adam(learning_rate=lr_schedule)
    else:
        optimizer = Adam(learning_rate=learning_rate)

    # Criar modelo com base no tipo especificado
    model = Sequential()

    # Camada de embedding (comum a todos os modelos)
    model.add(Embedding(input_dim=vocab_size, output_dim=embedding_dim, input_length=max_len))

    if model_type == 'lstm':
        # Modelo LSTM bidirecional (mais complexo, mais preciso)
        model.add(Bidirectional(LSTM(rnn_units)))
        model.add(Dropout(dropout_rate))

    elif model_type == 'gru':
        # Modelo GRU bidirecional (mais rápido que LSTM, geralmente com desempenho similar)
        model.add(Bidirectional(GRU(rnn_units)))
        model.add(Dropout(dropout_rate))

    elif model_type == 'simple':
        # Modelo mais simples e rápido, mas ainda eficaz
        model.add(GRU(rnn_units))
        model.add(Dropout(dropout_rate))

    else:
        raise ValueError(f"Tipo de modelo desconhecido: {model_type}")

    # Camada de saída (comum a todos os modelos)
    model.add(Dense(num_classes, activation='softmax'))

    # Compilar modelo
    model.compile(
        optimizer=optimizer,
        loss='categorical_crossentropy',
        metrics=['accuracy']
    )

    return model

def train_model(model, x_train, y_train, x_val, y_val, batch_size=128, epochs=20,
              use_tensorboard=True, use_early_stopping=True, patience=5, use_cache=True, use_decay=True):
    """
    Treina o modelo com os dados fornecidos, com otimizações para MacBook.

    Args:
        model: Modelo a ser treinado
        x_train: Dados de treino (features)
        y_train: Dados de treino (labels)
        x_val: Dados de validação (features)
        y_val: Dados de validação (labels)
        batch_size (int): Tamanho do batch
        epochs (int): Número de épocas
        use_tensorboard (bool): Se True, usa TensorBoard para monitorar o treinamento
        use_early_stopping (bool): Se True, usa early stopping para evitar overfitting
        patience (int): Número de épocas sem melhoria antes de parar o treinamento
        use_cache (bool): Se True, usa cache para acelerar o carregamento de dados
        use_decay (bool): Se True, indica que o modelo está usando LearningRateSchedule
    """
    start_time = time.time()

    # Criar diretório para salvar o modelo
    os.makedirs(models_dir, exist_ok=True)

    # Lista de callbacks
    callbacks = []

    # Checkpoint para salvar o melhor modelo
    checkpoint_path = os.path.join(models_dir, 'best_model.h5')
    checkpoint = ModelCheckpoint(
        checkpoint_path,
        monitor='val_accuracy',
        save_best_only=True,
        verbose=1
    )
    callbacks.append(checkpoint)

    # Early stopping para evitar overfitting
    if use_early_stopping:
        early_stopping = EarlyStopping(
            monitor='val_accuracy',
            patience=patience,
            verbose=1,
            restore_best_weights=True
        )
        callbacks.append(early_stopping)
        print(f"Early stopping habilitado com paciência de {patience} épocas")

    # Redução da taxa de aprendizado quando o treinamento estagna
    # Só adicionar se não estiver usando um LearningRateSchedule
    if not use_decay:
        reduce_lr = ReduceLROnPlateau(
            monitor='val_loss',
            factor=0.5,  # Redução mais agressiva
            patience=patience // 2,  # Metade da paciência do early stopping
            min_lr=1e-6,
            verbose=1
        )
        callbacks.append(reduce_lr)
        print("Adicionado callback para redução de taxa de aprendizado")
    else:
        print("Usando LearningRateSchedule, callback ReduceLROnPlateau não adicionado")

    # TensorBoard para visualização do treinamento (configurado para mínimo overhead)
    if use_tensorboard:
        log_dir = os.path.join(models_dir, 'logs', time.strftime("%Y%m%d-%H%M%S"))
        os.makedirs(log_dir, exist_ok=True)

        tensorboard = TensorBoard(
            log_dir=log_dir,
            histogram_freq=0,
            write_graph=False,
            update_freq='epoch'
        )
        callbacks.append(tensorboard)
        print(f"Logs do TensorBoard salvos em: {log_dir}")

    # Preparar dataset para treinamento eficiente
    if use_cache:
        print("Preparando datasets otimizados...")

        # Otimizar tamanho do buffer de shuffle para economizar memória
        buffer_size = min(10000, len(x_train))

        # Criar dataset do TensorFlow com otimizações para velocidade
        train_dataset = tf.data.Dataset.from_tensor_slices((x_train, y_train))
        train_dataset = train_dataset.shuffle(buffer_size=buffer_size)
        train_dataset = train_dataset.batch(batch_size)
        train_dataset = train_dataset.prefetch(tf.data.AUTOTUNE)

        # Dataset de validação
        val_dataset = tf.data.Dataset.from_tensor_slices((x_val, y_val))
        val_dataset = val_dataset.batch(batch_size)
        val_dataset = val_dataset.prefetch(tf.data.AUTOTUNE)

        # Treinar o modelo com dataset
        print(f"Iniciando treinamento com batch_size={batch_size}, epochs={epochs}")
        history = model.fit(
            train_dataset,
            validation_data=val_dataset,
            epochs=epochs,
            callbacks=callbacks,
            verbose=1
        )
    else:
        # Treinar o modelo com arrays NumPy
        print(f"Iniciando treinamento com arrays NumPy")
        print(f"batch_size={batch_size}, epochs={epochs}")
        history = model.fit(
            x_train, y_train,
            validation_data=(x_val, y_val),
            batch_size=batch_size,
            epochs=epochs,
            callbacks=callbacks,
            verbose=1
        )

    # Salvar o modelo final
    try:
        final_model_path = os.path.join(models_dir, 'final_model.h5')
        model.save(final_model_path)
        print(f"Modelo final salvo em: {final_model_path}")
    except Exception as e:
        print(f"Aviso: Não foi possível salvar o modelo final: {e}")
        print("Tentando salvar apenas os pesos do modelo...")
        try:
            weights_path = os.path.join(models_dir, 'final_model_weights.h5')
            model.save_weights(weights_path)
            print(f"Pesos do modelo salvos em: {weights_path}")
        except Exception as e2:
            print(f"Erro ao salvar os pesos do modelo: {e2}")

    # Salvar histórico de treinamento
    history_path = os.path.join(models_dir, 'training_history.pkl')
    with open(history_path, 'wb') as f:
        pickle.dump(history.history, f)

    # Exibir tempo total de treinamento
    training_time = time.time() - start_time
    print(f"Tempo total de treinamento: {training_time:.2f} segundos ({training_time/60:.2f} minutos)")

    return history

def plot_history(history):
    """
    Plota o histórico de treinamento.
    """
    plt.figure(figsize=(12, 4))

    # Acurácia
    plt.subplot(1, 2, 1)
    plt.plot(history.history['accuracy'], label='Treino')
    plt.plot(history.history['val_accuracy'], label='Validação')
    plt.title('Acurácia do Modelo')
    plt.xlabel('Época')
    plt.ylabel('Acurácia')
    plt.legend()

    # Perda
    plt.subplot(1, 2, 2)
    plt.plot(history.history['loss'], label='Treino')
    plt.plot(history.history['val_loss'], label='Validação')
    plt.title('Perda do Modelo')
    plt.xlabel('Época')
    plt.ylabel('Perda')
    plt.legend()

    plt.tight_layout()
    plt.savefig(os.path.join(models_dir, 'training_history.png'))
    plt.close()

def evaluate_model(model, x_test, y_test):
    """
    Avalia o modelo no conjunto de teste.
    """
    print("Avaliando o modelo no conjunto de teste...")
    loss, accuracy = model.evaluate(x_test, y_test, verbose=1)
    print(f"Acurácia no conjunto de teste: {accuracy:.4f}")
    print(f"Perda no conjunto de teste: {loss:.4f}")

    # Salvar métricas
    with open(os.path.join(models_dir, 'test_metrics.txt'), 'w') as f:
        f.write(f"Acurácia no conjunto de teste: {accuracy:.4f}\n")
        f.write(f"Perda no conjunto de teste: {loss:.4f}\n")

    return loss, accuracy

if __name__ == "__main__":
    parser = argparse.ArgumentParser(description='Treinar modelo de classificação de nacionalidade por nome.')

    # Parâmetros de hardware e otimização
    parser.add_argument('--mixed-precision', action='store_true', help='Usar precisão mista para acelerar o treinamento')
    parser.add_argument('--no-memory-growth', action='store_true', help='Desabilitar crescimento de memória da GPU')
    parser.add_argument('--no-cache', action='store_true', help='Desabilitar cache de dados')

    # Parâmetros do modelo
    parser.add_argument('--model-type', type=str, default='gru', choices=['lstm', 'gru', 'simple'],
                        help='Tipo de modelo (lstm, gru, simple)')
    parser.add_argument('--batch-size', type=int, default=128, help='Tamanho do batch')
    parser.add_argument('--epochs', type=int, default=20, help='Número de épocas')
    parser.add_argument('--embedding-dim', type=int, default=64, help='Dimensão do embedding')
    parser.add_argument('--rnn-units', type=int, default=128, help='Número de unidades RNN (LSTM/GRU)')
    parser.add_argument('--learning-rate', type=float, default=0.001, help='Taxa de aprendizado inicial')
    parser.add_argument('--no-decay', action='store_true', help='Desabilitar decaimento da taxa de aprendizado')
    parser.add_argument('--dropout', type=float, default=0.2, help='Taxa de dropout')

    # Parâmetros de treinamento
    parser.add_argument('--no-early-stopping', action='store_true', help='Desabilitar early stopping')
    parser.add_argument('--patience', type=int, default=5, help='Paciência para early stopping')
    parser.add_argument('--no-tensorboard', action='store_true', help='Desabilitar TensorBoard')

    args = parser.parse_args()

    print("\n" + "="*50)
    print("TREINAMENTO DE MODELO DE CLASSIFICAÇÃO DE NACIONALIDADE")
    print("="*50 + "\n")

    # Configurar TensorFlow para melhor desempenho
    configure_tensorflow(
        mixed_precision=args.mixed_precision,
        memory_growth=not args.no_memory_growth
    )

    # Carregar dados
    print("\nCarregando dados...")
    x_train, y_train, x_val, y_val, x_test, y_test, max_len, vocab_size, num_classes = load_data()

    # Criar modelo
    print("\nCriando modelo...")
    model = create_model(
        max_len=max_len,
        vocab_size=vocab_size,
        num_classes=num_classes,
        embedding_dim=args.embedding_dim,
        rnn_units=args.rnn_units,
        model_type=args.model_type,
        learning_rate=args.learning_rate,
        use_decay=not args.no_decay,
        dropout_rate=args.dropout
    )

    # Resumo do modelo
    model.summary()

    # Treinar modelo com otimizações para MacBook
    print("\nIniciando treinamento otimizado...")
    history = train_model(
        model=model,
        x_train=x_train,
        y_train=y_train,
        x_val=x_val,
        y_val=y_val,
        batch_size=args.batch_size,
        epochs=args.epochs,
        use_tensorboard=not args.no_tensorboard,
        use_early_stopping=not args.no_early_stopping,
        patience=args.patience,
        use_cache=not args.no_cache,
        use_decay=not args.no_decay
    )

    # Plotar histórico
    print("\nGerando gráficos de treinamento...")
    plot_history(history)

    # Avaliar modelo
    print("\nAvaliando modelo no conjunto de teste...")
    evaluate_model(model, x_test, y_test)

    # Imprimir resumo do treinamento
    print("\n" + "="*50)
    print("RESUMO DO TREINAMENTO")
    print("="*50)
    print(f"Tipo de modelo: {args.model_type}")
    print(f"Batch size: {args.batch_size}")
    print(f"Épocas: {args.epochs}")

    # Imprimir métricas finais
    final_val_acc = history.history['val_accuracy'][-1]
    final_val_loss = history.history['val_loss'][-1]
    print(f"Acurácia final de validação: {final_val_acc:.4f}")
    print(f"Perda final de validação: {final_val_loss:.4f}")

    print("\nTreinamento concluído com sucesso!")
