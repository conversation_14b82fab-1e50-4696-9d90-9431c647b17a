# Previsão de Nacionalidade por Nome

Este projeto utiliza redes neurais para prever a nacionalidade de uma pessoa com base em seu nome.

## Otimizações para MacBook

O código foi otimizado para melhor desempenho em MacBooks, incluindo:

- Suporte para Apple Silicon (M1/M2/M3)
- Modelo GRU bidirecional para boa precisão e velocidade
- Uso eficiente de memória
- Processamento em lote otimizado
- Early stopping para evitar overfitting

## Treinamento do Modelo

Para treinar o modelo com configurações otimizadas:

```bash
python src/train.py --mixed-precision
```

O modelo padrão é o `gru` (GRU bidirecional), que oferece um bom equilíbrio entre precisão e velocidade.

Opções disponíveis:
- `--model-type`: Tipo de modelo (`gru`, `lstm`, `simple`)
- `--mixed-precision`: Usar precisão mista para acelerar o treinamento
- `--batch-size`: <PERSON><PERSON><PERSON> do lote (padrão: 128)
- `--epochs`: Número de épocas de treinamento (padrão: 20)
- `--embedding-dim`: Dimensão do embedding (padrão: 64)
- `--rnn-units`: Número de unidades RNN (padrão: 128)

## Previsão de Nacionalidade

Para prever a nacionalidade de um nome:

```bash
python src/predict.py --name="João Silva"
```

Para processar múltiplos nomes de um arquivo:

```bash
python src/predict.py --file=nomes.txt --batch-size=32
```

Para usar o modo interativo:

```bash
python src/predict.py --interactive
```

Opções disponíveis:
- `--name`: Nome para prever a nacionalidade
- `--file`: Arquivo com nomes (um por linha)
- `--top-k`: Número de previsões a retornar (padrão: 3)
- `--batch-size`: Tamanho do lote para processamento em batch (padrão: 32)
- `--no-full-names`: Mostrar apenas códigos de país em vez de nomes completos
- `--show-all`: Mostrar todas as nacionalidades
- `--output`: Arquivo para salvar os resultados em formato CSV

## Recomendações para Melhor Desempenho

1. **Modelo Padrão**: Use o modelo padrão `gru` para um bom equilíbrio entre precisão e velocidade
2. **Modelo Mais Rápido**: Se precisar de mais velocidade, use `--model-type=simple` (2x mais rápido)
3. **Modelo Mais Preciso**: Se precisar de máxima precisão, use `--model-type=lstm` (mais lento)
4. **Batch Size**: Use o padrão (128) para treinamento e `--batch-size=32` para previsão
5. **Precisão Mista**: Use `--mixed-precision` para acelerar o treinamento (se disponível)
6. **Previsão em Lote**: Para processar muitos nomes, use arquivos em vez de modo interativo

### Comparação de Modelos

| Modelo | Velocidade Relativa | Precisão Relativa |
|--------|---------------------|-------------------|
| simple | 1x (mais rápido)    | Boa               |
| gru    | 1.5-2x mais lento   | Melhor            |
| lstm   | 2-3x mais lento     | Ligeiramente melhor |

### Monitoramento de Recursos

Para monitorar o uso de CPU e memória durante o treinamento:

```bash
# Em um terminal separado
python src/monitor.py
```

## Requisitos

- Python 3.6+
- TensorFlow 2.x
- NumPy
- Matplotlib (para visualização)
