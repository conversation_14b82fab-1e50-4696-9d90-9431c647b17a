import math
import os

def clear():
    os.system('cls' if os.name == 'nt' else 'clear')

clear()

def taylor_series(func, x_rad, n_terms=100):
    """
    Calcula aproximação de funções usando série de Taylor.
    """
    result = 0.0
    for n in range(n_terms):
        if func == 'sin':
            if n % 2 == 0:
                term = ((-1)**(n//2) * (x_rad**(2*(n//2)+1)) / math.factorial(2*(n//2)+1))
                result += term
        elif func == 'cos':
            if n % 2 == 0:
                term = (-1)**(n//2) * x_rad**(2*(n//2)) / math.factorial(2*(n//2))
                result += term
        elif func == 'tan':
            if n < len(tan_coeffs):
                term = tan_coeffs[n] * x_rad**(2*n+1)
                result += term
    return result

# Coeficientes para a série da tangente
tan_coeffs = [1, 1/3, 2/15, 17/315, 62/2835]

def calculate_trig_values(angle_deg):
    """Calcula todos os valores trigonométricos para um ângulo"""
    x_rad = math.radians(angle_deg)

    # Usando 100 termos para melhor precisão
    sin_approx = taylor_series('sin', x_rad)
    cos_approx = taylor_series('cos', x_rad)
    tan_approx = taylor_series('tan', x_rad)

    # Valores reais para comparação
    real_sin = math.sin(x_rad)
    real_cos = math.cos(x_rad)
    real_tan = math.tan(x_rad)

    print(f"\nÂngulo: {angle_deg}° ({x_rad:.4f} rad)")
    print("="*40)
    print(f"| {'Função':<10} | {'Aproximação':<12} | {'Valor Real':<12}|")
    print("="*40)
    print(f"| {'seno':<10} | {sin_approx:<12.8f} | {real_sin:<12.8f}| ")
    print(f"| {'cosseno':<10} | {cos_approx:<12.8f} | {real_cos:<12.8f}| ")
    print(f"| {'tangente':<10} | {tan_approx:<12.8f} | {real_tan:<12.8f}|")
    print("="*40)

def find_angles_from_value(value):
    """Encontra todos os ângulos possíveis para um valor trigonométrico"""
    print(f"\nPara o valor: {value}")
    print("="*60)

    # Arco seno
    if -1 <= value <= 1:
        asin_rad = math.asin(value)
        asin_deg = math.degrees(asin_rad)
        print(f"Arco seno principal: {asin_deg:.4f}° ({asin_rad:.4f} rad)")
        print(f"Outra solução: {180-asin_deg:.4f}° ({math.pi-asin_rad:.4f} rad)")
    else:
        print("Arco seno: Valor fora do domínio [-1, 1]")

    # Arco cosseno
    if -1 <= value <= 1:
        acos_rad = math.acos(value)
        acos_deg = math.degrees(acos_rad)
        print(f"\nArco cosseno principal: {acos_deg:.4f}° ({acos_rad:.4f} rad)")
        print(f"Outra solução: {-acos_deg:.4f}° ({-acos_rad:.4f} rad)")
    else:
        print("\nArco cosseno: Valor fora do domínio [-1, 1]")
    
    # Arco tangente
    atan_rad = math.atan(value)
    atan_deg = math.degrees(atan_rad)
    print(f"\nArco tangente principal: {atan_deg:.4f}° ({atan_rad:.4f} rad)")
    print(f"Outra solução: {atan_deg+180:.4f}° ({atan_rad+math.pi:.4f} rad)")
    print("="*60)

def main():
    print("1. Calcular valores trigonométricos para um ângulo")
    print("2. Encontrar ângulos a partir de um valor trigonométrico")
    choice = input("\nEscolha (1/2): ")
    
    if choice == '1':
        angle = float(input("\nDigite o ângulo em graus: "))
        calculate_trig_values(angle)
    elif choice == '2':
        value = float(input("\nDigite o valor trigonométrico: "))
        find_angles_from_value(value)
    else:
        print("Opção inválida!")

if __name__ == "__main__":
    main()