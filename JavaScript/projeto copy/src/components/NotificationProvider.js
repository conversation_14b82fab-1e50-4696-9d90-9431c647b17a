import React, { createContext, useState, useContext } from 'react';
import Notification from './Notification';

// Criar contexto para notificações
const NotificationContext = createContext();

/**
 * Provider para o sistema de notificações
 * @param {Object} props - Propriedades do componente
 * @param {React.ReactNode} props.children - Componentes filhos
 */
export const NotificationProvider = ({ children }) => {
  const [notifications, setNotifications] = useState([]);

  // Adicionar uma nova notificação
  const addNotification = (message, type = 'info', duration = 3000) => {
    const id = Date.now();
    setNotifications(prev => [...prev, { id, message, type, duration }]);
    
    // Remover a notificação após a duração especificada
    setTimeout(() => {
      removeNotification(id);
    }, duration);
    
    return id;
  };

  // Remover uma notificação pelo ID
  const removeNotification = (id) => {
    setNotifications(prev => prev.filter(notification => notification.id !== id));
  };

  // Valor do contexto
  const value = {
    notifications,
    addNotification,
    removeNotification
  };

  return (
    <NotificationContext.Provider value={value}>
      {children}
      
      {/* Renderizar todas as notificações ativas */}
      <div className="notifications-container">
        {notifications.map(notification => (
          <Notification
            key={notification.id}
            message={notification.message}
            type={notification.type}
            duration={notification.duration}
            show={true}
            onClose={() => removeNotification(notification.id)}
          />
        ))}
      </div>
    </NotificationContext.Provider>
  );
};

/**
 * Hook para usar o sistema de notificações
 * @returns {Object} - Funções para gerenciar notificações
 */
export const useNotifications = () => {
  const context = useContext(NotificationContext);
  if (!context) {
    throw new Error('useNotifications deve ser usado dentro de um NotificationProvider');
  }
  return context;
};

export default NotificationProvider;
