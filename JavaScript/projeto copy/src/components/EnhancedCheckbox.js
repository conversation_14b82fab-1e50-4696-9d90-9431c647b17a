import React from 'react';
import TooltipWrapper from './TooltipWrapper';

/**
 * Checkbox melhorado com estilo personalizado e tooltip
 * @param {Object} props - Propriedades do componente
 * @param {string} props.id - ID do checkbox
 * @param {string} props.label - Label do checkbox
 * @param {boolean} props.checked - Se o checkbox está marcado
 * @param {Function} props.onChange - Função de mudança
 * @param {boolean} props.disabled - Se o checkbox está desabilitado
 * @param {string} props.className - Classes CSS adicionais
 * @param {string} props.tooltip - Texto do tooltip
 */
const EnhancedCheckbox = ({
  id,
  label,
  checked = false,
  onChange,
  disabled = false,
  className = '',
  tooltip = '',
  ...rest
}) => {
  // Criar o checkbox base
  const checkbox = (
    <label
      className={`custom-checkbox ${className} ${disabled ? 'disabled' : ''}`}
      htmlFor={id}
    >
      <input
        type="checkbox"
        id={id}
        checked={checked}
        onChange={onChange}
        disabled={disabled}
        {...rest}
      />
      <span className="checkmark"></span>
      <span className="checkbox-label">{label}</span>
    </label>
  );
  
  // Adicionar tooltip se fornecido
  return tooltip ? <TooltipWrapper text={tooltip}>{checkbox}</TooltipWrapper> : checkbox;
};

export default EnhancedCheckbox;
