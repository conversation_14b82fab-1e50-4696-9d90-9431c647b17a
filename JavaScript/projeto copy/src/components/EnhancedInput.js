import React, { useState } from 'react';
import TooltipWrapper from './TooltipWrapper';

/**
 * Input melhorado com auto-clear, validação e feedback visual
 * @param {Object} props - Propriedades do componente
 * @param {string} props.value - Valor do input
 * @param {Function} props.onChange - Função de mudança
 * @param {string} props.placeholder - Placeholder do input
 * @param {boolean} props.autoClear - Se o input deve limpar automaticamente ao focar
 * @param {Function} props.validate - Função de validação
 * @param {string} props.errorMessage - Mensagem de erro padrão
 * @param {string} props.className - Classes CSS adicionais
 * @param {string} props.tooltip - Texto do tooltip
 */
const EnhancedInput = ({
  value,
  onChange,
  placeholder = '',
  autoClear = false,
  validate,
  errorMessage = 'Valor inválido',
  className = '',
  tooltip = '',
  ...rest
}) => {
  const [error, setError] = useState('');
  const [originalValue, setOriginalValue] = useState('');
  
  // Função para lidar com o foco
  const handleFocus = (e) => {
    if (autoClear && value) {
      setOriginalValue(value);
      onChange('');
    }
    
    if (rest.onFocus) {
      rest.onFocus(e);
    }
  };
  
  // Função para lidar com a perda de foco
  const handleBlur = (e) => {
    // Se o valor estiver vazio e tínhamos um valor original, restaurar
    if (autoClear && value === '' && originalValue) {
      onChange(originalValue);
    }
    
    // Validar o valor se uma função de validação for fornecida
    if (validate && value) {
      const isValid = validate(value);
      if (!isValid) {
        setError(errorMessage);
      } else {
        setError('');
      }
    }
    
    if (rest.onBlur) {
      rest.onBlur(e);
    }
  };
  
  // Função para lidar com a mudança
  const handleChange = (e) => {
    onChange(e.target.value);
    
    // Limpar erro quando o usuário começa a digitar
    if (error) {
      setError('');
    }
  };
  
  // Adicionar classes de erro se necessário
  const inputClasses = `${className} ${error ? 'error-input' : ''} ${autoClear ? 'auto-clear' : ''}`;
  
  // Criar o input base
  const input = (
    <div className="enhanced-input-container">
      <input
        value={value}
        onChange={handleChange}
        onFocus={handleFocus}
        onBlur={handleBlur}
        placeholder={placeholder}
        className={inputClasses}
        {...rest}
      />
      {error && <div className="error-message">{error}</div>}
    </div>
  );
  
  // Adicionar tooltip se fornecido
  return tooltip ? <TooltipWrapper text={tooltip}>{input}</TooltipWrapper> : input;
};

export default EnhancedInput;
