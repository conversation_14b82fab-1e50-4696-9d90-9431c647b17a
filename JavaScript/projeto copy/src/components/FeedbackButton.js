import React, { useState } from 'react';
import Modal from './Modal';
import EnhancedButton from './EnhancedButton';
import { useNotifications } from './NotificationProvider';

/**
 * Componente de botão de feedback que abre um modal para enviar feedback
 * @param {Object} props - Propriedades do componente
 * @param {string} props.className - Classes CSS adicionais
 */
const FeedbackButton = ({ className = '' }) => {
  const [isOpen, setIsOpen] = useState(false);
  const [feedback, setFeedback] = useState('');
  const [email, setEmail] = useState('');
  const [isSubmitting, setIsSubmitting] = useState(false);
  const { addNotification } = useNotifications();
  
  const openModal = () => setIsOpen(true);
  const closeModal = () => setIsOpen(false);
  
  const handleSubmit = (e) => {
    e.preventDefault();
    
    // Simular envio de feedback
    setIsSubmitting(true);
    
    setTimeout(() => {
      setIsSubmitting(false);
      closeModal();
      setFeedback('');
      setEmail('');
      
      // Mostrar notificação de sucesso
      addNotification('Feedback enviado com sucesso! Obrigado por sua contribuição.', 'success');
    }, 1000);
  };
  
  return (
    <>
      <button
        className={`feedback-button ${className}`}
        onClick={openModal}
        aria-label="Enviar feedback"
        title="Enviar feedback"
      >
        Feedback
      </button>
      
      <Modal
        isOpen={isOpen}
        onClose={closeModal}
        title="Enviar Feedback"
      >
        <form onSubmit={handleSubmit}>
          <div className="form-group">
            <label htmlFor="feedback">Seu feedback:</label>
            <textarea
              id="feedback"
              value={feedback}
              onChange={(e) => setFeedback(e.target.value)}
              placeholder="Compartilhe suas sugestões, problemas ou ideias..."
              rows={5}
              required
            />
          </div>
          
          <div className="form-group">
            <label htmlFor="email">Seu email (opcional):</label>
            <input
              type="email"
              id="email"
              value={email}
              onChange={(e) => setEmail(e.target.value)}
              placeholder="Para que possamos responder a você"
            />
          </div>
          
          <div className="form-actions">
            <EnhancedButton
              type="button"
              onClick={closeModal}
              disabled={isSubmitting}
              className="cancel-button"
            >
              Cancelar
            </EnhancedButton>
            
            <EnhancedButton
              type="submit"
              disabled={!feedback || isSubmitting}
              isLoading={isSubmitting}
              className="submit-button"
            >
              Enviar Feedback
            </EnhancedButton>
          </div>
        </form>
      </Modal>
      
      <style jsx>{`
        .feedback-button {
          background-color: rgba(76, 175, 80, 0.2);
          border: 1px solid var(--primary-color);
          color: var(--primary-color);
          border-radius: 4px;
          padding: 6px 12px;
          font-size: 14px;
          cursor: pointer;
          transition: all 0.3s ease;
        }
        
        .feedback-button:hover {
          background-color: rgba(76, 175, 80, 0.4);
        }
        
        .form-group {
          margin-bottom: 15px;
        }
        
        label {
          display: block;
          margin-bottom: 5px;
          color: #ddd;
          font-size: 14px;
        }
        
        textarea, input {
          width: 100%;
          padding: 8px 12px;
          background-color: #333;
          border: 1px solid #555;
          border-radius: 4px;
          color: white;
          font-size: 14px;
        }
        
        textarea:focus, input:focus {
          border-color: var(--primary-color);
          outline: none;
        }
        
        .form-actions {
          display: flex;
          justify-content: flex-end;
          gap: 10px;
          margin-top: 20px;
        }
        
        .cancel-button {
          background-color: transparent;
          border: 1px solid #555;
          color: #ddd;
        }
        
        .submit-button {
          background-color: var(--primary-color);
          border: 1px solid var(--primary-color);
          color: white;
        }
      `}</style>
    </>
  );
};

export default FeedbackButton;
