import React from 'react';

/**
 * Componente de indicador de carregamento
 * @param {Object} props - Propriedades do componente
 * @param {string} props.size - Tamanho do indicador ('small', 'medium', 'large')
 * @param {string} props.color - Cor do indicador
 * @param {string} props.className - Classes CSS adicionais
 */
const LoadingIndicator = ({
  size = 'medium',
  color = 'var(--primary-color)',
  className = '',
  ...rest
}) => {
  // Determinar o tamanho com base na prop
  const sizeMap = {
    small: { width: '16px', height: '16px', borderWidth: '2px' },
    medium: { width: '24px', height: '24px', borderWidth: '3px' },
    large: { width: '32px', height: '32px', borderWidth: '4px' }
  };
  
  const { width, height, borderWidth } = sizeMap[size] || sizeMap.medium;
  
  // Estilo inline para o spinner
  const spinnerStyle = {
    width,
    height,
    border: `${borderWidth} solid rgba(255, 255, 255, 0.2)`,
    borderTop: `${borderWidth} solid ${color}`,
    borderRadius: '50%',
    animation: 'spin 1s linear infinite'
  };
  
  return (
    <div className={`loading-indicator ${className}`} {...rest}>
      <style>
        {`
          @keyframes spin {
            0% { transform: rotate(0deg); }
            100% { transform: rotate(360deg); }
          }
        `}
      </style>
      <div style={spinnerStyle}></div>
    </div>
  );
};

export default LoadingIndicator;
