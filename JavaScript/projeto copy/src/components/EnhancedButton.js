import React from 'react';
import TooltipWrapper from './TooltipWrapper';

/**
 * Botão melhorado com efeito de ripple, tooltip e feedback visual
 * @param {Object} props - Propriedades do componente
 * @param {React.ReactNode} props.children - Conte<PERSON><PERSON> do botão
 * @param {Function} props.onClick - Função de clique
 * @param {boolean} props.disabled - Se o botão está desabilitado
 * @param {string} props.className - Classes CSS adicionais
 * @param {string} props.tooltip - Texto do tooltip
 * @param {string} props.shortcut - Atalho de teclado (ex: 'Alt+C')
 * @param {boolean} props.isLoading - Se o botão está em estado de carregamento
 */
const EnhancedButton = ({
  children,
  onClick,
  disabled = false,
  className = '',
  tooltip = '',
  shortcut = '',
  isLoading = false,
  ...rest
}) => {
  // Adicionar classe de loading se necessário
  const buttonClasses = `${className} ${isLoading ? 'loading' : ''} ${shortcut ? 'has-shortcut' : ''}`;
  
  // Criar o botão base
  const button = (
    <button
      className={buttonClasses}
      onClick={onClick}
      disabled={disabled || isLoading}
      data-shortcut={shortcut}
      {...rest}
    >
      {children}
    </button>
  );
  
  // Adicionar tooltip se fornecido
  return tooltip ? <TooltipWrapper text={tooltip}>{button}</TooltipWrapper> : button;
};

export default EnhancedButton;
