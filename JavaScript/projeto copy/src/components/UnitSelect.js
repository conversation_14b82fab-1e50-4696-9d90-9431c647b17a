import React from 'react';
import Select from 'react-select';
import { unitSelectStyles } from '../styles/selectStyles';

// Component for unit selection
const UnitSelect = ({ value, onChange, options }) => {
  return (
    <Select
      value={options.find(opt => opt.value === value)}
      onChange={(option) => onChange(option.value)}
      options={options}
      styles={unitSelectStyles}
      isSearchable={false}
      components={{
        IndicatorSeparator: null
      }}
    />
  );
};

export default UnitSelect;
