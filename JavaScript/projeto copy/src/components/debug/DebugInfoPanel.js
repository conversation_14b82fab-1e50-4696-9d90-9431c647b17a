import React from 'react';
import DebugDropdown from './DebugDropdown';

/**
 * Componente para o painel de informações de debug
 */
const DebugInfoPanel = ({
  infoRef,
  elementInfo,
  isPinned,
  unpinSelection,
  isConfigOpen,
  toggleConfigPanel,
  config,
  toggleConfig,
  toggleGroupConfig
}) => {
  return (
    <div className="debug-info-panel" ref={infoRef}>
      <div className="debug-panel-header">
        <h3>Debug Information</h3>
        <div className="debug-panel-controls">
          <button
            className={`debug-pin-toggle ${isPinned ? 'active' : ''}`}
            onClick={unpinSelection}
            title={isPinned ? "Unpin Selection" : "Selection is not pinned"}
          >
            {isPinned ? "📌" : "📋"}
          </button>
          <button
            className="debug-config-toggle"
            onClick={toggleConfigPanel}
            title="Configure Debug Options"
          >
            ⚙️
          </button>
        </div>
      </div>

      {isConfigOpen && (
        <div className="debug-config-panel">
          <div className="debug-config-section">
            <h4>Display Options</h4>
            <div className="debug-config-row">
              <div className="debug-config-option">
                <label htmlFor="showOutlines">
                  <input
                    type="checkbox"
                    id="showOutlines"
                    checked={config.showOutlines}
                    onChange={() => toggleConfig('showOutlines')}
                  />
                  <span>Outlines</span>
                </label>
              </div>

              <div className="debug-config-option">
                <label htmlFor="showDimensions">
                  <input
                    type="checkbox"
                    id="showDimensions"
                    checked={config.showDimensions}
                    onChange={() => toggleConfig('showDimensions')}
                  />
                  <span>Dimensions</span>
                </label>
              </div>

              <div className="debug-config-option">
                <label htmlFor="showCenterLines">
                  <input
                    type="checkbox"
                    id="showCenterLines"
                    checked={config.showCenterLines}
                    onChange={() => toggleConfig('showCenterLines')}
                  />
                  <span>All Center Lines</span>
                </label>
              </div>
            </div>
          </div>

          <div className="debug-config-section">
            <h4>Information Sections</h4>
            <div className="debug-config-row">
              <div className="debug-config-option">
                <label htmlFor="showBoxModel">
                  <input
                    type="checkbox"
                    id="showBoxModel"
                    checked={config.showBoxModel}
                    onChange={() => toggleConfig('showBoxModel')}
                  />
                  <span>Box Model</span>
                </label>
              </div>

              <div className="debug-config-option">
                <label htmlFor="showLayout">
                  <input
                    type="checkbox"
                    id="showLayout"
                    checked={config.showLayout}
                    onChange={() => toggleConfig('showLayout')}
                  />
                  <span>Layout</span>
                </label>
              </div>

              <div className="debug-config-option">
                <label htmlFor="showStyle">
                  <input
                    type="checkbox"
                    id="showStyle"
                    checked={config.showStyle}
                    onChange={() => toggleConfig('showStyle')}
                  />
                  <span>Style</span>
                </label>
              </div>
            </div>
          </div>

          <div className="debug-config-section">
            <h4>Interface Elements</h4>

            {/* Dropdown para Conversions */}
            <DebugDropdown
              title="Conversions"
              selectAllId="toggleAllConversions"
              selectAllChecked={config.showConversionBox && config.showConversionControls && config.showConversionSelect}
              onSelectAll={() => toggleGroupConfig('showConversion', !config.showConversionBox)}
            >
              <div className="debug-config-row">
                <div className="debug-config-option">
                  <label htmlFor="showConversions">
                    <input
                      type="checkbox"
                      id="showConversions"
                      checked={config.showConversions}
                      onChange={() => toggleConfig('showConversions')}
                    />
                    <span>Show All Conversions</span>
                  </label>
                </div>
              </div>

              <div className="debug-config-row">
                <div className="debug-config-option">
                  <label htmlFor="showConversionBox">
                    <input
                      type="checkbox"
                      id="showConversionBox"
                      checked={config.showConversionBox}
                      onChange={() => toggleConfig('showConversionBox')}
                    />
                    <span>Conversion Box</span>
                  </label>
                </div>

                <div className="debug-config-option">
                  <label htmlFor="showConversionControls">
                    <input
                      type="checkbox"
                      id="showConversionControls"
                      checked={config.showConversionControls}
                      onChange={() => toggleConfig('showConversionControls')}
                    />
                    <span>Controls</span>
                  </label>
                </div>

                <div className="debug-config-option">
                  <label htmlFor="showConversionSelect">
                    <input
                      type="checkbox"
                      id="showConversionSelect"
                      checked={config.showConversionSelect}
                      onChange={() => toggleConfig('showConversionSelect')}
                    />
                    <span>Select</span>
                  </label>
                </div>
              </div>
            </DebugDropdown>

            {/* Dropdown para Conditions */}
            <DebugDropdown
              title="Conditions"
              selectAllId="toggleAllConditions"
              selectAllChecked={config.showConditionBox && config.showConditionInput}
              onSelectAll={() => toggleGroupConfig('showCondition', !config.showConditionBox)}
            >
              <div className="debug-config-row">
                <div className="debug-config-option">
                  <label htmlFor="showConditions">
                    <input
                      type="checkbox"
                      id="showConditions"
                      checked={config.showConditions}
                      onChange={() => toggleConfig('showConditions')}
                    />
                    <span>Show All Conditions</span>
                  </label>
                </div>
              </div>

              <div className="debug-config-row">
                <div className="debug-config-option">
                  <label htmlFor="showConditionBox">
                    <input
                      type="checkbox"
                      id="showConditionBox"
                      checked={config.showConditionBox}
                      onChange={() => toggleConfig('showConditionBox')}
                    />
                    <span>Condition Box</span>
                  </label>
                </div>

                <div className="debug-config-option">
                  <label htmlFor="showConditionInput">
                    <input
                      type="checkbox"
                      id="showConditionInput"
                      checked={config.showConditionInput}
                      onChange={() => toggleConfig('showConditionInput')}
                    />
                    <span>Input</span>
                  </label>
                </div>
              </div>

              <div className="debug-config-row">
                <div className="debug-config-option">
                  <label htmlFor="showPresets">
                    <input
                      type="checkbox"
                      id="showPresets"
                      checked={config.showPresets}
                      onChange={() => toggleConfig('showPresets')}
                    />
                    <span>Show All Presets</span>
                  </label>
                </div>

                <div className="debug-config-option">
                  <label htmlFor="showPresetButtons">
                    <input
                      type="checkbox"
                      id="showPresetButtons"
                      checked={config.showPresetButtons}
                      onChange={() => toggleConfig('showPresetButtons')}
                    />
                    <span>Preset Buttons</span>
                  </label>
                </div>
              </div>
            </DebugDropdown>

            {/* Dropdown para Compound */}
            <DebugDropdown
              title="Compound"
              selectAllId="toggleAllCompound"
              selectAllChecked={config.showCompoundInput && config.showCompoundResult}
              onSelectAll={() => toggleGroupConfig('showCompound', !config.showCompoundInput)}
            >
              <div className="debug-config-row">
                <div className="debug-config-option">
                  <label htmlFor="showCompound">
                    <input
                      type="checkbox"
                      id="showCompound"
                      checked={config.showCompound}
                      onChange={() => toggleConfig('showCompound')}
                    />
                    <span>Show All Compound</span>
                  </label>
                </div>
              </div>

              <div className="debug-config-row">
                <div className="debug-config-option">
                  <label htmlFor="showCompoundInput">
                    <input
                      type="checkbox"
                      id="showCompoundInput"
                      checked={config.showCompoundInput}
                      onChange={() => toggleConfig('showCompoundInput')}
                    />
                    <span>Compound Input</span>
                  </label>
                </div>

                <div className="debug-config-option">
                  <label htmlFor="showCompoundResult">
                    <input
                      type="checkbox"
                      id="showCompoundResult"
                      checked={config.showCompoundResult}
                      onChange={() => toggleConfig('showCompoundResult')}
                    />
                    <span>Results</span>
                  </label>
                </div>
              </div>
            </DebugDropdown>

            {/* Dropdown para Gas Law */}
            <DebugDropdown
              title="Gas&nbsp;Law"
              selectAllId="toggleAllGasLaw"
              selectAllChecked={config.showGasLawInput && config.showGasLawEquation && config.showGasLawResult}
              onSelectAll={() => toggleGroupConfig('showGasLaw', !config.showGasLawInput)}
            >
              <div className="debug-config-row">
                <div className="debug-config-option">
                  <label htmlFor="showGasLaw">
                    <input
                      type="checkbox"
                      id="showGasLaw"
                      checked={config.showGasLaw}
                      onChange={() => toggleConfig('showGasLaw')}
                    />
                    <span>Show All Gas Law</span>
                  </label>
                </div>
              </div>

              <div className="debug-config-row">
                <div className="debug-config-option">
                  <label htmlFor="showGasLawInput">
                    <input
                      type="checkbox"
                      id="showGasLawInput"
                      checked={config.showGasLawInput}
                      onChange={() => toggleConfig('showGasLawInput')}
                    />
                    <span>Gas Law Input</span>
                  </label>
                </div>

                <div className="debug-config-option">
                  <label htmlFor="showGasLawEquation">
                    <input
                      type="checkbox"
                      id="showGasLawEquation"
                      checked={config.showGasLawEquation}
                      onChange={() => toggleConfig('showGasLawEquation')}
                    />
                    <span>Equation</span>
                  </label>
                </div>

                <div className="debug-config-option">
                  <label htmlFor="showGasLawResult">
                    <input
                      type="checkbox"
                      id="showGasLawResult"
                      checked={config.showGasLawResult}
                      onChange={() => toggleConfig('showGasLawResult')}
                    />
                    <span>Results</span>
                  </label>
                </div>
              </div>
            </DebugDropdown>

            {/* Dropdown para Inputs */}
            <DebugDropdown title="Inputs & Units">
              <div className="debug-config-row">
                <div className="debug-config-option">
                  <label htmlFor="showInputs">
                    <input
                      type="checkbox"
                      id="showInputs"
                      checked={config.showInputs}
                      onChange={() => toggleConfig('showInputs')}
                    />
                    <span>Show All Inputs/Units</span>
                  </label>
                </div>
              </div>
            </DebugDropdown>
          </div>

          <div className="debug-config-section">
            <h4>Element Visibility</h4>

            <DebugDropdown title="Conversions Visibility" selectAllId="toggleAllConversionsVisibility" selectAllChecked={config.showConversion1 && config.showConversion2 && config.showConversion3} onSelectAll={() => toggleGroupConfig('showAllConversions', !config.showConversion1)}>
              <div className="debug-config-row">
                <div className="debug-config-option">
                  <label htmlFor="showConversion1">
                    <input
                      type="checkbox"
                      id="showConversion1"
                      checked={config.showConversion1}
                      onChange={() => toggleConfig('showConversion1')}
                    />
                    <span>Show Conversion 1</span>
                  </label>
                </div>

                <div className="debug-config-option">
                  <label htmlFor="showConversion2">
                    <input
                      type="checkbox"
                      id="showConversion2"
                      checked={config.showConversion2}
                      onChange={() => toggleConfig('showConversion2')}
                    />
                    <span>Show Conversion 2</span>
                  </label>
                </div>
              </div>

              <div className="debug-config-row">
                <div className="debug-config-option">
                  <label htmlFor="showConversion3">
                    <input
                      type="checkbox"
                      id="showConversion3"
                      checked={config.showConversion3}
                      onChange={() => toggleConfig('showConversion3')}
                    />
                    <span>Show Conversion 3</span>
                  </label>
                </div>
              </div>
            </DebugDropdown>

            <DebugDropdown title="Conditions Visibility" selectAllId="toggleAllConditionsVisibility" selectAllChecked={config.showCondition1 && config.showCondition2} onSelectAll={() => toggleGroupConfig('showAllConditions', !config.showCondition1)}>
              <div className="debug-config-row">
                <div className="debug-config-option">
                  <label htmlFor="showCondition1">
                    <input
                      type="checkbox"
                      id="showCondition1"
                      checked={config.showCondition1}
                      onChange={() => toggleConfig('showCondition1')}
                    />
                    <span>Show Condition 1</span>
                  </label>
                </div>

                <div className="debug-config-option">
                  <label htmlFor="showCondition2">
                    <input
                      type="checkbox"
                      id="showCondition2"
                      checked={config.showCondition2}
                      onChange={() => toggleConfig('showCondition2')}
                    />
                    <span>Show Condition 2</span>
                  </label>
                </div>
              </div>
            </DebugDropdown>

            <DebugDropdown title="Presets Visibility" selectAllId="toggleAllPresetsVisibility" selectAllChecked={config.showStpPreset && config.showSatpPreset && config.showRoomTempPreset && config.showCustomPreset} onSelectAll={() => toggleGroupConfig('showAllPresets', !config.showStpPreset)}>
              <div className="debug-config-row">
                <div className="debug-config-option">
                  <label htmlFor="showStpPreset">
                    <input
                      type="checkbox"
                      id="showStpPreset"
                      checked={config.showStpPreset}
                      onChange={() => toggleConfig('showStpPreset')}
                    />
                    <span>Show STP Preset</span>
                  </label>
                </div>

                <div className="debug-config-option">
                  <label htmlFor="showSatpPreset">
                    <input
                      type="checkbox"
                      id="showSatpPreset"
                      checked={config.showSatpPreset}
                      onChange={() => toggleConfig('showSatpPreset')}
                    />
                    <span>Show SATP Preset</span>
                  </label>
                </div>
              </div>

              <div className="debug-config-row">
                <div className="debug-config-option">
                  <label htmlFor="showRoomTempPreset">
                    <input
                      type="checkbox"
                      id="showRoomTempPreset"
                      checked={config.showRoomTempPreset}
                      onChange={() => toggleConfig('showRoomTempPreset')}
                    />
                    <span>Show Room Temp Preset</span>
                  </label>
                </div>

                <div className="debug-config-option">
                  <label htmlFor="showCustomPreset">
                    <input
                      type="checkbox"
                      id="showCustomPreset"
                      checked={config.showCustomPreset}
                      onChange={() => toggleConfig('showCustomPreset')}
                    />
                    <span>Show Custom Preset</span>
                  </label>
                </div>
              </div>
            </DebugDropdown>

            <DebugDropdown title="Compound Visibility" selectAllId="toggleAllCompoundVisibility" selectAllChecked={config.showCompoundSection1 && config.showCompoundSection2} onSelectAll={() => toggleGroupConfig('showAllCompound', !config.showCompoundSection1)}>
              <div className="debug-config-row">
                <div className="debug-config-option">
                  <label htmlFor="showCompoundSection1">
                    <input
                      type="checkbox"
                      id="showCompoundSection1"
                      checked={config.showCompoundSection1}
                      onChange={() => toggleConfig('showCompoundSection1')}
                    />
                    <span>Show Compound Section 1</span>
                  </label>
                </div>

                <div className="debug-config-option">
                  <label htmlFor="showCompoundSection2">
                    <input
                      type="checkbox"
                      id="showCompoundSection2"
                      checked={config.showCompoundSection2}
                      onChange={() => toggleConfig('showCompoundSection2')}
                    />
                    <span>Show Compound Section 2</span>
                  </label>
                </div>
              </div>
            </DebugDropdown>

            <DebugDropdown title="Gas Law Visibility" selectAllId="toggleAllGasLawVisibility" selectAllChecked={config.showGasLawSection1 && config.showGasLawSection2} onSelectAll={() => toggleGroupConfig('showAllGasLaw', !config.showGasLawSection1)}>
              <div className="debug-config-row">
                <div className="debug-config-option">
                  <label htmlFor="showGasLawSection1">
                    <input
                      type="checkbox"
                      id="showGasLawSection1"
                      checked={config.showGasLawSection1}
                      onChange={() => toggleConfig('showGasLawSection1')}
                    />
                    <span>Show Gas Law Section 1</span>
                  </label>
                </div>

                <div className="debug-config-option">
                  <label htmlFor="showGasLawSection2">
                    <input
                      type="checkbox"
                      id="showGasLawSection2"
                      checked={config.showGasLawSection2}
                      onChange={() => toggleConfig('showGasLawSection2')}
                    />
                    <span>Show Gas Law Section 2</span>
                  </label>
                </div>
              </div>
            </DebugDropdown>
          </div>

          <div className="debug-config-section">
            <h4>Interface Center Lines</h4>

            <DebugDropdown title="Center Lines Options">
              <div className="debug-config-row">
                <div className="debug-config-option">
                  <label htmlFor="showCenterLines">
                    <input
                      type="checkbox"
                      id="showCenterLines"
                      checked={config.showCenterLines}
                      onChange={() => toggleConfig('showCenterLines')}
                    />
                    <span>Show All Center Lines</span>
                  </label>
                </div>
              </div>

              <div className="debug-config-row">
                <div className="debug-config-option">
                  <label htmlFor="showConditionsCenterLines">
                    <input
                      type="checkbox"
                      id="showConditionsCenterLines"
                      checked={config.showConditionsCenterLines}
                      onChange={() => toggleConfig('showConditionsCenterLines')}
                    />
                    <span>Conditions</span>
                  </label>
                </div>

                <div className="debug-config-option">
                  <label htmlFor="showPresetsCenterLines">
                    <input
                      type="checkbox"
                      id="showPresetsCenterLines"
                      checked={config.showPresetsCenterLines}
                      onChange={() => toggleConfig('showPresetsCenterLines')}
                    />
                    <span>Presets</span>
                  </label>
                </div>
              </div>

              <div className="debug-config-row">
                <div className="debug-config-option">
                  <label htmlFor="showConversionsCenterLines">
                    <input
                      type="checkbox"
                      id="showConversionsCenterLines"
                      checked={config.showConversionsCenterLines}
                      onChange={() => toggleConfig('showConversionsCenterLines')}
                    />
                    <span>Conversions</span>
                  </label>
                </div>

                <div className="debug-config-option">
                  <label htmlFor="showCompoundCenterLines">
                    <input
                      type="checkbox"
                      id="showCompoundCenterLines"
                      checked={config.showCompoundCenterLines}
                      onChange={() => toggleConfig('showCompoundCenterLines')}
                    />
                    <span>Compound</span>
                  </label>
                </div>
              </div>

              <div className="debug-config-row">
                <div className="debug-config-option">
                  <label htmlFor="showGasLawCenterLines">
                    <input
                      type="checkbox"
                      id="showGasLawCenterLines"
                      checked={config.showGasLawCenterLines}
                      onChange={() => toggleConfig('showGasLawCenterLines')}
                    />
                    <span>Gas Law</span>
                  </label>
                </div>
              </div>

              <div className="debug-config-row">
                <div className="debug-config-option">
                  <label htmlFor="showParameterPanelCenterLines">
                    <input
                      type="checkbox"
                      id="showParameterPanelCenterLines"
                      checked={config.showParameterPanelCenterLines}
                      onChange={() => toggleConfig('showParameterPanelCenterLines')}
                    />
                    <span>Parameter Panel</span>
                  </label>
                </div>

                <div className="debug-config-option">
                  <label htmlFor="showResultsPanelCenterLines">
                    <input
                      type="checkbox"
                      id="showResultsPanelCenterLines"
                      checked={config.showResultsPanelCenterLines}
                      onChange={() => toggleConfig('showResultsPanelCenterLines')}
                    />
                    <span>Results Panel</span>
                  </label>
                </div>
              </div>
            </DebugDropdown>
          </div>

          <div className="debug-config-section">
            <h4>Guide Grids</h4>
            <div className="debug-config-row">
              <div className="debug-config-option">
                <label htmlFor="showFullGrid">
                  <input
                    type="checkbox"
                    id="showFullGrid"
                    checked={config.showFullGrid}
                    onChange={() => toggleConfig('showFullGrid')}
                  />
                  <span>Full Grid</span>
                </label>
              </div>

              <div className="debug-config-option">
                <label htmlFor="showCenterGrid">
                  <input
                    type="checkbox"
                    id="showCenterGrid"
                    checked={config.showCenterGrid}
                    onChange={() => toggleConfig('showCenterGrid')}
                  />
                  <span>Center Grid</span>
                </label>
              </div>

              <div className="debug-config-option">
                <label htmlFor="showRuleOfThirds">
                  <input
                    type="checkbox"
                    id="showRuleOfThirds"
                    checked={config.showRuleOfThirds}
                    onChange={() => toggleConfig('showRuleOfThirds')}
                  />
                  <span>Rule of Thirds</span>
                </label>
              </div>
            </div>
          </div>
        </div>
      )}

      <div className="debug-element-info">
        {elementInfo.tagName ? (
          <>
            <div className="debug-info-section">
              <h4>Element</h4>
              <div className="debug-info-row">
                <span className="debug-info-label">Tag:</span>
                <span className="debug-info-value">{elementInfo.tagName}</span>
              </div>
              <div className="debug-info-row">
                <span className="debug-info-label">Class:</span>
                <span className="debug-info-value">{elementInfo.className || 'none'}</span>
              </div>
              <div className="debug-info-row">
                <span className="debug-info-label">ID:</span>
                <span className="debug-info-value">{elementInfo.id}</span>
              </div>
            </div>

            {config.showBoxModel && (
              <div className="debug-info-section">
                <h4>Box Model</h4>
                <div className="debug-info-row">
                  <span className="debug-info-label">Width:</span>
                  <span className="debug-info-value">{elementInfo.width}px</span>
                </div>
                <div className="debug-info-row">
                  <span className="debug-info-label">Height:</span>
                  <span className="debug-info-value">{elementInfo.height}px</span>
                </div>
                <div className="debug-info-row">
                  <span className="debug-info-label">Padding:</span>
                  <span className="debug-info-value">{elementInfo.padding}</span>
                </div>
                <div className="debug-info-row">
                  <span className="debug-info-label">Border:</span>
                  <span className="debug-info-value">{elementInfo.border}</span>
                </div>
                <div className="debug-info-row">
                  <span className="debug-info-label">Margin:</span>
                  <span className="debug-info-value">{elementInfo.margin}</span>
                </div>
              </div>
            )}

            {config.showLayout && (
              <div className="debug-info-section">
                <h4>Layout</h4>
                <div className="debug-info-row">
                  <span className="debug-info-label">Position:</span>
                  <span className="debug-info-value">{elementInfo.position}</span>
                </div>
                <div className="debug-info-row">
                  <span className="debug-info-label">Top:</span>
                  <span className="debug-info-value">{elementInfo.top}px</span>
                </div>
                <div className="debug-info-row">
                  <span className="debug-info-label">Left:</span>
                  <span className="debug-info-value">{elementInfo.left}px</span>
                </div>
                <div className="debug-info-row">
                  <span className="debug-info-label">Display:</span>
                  <span className="debug-info-value">{elementInfo.display}</span>
                </div>
                <div className="debug-info-row">
                  <span className="debug-info-label">Z-Index:</span>
                  <span className="debug-info-value">{elementInfo.zIndex}</span>
                </div>
              </div>
            )}

            {config.showStyle && (
              <div className="debug-info-section">
                <h4>Style</h4>
                <div className="debug-info-row">
                  <span className="debug-info-label">Color:</span>
                  <span className="debug-info-value">{elementInfo.color}</span>
                </div>
                <div className="debug-info-row">
                  <span className="debug-info-label">Background:</span>
                  <span className="debug-info-value">{elementInfo.backgroundColor}</span>
                </div>
                {elementInfo.flexDirection && (
                  <>
                    <div className="debug-info-row">
                      <span className="debug-info-label">Flex Direction:</span>
                      <span className="debug-info-value">{elementInfo.flexDirection}</span>
                    </div>
                    <div className="debug-info-row">
                      <span className="debug-info-label">Justify Content:</span>
                      <span className="debug-info-value">{elementInfo.justifyContent}</span>
                    </div>
                    <div className="debug-info-row">
                      <span className="debug-info-label">Align Items:</span>
                      <span className="debug-info-value">{elementInfo.alignItems}</span>
                    </div>
                    <div className="debug-info-row">
                      <span className="debug-info-label">Gap:</span>
                      <span className="debug-info-value">{elementInfo.gap}</span>
                    </div>
                  </>
                )}
              </div>
            )}
          </>
        ) : (
          <div className="debug-info-empty">
            <p>Hover over an element to see its information</p>
            <p>Click on an element to pin its information</p>
          </div>
        )}
      </div>
    </div>
  );
};

export default DebugInfoPanel;
