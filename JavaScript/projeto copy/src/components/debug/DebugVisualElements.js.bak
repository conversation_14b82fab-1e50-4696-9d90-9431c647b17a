import { useEffect, useRef, useCallback } from 'react';
import '../../styles/debug-visual-elements.css';

/**
 * Componente para adicionar elementos visuais de debug (bordas, linhas, etc.)
 */
const DebugVisualElements = ({ isDebugMode, config }) => {
  const debugContainerRef = useRef(null);

  // Função para limpar completamente o DOM de elementos de debug
  const cleanupDebugElements = useCallback(() => {
    try {
      // Remover o container de debug de forma eficiente
      if (debugContainerRef.current) {
        // Esconder o container antes de removê-lo para evitar piscadas
        debugContainerRef.current.style.display = 'none';
        debugContainerRef.current.style.opacity = '0';
        
        // Limpar o conteúdo do container
        debugContainerRef.current.innerHTML = '';
        
        // Remover o container do DOM
        if (debugContainerRef.current.parentNode) {
          debugContainerRef.current.parentNode.removeChild(debugContainerRef.current);
        }
        debugContainerRef.current = null;
      }

      // Remover todos os elementos com classes de debug
      const debugSelectors = [
        '.debug-dimension-label',
        '.center-line-x',
        '.center-line-y',
        '.debug-outline',
        '.debug-grid-line',
        '.debug-center-line',
        '.debug-thirds-line',
        '.horizontal',
        '.vertical',
        '.debug-element',
        '.center-point',
        '#debug-elements-container'
      ];

      // Remover cada tipo de elemento
      debugSelectors.forEach(selector => {
        document.querySelectorAll(selector).forEach(el => {
          if (el && el.parentNode) {
            el.parentNode.removeChild(el);
          }
        });
      });

      // Remover todos os elementos com a classe debug-element
      document.querySelectorAll('.debug-element').forEach(el => {
        if (el && el.parentNode) {
          el.parentNode.removeChild(el);
        }
      });

      // Remover todos os elementos com a classe center-line-x
      document.querySelectorAll('.center-line-x').forEach(el => {
        if (el && el.parentNode) {
          el.parentNode.removeChild(el);
        }
      });

      // Remover todos os elementos com a classe center-line-y
      document.querySelectorAll('.center-line-y').forEach(el => {
        if (el && el.parentNode) {
          el.parentNode.removeChild(el);
        }
      });

      // Remover todos os elementos com a classe center-point
      document.querySelectorAll('.center-point').forEach(el => {
        if (el && el.parentNode) {
          el.parentNode.removeChild(el);
        }
      });

      // Restaurar a visibilidade de elementos ocultados
      document.querySelectorAll('[style*="display: none"]').forEach(el => {
        if (el.classList.contains('preset-button') || el.classList.contains('condition-input')) {
          el.style.display = '';
        }
      });

      // Remover a classe debug-mode-active do body
      document.body.classList.remove('debug-mode-active');

      // Forçar uma limpeza adicional após um pequeno atraso
      setTimeout(() => {
        // Remover novamente todos os elementos de debug que possam ter sido adicionados
        debugSelectors.forEach(selector => {
          document.querySelectorAll(selector).forEach(el => {
            if (el && el.parentNode) {
              el.parentNode.removeChild(el);
            }
          });
        });
      }, 100);

      console.log('Debug elements cleaned up');
    } catch (error) {
      console.error('Error cleaning up debug elements:', error);
    }
  }, []);

  // Função para adicionar elementos de debug
  const addDebugElements = useCallback(() => {
    try {
      // Criar ou obter o container para os elementos de debug
      let container = debugContainerRef.current;

      if (!container) {
        container = document.createElement('div');
        container.id = 'debug-elements-container';
        container.style.cssText = `
          position: fixed;
          top: 0;
          left: 0;
          width: 100vw;
          height: 100vh;
          pointer-events: none;
          z-index: 9997;
          overflow: visible;
          opacity: 0;
          transition: opacity 0.1s ease;
        `;
        document.body.appendChild(container);
        debugContainerRef.current = container;
      }

      // Esconder o container enquanto atualizamos seu conteúdo para evitar piscadas
      container.style.opacity = '0';
      
      // Limpar o container para evitar duplicação de elementos
      container.innerHTML = '';

      // Criar um fragmento para adicionar todos os elementos de uma vez
      const fragment = document.createDocumentFragment();

      // Selecionar elementos para mostrar informações
      const elements = [];

      // Adicionar elementos com base nas configurações
      // Conditions
      if (config.showConditions) {
        document.querySelectorAll('.conditions-container, .conditions-block, .calculator-block').forEach(el => elements.push(el));
      }

      if (config.showConditionBox) {
        document.querySelectorAll('.condition-box').forEach(el => elements.push(el));
      }

      if (config.showConditionInput) {
        document.querySelectorAll('.condition-input').forEach(el => elements.push(el));
      }

      // Presets
      if (config.showPresets) {
        document.querySelectorAll('.presets-container').forEach(el => elements.push(el));
      }

      if (config.showPresetButtons) {
        document.querySelectorAll('.preset-button').forEach(el => elements.push(el));
      }

      // Inputs
      if (config.showInputs) {
        document.querySelectorAll('.condition-input, .conversion-input, .parameter-input').forEach(el => elements.push(el));
      }

      // Conversions
      if (config.showConversions) {
        document.querySelectorAll('.conversion-container, .conversions-block, .calculator-block').forEach(el => elements.push(el));
      }

      if (config.showConversionBox) {
        document.querySelectorAll('.conversion-box').forEach(el => elements.push(el));
      }

      if (config.showConversionControls) {
        document.querySelectorAll('.conversion-controls, .swap-button, .copy-button').forEach(el => elements.push(el));
      }

      if (config.showConversionSelect) {
        document.querySelectorAll('.conversion-select, .unit-select').forEach(el => elements.push(el));
      }

      // Compound
      if (config.showCompound) {
        document.querySelectorAll('.compound-container, .compound-info-block, .calculator-block').forEach(el => elements.push(el));
      }

      if (config.showCompoundInput) {
        document.querySelectorAll('.compound-input').forEach(el => elements.push(el));
      }

      if (config.showCompoundResult) {
        document.querySelectorAll('.compound-result').forEach(el => elements.push(el));
      }

      // Gas Law
      if (config.showGasLaw) {
        document.querySelectorAll('.gas-law-container, .gas-law-block, .calculator-block').forEach(el => elements.push(el));
      }

      if (config.showGasLawInput) {
        document.querySelectorAll('.gas-law-input').forEach(el => elements.push(el));
      }

      if (config.showGasLawEquation) {
        document.querySelectorAll('.gas-law-equation').forEach(el => elements.push(el));
      }

      if (config.showGasLawResult) {
        document.querySelectorAll('.gas-law-result').forEach(el => elements.push(el));
      }

      // Adicionar labels de dimensões e outlines
      elements.forEach(element => {
        if (!element) return;

        const rect = element.getBoundingClientRect();
        const elementId = `${element.tagName}-${Math.round(rect.left)}-${Math.round(rect.top)}-${Math.round(rect.width)}-${Math.round(rect.height)}`;

        // Adicionar label de dimensões se configurado
        if (config.showDimensions) {
          const dimensionLabel = document.createElement('div');
          dimensionLabel.id = `dimension-label-${elementId}`;
          dimensionLabel.classList.add('debug-dimension-label');
          dimensionLabel.textContent = `${Math.round(rect.width)}x${Math.round(rect.height)}`;
          dimensionLabel.style.cssText = `
            position: fixed;
            top: ${window.scrollY + rect.bottom}px;
            left: ${window.scrollX + rect.right - 50}px;
            font-size: 9px;
            color: black;
            background-color: rgba(255, 255, 255, 0.7);
            padding: 1px 3px;
            border-radius: 2px;
            z-index: 9999;
            pointer-events: none;
          `;

          // Adicionar o label ao fragmento
          fragment.appendChild(dimensionLabel);
        }

        // Adicionar outline ao elemento se configurado
        if (config.showOutlines) {
          const outline = document.createElement('div');
          outline.id = `outline-${elementId}`;
          outline.classList.add('debug-outline');
          outline.style.cssText = `
            position: fixed;
            top: ${window.scrollY + rect.top}px;
            left: ${window.scrollX + rect.left}px;
            width: ${rect.width}px;
            height: ${rect.height}px;
            border: 2px dashed rgba(255, 0, 0, 0.7);
            box-sizing: border-box;
            pointer-events: none;
            z-index: 9998;
          `;

          // Adicionar o outline ao fragmento
          fragment.appendChild(outline);
        }
      });

      // Função para adicionar linhas de centro a um elemento
      const addCenterLinesToElement = (element, color = 'rgba(255, 0, 0, 0.9)') => {
        if (!element) return;

        const rect = element.getBoundingClientRect();
        const elementId = `${element.tagName}-${Math.round(rect.left)}-${Math.round(rect.top)}-${Math.round(rect.width)}-${Math.round(rect.height)}`;

        // Linha horizontal central
        const centerLineX = document.createElement('div');
        centerLineX.id = `center-line-x-${elementId}`;
        centerLineX.classList.add('center-line-x', 'debug-element');
        centerLineX.style.cssText = `
          position: fixed;
          top: ${window.scrollY + rect.top + rect.height / 2}px;
          left: ${window.scrollX + rect.left}px;
          width: ${rect.width}px;
          height: 3px;
          background-color: ${color};
          pointer-events: none;
          z-index: 9998;
          box-shadow: 0 0 4px rgba(0, 0, 0, 0.7);
        `;

        // Adicionar a linha horizontal ao fragmento
        fragment.appendChild(centerLineX);

        // Linha vertical central
        const centerLineY = document.createElement('div');
        centerLineY.id = `center-line-y-${elementId}`;
        centerLineY.classList.add('center-line-y', 'debug-element');
        centerLineY.style.cssText = `
          position: fixed;
          top: ${window.scrollY + rect.top}px;
          left: ${window.scrollX + rect.left + rect.width / 2}px;
          width: 3px;
          height: ${rect.height}px;
          background-color: ${color};
          pointer-events: none;
          z-index: 9998;
          box-shadow: 0 0 4px rgba(0, 0, 0, 0.7);
        `;

        // Adicionar a linha vertical ao fragmento
        fragment.appendChild(centerLineY);

        // Adicionar um ponto de interseção no centro
        const centerPoint = document.createElement('div');
        centerPoint.id = `center-point-${elementId}`;
        centerPoint.classList.add('center-point', 'debug-element');
        centerPoint.style.cssText = `
          position: fixed;
          top: ${window.scrollY + rect.top + rect.height / 2 - 5}px;
          left: ${window.scrollX + rect.left + rect.width / 2 - 5}px;
          width: 10px;
          height: 10px;
          background-color: ${color};
          border-radius: 50%;
          pointer-events: none;
          z-index: 9999;
          box-shadow: 0 0 5px rgba(0, 0, 0, 0.9);
        `;

        // Adicionar o ponto central ao fragmento
        fragment.appendChild(centerPoint);

        // Adicionar rótulos de dimensões
        const dimensionLabel = document.createElement('div');
        dimensionLabel.id = `dimension-label-center-${elementId}`;
        dimensionLabel.classList.add('debug-dimension-label', 'debug-element');
        dimensionLabel.style.cssText = `
          position: fixed;
          top: ${window.scrollY + rect.top - 20}px;
          left: ${window.scrollX + rect.left + rect.width / 2 - 50}px;
          width: 100px;
          height: 20px;
          background-color: rgba(0, 0, 0, 0.7);
          color: white;
          font-size: 12px;
          text-align: center;
          line-height: 20px;
          border-radius: 3px;
          pointer-events: none;
          z-index: 9999;
        `;
        dimensionLabel.textContent = `${Math.round(rect.width)} x ${Math.round(rect.height)}`;

        // Adicionar o rótulo de dimensões ao fragmento
        fragment.appendChild(dimensionLabel);
      };

      // Adicionar linhas de centro para todos os elementos
      if (config.showCenterLines) {
        // Selecionar apenas os elementos principais para evitar sobrecarga
        // Reduzimos significativamente o número de elementos para melhorar o desempenho
        const mainSelectors = [
          '.calculator-block',
          '.conversions-block',
          '.conditions-block',
          '.compound-info-block',
          '.gas-law-block'
        ];

        document.querySelectorAll(mainSelectors.join(', ')).forEach(element => {
          // Verificar se o elemento é visível e tem dimensões
          const rect = element.getBoundingClientRect();
          if (rect.width > 0 && rect.height > 0) {
            // Adicionar linhas de centro
            addCenterLinesToElement(element, 'rgba(255, 0, 0, 0.7)');

            // Adicionar também uma borda tracejada para cada elemento
            const elementId = `${element.tagName}-${Math.round(rect.left)}-${Math.round(rect.top)}-${Math.round(rect.width)}-${Math.round(rect.height)}`;
            const outline = document.createElement('div');
            outline.id = `outline-centerline-${elementId}`;
            outline.classList.add('debug-outline');
            outline.style.cssText = `
              position: fixed;
              top: ${window.scrollY + rect.top}px;
              left: ${window.scrollX + rect.left}px;
              width: ${rect.width}px;
              height: ${rect.height}px;
              border: 2px dashed rgba(255, 0, 0, 0.5);
              box-sizing: border-box;
              pointer-events: none;
              z-index: 9998;
            `;
            fragment.appendChild(outline);
          }
        });
      }

      // Adicionar linhas de centro para cada interface específica
      if (config.showConditionsCenterLines) {
        document.querySelectorAll('.conditions-container, .condition-row, .condition-input, .condition-box, .conditions-block, .calculator-block').forEach(element => {
          addCenterLinesToElement(element, 'rgba(255, 0, 0, 0.9)');
        });
      }

      if (config.showPresetsCenterLines) {
        document.querySelectorAll('.presets-container, .preset-button').forEach(element => {
          addCenterLinesToElement(element, 'rgba(0, 255, 0, 0.9)');
        });
      }

      if (config.showConversionsCenterLines) {
        document.querySelectorAll('.conversion-container, .conversion-row, .conversion-input-container, .conversion-input, .conversions-block, .calculator-block').forEach(element => {
          addCenterLinesToElement(element, 'rgba(0, 0, 255, 0.9)');
        });
      }

      if (config.showCompoundCenterLines) {
        document.querySelectorAll('.compound-container, .compound-input, .compound-result, .compound-info-block, .calculator-block').forEach(element => {
          addCenterLinesToElement(element, 'rgba(255, 255, 0, 0.9)');
        });
      }

      if (config.showGasLawCenterLines) {
        document.querySelectorAll('.gas-law-container, .gas-law-row, .gas-law-input, .gas-law-result, .gas-law-block, .calculator-block').forEach(element => {
          addCenterLinesToElement(element, 'rgba(255, 0, 255, 0.9)');
        });
      }

      if (config.showParameterPanelCenterLines) {
        document.querySelectorAll('.parameter-panel, .parameter-row, .parameter-input, .parameter-label').forEach(element => {
          addCenterLinesToElement(element, 'rgba(0, 255, 255, 0.9)');
        });
      }

      if (config.showResultsPanelCenterLines) {
        document.querySelectorAll('.results-panel, .results-row, .results-value, .results-label').forEach(element => {
          addCenterLinesToElement(element, 'rgba(255, 165, 0, 0.9)');
        });
      }

      // Adicionar grid de guias se configurado
      if (config.showFullGrid) {
        // Adicionar linhas horizontais a cada 50px
        for (let y = 0; y < window.innerHeight; y += 50) {
          const gridLine = document.createElement('div');
          gridLine.classList.add('debug-grid-line', 'horizontal');
          gridLine.style.cssText = `
            position: fixed;
            top: ${y}px;
            left: 0;
            width: 100%;
            height: 1px;
            background-color: rgba(0, 0, 0, 0.1);
            pointer-events: none;
            z-index: 9996;
          `;
          fragment.appendChild(gridLine);
        }

        // Adicionar linhas verticais a cada 50px
        for (let x = 0; x < window.innerWidth; x += 50) {
          const gridLine = document.createElement('div');
          gridLine.classList.add('debug-grid-line', 'vertical');
          gridLine.style.cssText = `
            position: fixed;
            top: 0;
            left: ${x}px;
            width: 1px;
            height: 100%;
            background-color: rgba(0, 0, 0, 0.1);
            pointer-events: none;
            z-index: 9996;
          `;
          fragment.appendChild(gridLine);
        }
      }

      // Adicionar linhas de centro da tela se configurado
      if (config.showCenterGrid) {
        // Linha horizontal central
        const centerLineX = document.createElement('div');
        centerLineX.classList.add('debug-center-line', 'horizontal');
        centerLineX.style.cssText = `
          position: fixed;
          top: ${window.innerHeight / 2}px;
          left: 0;
          width: 100%;
          height: 1px;
          background-color: rgba(255, 0, 0, 0.5);
          pointer-events: none;
          z-index: 9996;
        `;
        fragment.appendChild(centerLineX);

        // Linha vertical central
        const centerLineY = document.createElement('div');
        centerLineY.classList.add('debug-center-line', 'vertical');
        centerLineY.style.cssText = `
          position: fixed;
          top: 0;
          left: ${window.innerWidth / 2}px;
          width: 1px;
          height: 100%;
          background-color: rgba(255, 0, 0, 0.5);
          pointer-events: none;
          z-index: 9996;
        `;
        fragment.appendChild(centerLineY);
      }

      // Adicionar linhas de terços se configurado (regra dos terços)
      if (config.showRuleOfThirds) {
        // Linhas horizontais nos terços
        for (let i = 1; i <= 2; i++) {
          const thirdsLineX = document.createElement('div');
          thirdsLineX.classList.add('debug-thirds-line', 'horizontal');
          thirdsLineX.style.cssText = `
            position: fixed;
            top: ${(window.innerHeight / 3) * i}px;
            left: 0;
            width: 100%;
            height: 1px;
            background-color: rgba(0, 0, 255, 0.5);
            pointer-events: none;
            z-index: 9996;
          `;
          fragment.appendChild(thirdsLineX);
        }

        // Linhas verticais nos terços
        for (let i = 1; i <= 2; i++) {
          const thirdsLineY = document.createElement('div');
          thirdsLineY.classList.add('debug-thirds-line', 'vertical');
          thirdsLineY.style.cssText = `
            position: fixed;
            top: 0;
            left: ${(window.innerWidth / 3) * i}px;
            width: 1px;
            height: 100%;
            background-color: rgba(0, 0, 255, 0.5);
            pointer-events: none;
            z-index: 9996;
          `;
          fragment.appendChild(thirdsLineY);
        }
      }

      // Ocultar elementos específicos se configurado
      if (config.hideStpPreset) {
        document.querySelectorAll('.preset-button[data-preset="stp"]').forEach(el => {
          el.style.display = 'none';
        });
      }

      if (config.hideSatpPreset) {
        document.querySelectorAll('.preset-button[data-preset="satp"]').forEach(el => {
          el.style.display = 'none';
        });
      }

      if (config.hideRoomTempPreset) {
        document.querySelectorAll('.preset-button[data-preset="room"]').forEach(el => {
          el.style.display = 'none';
        });
      }

      if (config.hideCustomPreset) {
        document.querySelectorAll('.preset-button[data-preset="custom"]').forEach(el => {
          el.style.display = 'none';
        });
      }

      // Adicionar todos os elementos ao container de uma vez
      container.appendChild(fragment);
      
      // Mostrar o container após adicionar todos os elementos
      // Usar requestAnimationFrame para garantir que a exibição ocorra no próximo frame
      requestAnimationFrame(() => {
        container.style.opacity = '1';
      });

      return () => {
        // Função vazia para compatibilidade com o código existente
      };
    } catch (error) {
      console.error('Error adding debug elements:', error);
      return () => {};
    }
  }, [config]);

  // Efeito para atualizar os elementos visuais quando o modo debug é ativado/desativado
  // ou quando as configurações são alteradas
  useEffect(() => {
    // Função para atualizar os elementos de debug em tempo real
    const updateDebugElements = () => {
      if (!isDebugMode) return;
      
      // Limpar elementos existentes para evitar duplicação
      cleanupDebugElements();
      
      // Usar requestAnimationFrame para sincronizar com o ciclo de renderização
      requestAnimationFrame(() => {
        // Verificar novamente se o modo debug ainda está ativo
        if (!isDebugMode) return;
        
        // Adicionar elementos de debug
        addDebugElements();
      });
    };

    // Função de debounce para limitar a frequência de execução
    function debounce(func, wait) {
      let timeout;
      return function(...args) {
        clearTimeout(timeout);
        timeout = setTimeout(() => func.apply(this, args), wait);
      };
    }

    // Função para lidar com eventos de resize
    const handleResize = debounce(() => {
      if (isDebugMode) {
        updateDebugElements();
      }
    }, 300); // Aumentado para 300ms para evitar sobrecarga

    // Função para lidar com eventos de scroll
    const handleScroll = debounce(() => {
      if (isDebugMode) {
        updateDebugElements();
      }
    }, 300); // Aumentado para 300ms para evitar sobrecarga

    // Função para lidar com mudanças na interface
    const handleInterfaceChange = () => {
      if (isDebugMode) {
        updateDebugElements();
      }
    };

    // Observar mudanças no DOM para detectar alterações na interface
    const observer = new MutationObserver(handleInterfaceChange);

    if (isDebugMode) {
      // Ativar modo debug
      document.body.classList.add('debug-mode-active');

      // Adicionar elementos de debug
      updateDebugElements();

      // Adicionar listeners para eventos de resize, scroll e mouse
      window.addEventListener('resize', handleResize);
      window.addEventListener('scroll', handleScroll, { passive: true });

      // Eventos essenciais para atualizações em tempo real
      document.addEventListener('click', updateDebugElements);
      document.addEventListener('input', updateDebugElements);

      // Adicionar listener para o evento personalizado de mudança de configuração
      // Este evento é disparado pelo componente DebugMode quando uma configuração é alterada
      let configChangeTimeout = null;
      const handleConfigChange = (event) => {
        if (!isDebugMode) return;
        
        // Cancelar qualquer atualização pendente para evitar múltiplas atualizações
        if (configChangeTimeout) {
          clearTimeout(configChangeTimeout);
        }
        
        // Agendar uma única atualização após um pequeno atraso
        configChangeTimeout = setTimeout(() => {
          // Esconder o container antes de atualizar para evitar piscadas
          if (debugContainerRef.current) {
            debugContainerRef.current.style.opacity = '0';
          }
          
          // Atualizar os elementos de debug
          addDebugElements();
          configChangeTimeout = null;
        }, 200); // Atraso maior para evitar sobrecarga
      };

      document.addEventListener('debug-config-changed', handleConfigChange);

      // Observar apenas mudanças significativas para evitar sobrecarga
      observer.observe(document.body, {
        childList: true,
        subtree: false, // Não observar todos os descendentes para evitar sobrecarga
        attributes: false // Não observar todas as mudanças de atributos
      });

      // Observar apenas os elementos principais que realmente importam
      const mainElements = document.querySelectorAll('.calculator-block, .tabs');
      mainElements.forEach(element => {
        observer.observe(element, {
          attributes: true,
          childList: true,
          subtree: false, // Não observar todos os descendentes para evitar sobrecarga
          attributeFilter: ['class', 'style'] // Observar apenas mudanças de classe e estilo
        });
      });

      // Atualizar periodicamente para garantir que tudo esteja atualizado
      // Usar um intervalo maior para evitar sobrecarga e travamento
      const intervalId = setInterval(updateDebugElements, 5000); // 5 segundos

      // Cleanup
      return () => {
        clearInterval(intervalId);
        window.removeEventListener('resize', handleResize);
        window.removeEventListener('scroll', handleScroll);
        document.removeEventListener('click', updateDebugElements);
        document.removeEventListener('input', updateDebugElements);
        document.removeEventListener('debug-config-changed', handleConfigChange);
        observer.disconnect();
        cleanupDebugElements();
      };
    } else {
      // Desativar modo debug
      cleanupDebugElements();

      // Forçar uma limpeza adicional após um pequeno atraso
      setTimeout(() => {
        cleanupDebugElements();
      }, 100);

      // Forçar outra limpeza após um atraso maior
      setTimeout(() => {
        cleanupDebugElements();
      }, 500);
    }
  }, [isDebugMode, addDebugElements, cleanupDebugElements]);

  // Este componente não renderiza nada visível diretamente
  return null;
};

export default DebugVisualElements;
