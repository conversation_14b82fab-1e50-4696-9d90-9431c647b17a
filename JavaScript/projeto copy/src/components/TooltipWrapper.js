import React from 'react';

/**
 * Componente para adicionar tooltips a qualquer elemento
 * @param {Object} props - Propriedades do componente
 * @param {React.ReactNode} props.children - O elemento ao qual adicionar o tooltip
 * @param {string} props.text - O texto do tooltip
 * @param {string} props.position - A posição do tooltip ('top', 'bottom', 'left', 'right')
 */
const TooltipWrapper = ({ children, text, position = 'top' }) => {
  if (!text) return children;
  
  // Clonar o elemento filho e adicionar o atributo data-tooltip
  return React.cloneElement(children, {
    'data-tooltip': text,
    'data-tooltip-position': position,
    className: `${children.props.className || ''} has-tooltip`
  });
};

export default TooltipWrapper;
