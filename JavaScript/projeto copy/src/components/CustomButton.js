import React, { useRef, useEffect } from 'react';
import '../styles/qol-improvements.css';

/**
 * Custom button component with ripple effect and improved styling
 * @param {Object} props - Component props
 * @param {React.ReactNode} props.children - Button content
 * @param {string} props.variant - Button variant ('primary', 'secondary', 'text')
 * @param {Function} props.onClick - Click event handler
 * @param {boolean} props.disabled - Whether the button is disabled
 * @param {boolean} props.isLoading - Whether the button is in loading state
 * @param {string} props.className - Additional CSS class
 * @param {string} props.tooltip - Tooltip text
 * @param {string} props.shortcut - Keyboard shortcut (e.g., 'Alt+S')
 */
const CustomButton = ({ 
  children, 
  variant = 'primary', 
  onClick, 
  disabled = false, 
  isLoading = false, 
  className = '',
  tooltip = '',
  shortcut = '',
  ...rest
}) => {
  const buttonRef = useRef(null);
  
  // Add ripple effect
  useEffect(() => {
    const button = buttonRef.current;
    
    const handleClick = (e) => {
      const rect = button.getBoundingClientRect();
      const x = e.clientX - rect.left;
      const y = e.clientY - rect.top;
      
      const ripple = document.createElement('span');
      ripple.className = 'ripple';
      ripple.style.left = `${x}px`;
      ripple.style.top = `${y}px`;
      
      button.appendChild(ripple);
      
      setTimeout(() => {
        ripple.remove();
      }, 600);
    };
    
    button.addEventListener('click', handleClick);
    
    return () => {
      button.removeEventListener('click', handleClick);
    };
  }, []);
  
  // Add keyboard shortcut
  useEffect(() => {
    if (!shortcut) return;
    
    const handleKeyDown = (e) => {
      // Parse the shortcut string (e.g., "Alt+S")
      const keys = shortcut.split('+');
      const key = keys.pop().toLowerCase();
      const needsAlt = keys.includes('Alt');
      const needsCtrl = keys.includes('Ctrl');
      const needsShift = keys.includes('Shift');
      
      // Check if the pressed keys match the shortcut
      if (
        e.key.toLowerCase() === key &&
        e.altKey === needsAlt &&
        e.ctrlKey === needsCtrl &&
        e.shiftKey === needsShift
      ) {
        e.preventDefault();
        buttonRef.current.click();
        
        // Add visual feedback
        buttonRef.current.classList.add('shortcut-activated');
        setTimeout(() => {
          buttonRef.current.classList.remove('shortcut-activated');
        }, 200);
      }
    };
    
    document.addEventListener('keydown', handleKeyDown);
    
    return () => {
      document.removeEventListener('keydown', handleKeyDown);
    };
  }, [shortcut]);
  
  return (
    <button
      ref={buttonRef}
      className={`custom-button ${variant} ${className} ${isLoading ? 'loading' : ''}`}
      onClick={onClick}
      disabled={disabled || isLoading}
      data-tooltip={tooltip}
      data-shortcut={shortcut}
      {...rest}
    >
      {children}
    </button>
  );
};

export default CustomButton;
