import React from 'react';
import Select from 'react-select';
import TooltipWrapper from './TooltipWrapper';

/**
 * Componente de seleção melhorado com tooltip e estilos consistentes
 * @param {Object} props - Propriedades do componente
 * @param {Array} props.options - Opções do select
 * @param {Object} props.value - Valor selecionado
 * @param {Function} props.onChange - Função de mudança
 * @param {Object} props.styles - Estilos personalizados
 * @param {boolean} props.isSearchable - Se o select permite busca
 * @param {Object} props.components - Componentes personalizados
 * @param {string} props.className - Classes CSS adicionais
 * @param {string} props.tooltip - Texto do tooltip
 * @param {boolean} props.isDisabled - Se o select está desabilitado
 */
const EnhancedSelect = ({
  options,
  value,
  onChange,
  styles = {},
  isSearchable = false,
  components = {},
  className = '',
  tooltip = '',
  isDisabled = false,
  ...rest
}) => {
  // Criar o select base
  const select = (
    <div className={`enhanced-select-container ${className}`}>
      <Select
        options={options}
        value={value}
        onChange={onChange}
        styles={styles}
        isSearchable={isSearchable}
        components={{
          IndicatorSeparator: () => null,
          ...components
        }}
        isDisabled={isDisabled}
        className="enhanced-select"
        classNamePrefix="enhanced-select"
        {...rest}
      />
    </div>
  );
  
  // Adicionar tooltip se fornecido
  return tooltip ? <TooltipWrapper text={tooltip}>{select}</TooltipWrapper> : select;
};

export default EnhancedSelect;
