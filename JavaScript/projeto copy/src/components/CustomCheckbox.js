import React from 'react';
import '../styles/qol-improvements.css';

/**
 * Custom checkbox component with improved styling and accessibility
 * @param {Object} props - Component props
 * @param {string} props.id - Checkbox ID
 * @param {string} props.label - Checkbox label
 * @param {boolean} props.checked - Whether the checkbox is checked
 * @param {Function} props.onChange - Change event handler
 * @param {boolean} props.disabled - Whether the checkbox is disabled
 * @param {string} props.className - Additional CSS class
 */
const CustomCheckbox = ({ 
  id, 
  label, 
  checked = false, 
  onChange, 
  disabled = false, 
  className = '' 
}) => {
  return (
    <label 
      className={`custom-checkbox ${className} ${disabled ? 'disabled' : ''}`}
      htmlFor={id}
    >
      <input
        type="checkbox"
        id={id}
        checked={checked}
        onChange={onChange}
        disabled={disabled}
      />
      <span className="checkmark"></span>
      <span className="checkbox-label">{label}</span>
    </label>
  );
};

export default CustomCheckbox;
