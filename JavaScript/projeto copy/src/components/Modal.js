import React, { useEffect, useRef } from 'react';

/**
 * Componente de modal
 * @param {Object} props - Propriedades do componente
 * @param {boolean} props.isOpen - Se o modal está aberto
 * @param {Function} props.onClose - Função para fechar o modal
 * @param {React.ReactNode} props.children - Conteúdo do modal
 * @param {string} props.title - Título do modal
 * @param {string} props.className - Classes CSS adicionais
 * @param {boolean} props.closeOnEsc - Se o modal fecha ao pressionar ESC
 * @param {boolean} props.closeOnOutsideClick - Se o modal fecha ao clicar fora
 */
const Modal = ({
  isOpen,
  onClose,
  children,
  title = '',
  className = '',
  closeOnEsc = true,
  closeOnOutsideClick = true
}) => {
  const modalRef = useRef(null);
  
  // Fechar o modal ao pressionar ESC
  useEffect(() => {
    const handleKeyDown = (e) => {
      if (closeOnEsc && e.key === 'Escape') {
        onClose();
      }
    };
    
    if (isOpen) {
      document.addEventListener('keydown', handleKeyDown);
      // Impedir o scroll do body quando o modal estiver aberto
      document.body.style.overflow = 'hidden';
    }
    
    return () => {
      document.removeEventListener('keydown', handleKeyDown);
      // Restaurar o scroll do body quando o modal for fechado
      document.body.style.overflow = '';
    };
  }, [isOpen, onClose, closeOnEsc]);
  
  // Fechar o modal ao clicar fora
  const handleOutsideClick = (e) => {
    if (closeOnOutsideClick && modalRef.current && !modalRef.current.contains(e.target)) {
      onClose();
    }
  };
  
  if (!isOpen) return null;
  
  return (
    <div className="modal-overlay" onClick={handleOutsideClick}>
      <div className={`modal ${className}`} ref={modalRef}>
        <div className="modal-header">
          {title && <h2 className="modal-title">{title}</h2>}
          <button className="modal-close" onClick={onClose}>×</button>
        </div>
        <div className="modal-content">
          {children}
        </div>
      </div>
      
      <style jsx>{`
        .modal-overlay {
          position: fixed;
          top: 0;
          left: 0;
          right: 0;
          bottom: 0;
          background-color: rgba(0, 0, 0, 0.7);
          display: flex;
          align-items: center;
          justify-content: center;
          z-index: 1000;
          animation: fadeIn 0.3s ease;
        }
        
        .modal {
          background-color: #1a1a1a;
          border-radius: 8px;
          border: 1px solid var(--border-color);
          width: 90%;
          max-width: 500px;
          max-height: 90vh;
          overflow: auto;
          animation: slideIn 0.3s ease;
        }
        
        .modal-header {
          display: flex;
          align-items: center;
          justify-content: space-between;
          padding: 15px 20px;
          border-bottom: 1px solid var(--border-color);
        }
        
        .modal-title {
          margin: 0;
          font-size: 18px;
          color: var(--primary-color);
        }
        
        .modal-close {
          background: none;
          border: none;
          color: #aaa;
          font-size: 24px;
          cursor: pointer;
          padding: 0;
          line-height: 1;
        }
        
        .modal-close:hover {
          color: white;
        }
        
        .modal-content {
          padding: 20px;
        }
        
        @keyframes fadeIn {
          from { opacity: 0; }
          to { opacity: 1; }
        }
        
        @keyframes slideIn {
          from { transform: translateY(-20px); opacity: 0; }
          to { transform: translateY(0); opacity: 1; }
        }
      `}</style>
    </div>
  );
};

export default Modal;
