.dock-outer {
  position: fixed;
  bottom: 0;
  left: 0;
  right: 0;
  display: flex;
  justify-content: center;
  align-items: flex-end;
  width: 100%;
  padding: 0;
  z-index: 1000;
  pointer-events: none;
  overflow: visible;
  padding-top: 50px;
}

.dock-panel {
  display: flex;
  align-items: flex-end;
  justify-content: center;
  gap: 0;
  border-radius: 1rem;
  background-color: rgba(6, 6, 6, 0.8);
  backdrop-filter: blur(10px);
  border: 1px solid #222;
  padding: 0.5rem 0;
  width: auto;
  min-width: 200px;
  max-width: 90%;
  pointer-events: auto;
  margin-bottom: 0.5rem;
  box-sizing: border-box;
}

.dock-item {
  position: relative;
  display: inline-flex;
  align-items: center;
  justify-content: center;
  border-radius: 10px;
  background-color: #060606;
  border: 1px solid #222;
  box-shadow: 0 4px 6px -1px rgba(0, 0, 0, 0.1),
    0 2px 4px -1px rgba(0, 0, 0, 0.06);
  cursor: pointer;
  outline: none;
  transform-origin: bottom;
  margin: 0 0.25rem;
}

.dock-icon {
  display: flex;
  align-items: center;
  justify-content: center;
}

.dock-label {
  position: absolute;
  top: -2rem;
  left: 50%;
  transform: translateX(-50%);
  width: fit-content;
  white-space: pre;
  border-radius: 0.375rem;
  border: 1px solid #222;
  background-color: #060606;
  padding: 0.125rem 0.5rem;
  font-size: 0.75rem;
  color: #fff;
  z-index: 1001;
}
