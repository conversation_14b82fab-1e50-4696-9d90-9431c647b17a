import React, { useState } from 'react';
import './App.css';
import './styles/index.css';
import './styles/calculator-back-buttons.css';
import CalculatorSelector from './calculators/calculatorSelector/CalculatorSelector.js';
import Chemistry from './calculators/chemistry/chemistry.js'; // Usando o chemistry.js original
import Physics from './calculators/physics/physics.js'; // Usando o physics.js original
import Math from './calculators/math/math.js'; // Usando o math.js original
import { BrowserRouter as Router, Route, Routes } from 'react-router-dom';

function App() {
  const [selectedCalculator, setSelectedCalculator] = useState(null);

  // Definir variáveis CSS globais
  React.useEffect(() => {
    // Definir cores com base na calculadora selecionada
    let primaryColor = '#4CAF50'; // Verde (padrão)
    let borderColor = 'rgba(76, 175, 80, 0.5)'; // Verde com transparência (padrão)

    if (selectedCalculator === 'physics') {
      primaryColor = '#9C27B0'; // Roxo
      borderColor = 'rgba(156, 39, 176, 0.5)'; // Roxo com transparência
    } else if (selectedCalculator === 'math') {
      primaryColor = '#2196F3'; // Azul
      borderColor = 'rgba(33, 150, 243, 0.5)'; // Azul com transparência
    }

    document.documentElement.style.setProperty('--primary-color', primaryColor);
    document.documentElement.style.setProperty('--border-color', borderColor);
  }, [selectedCalculator]);

  const handleSelectCalculator = (calculatorId) => {
    setSelectedCalculator(calculatorId);
  };

  const handleBackToSelector = () => {
    setSelectedCalculator(null);
  };

  // Renderiza o conteúdo com base na calculadora selecionada
  const renderContent = () => {
    if (!selectedCalculator) {
      return <CalculatorSelector onSelectCalculator={handleSelectCalculator} />;
    }

    switch (selectedCalculator) {
      case 'chemistry':
        return (
          <div>
            <button
              className="back-button back-button-right back-button-chemistry"
              onClick={handleBackToSelector}
            >
              Back to Calculators
            </button>
            <Chemistry />
          </div>
        );
      case 'physics':
        return (
          <div>
            <button
              className="back-button back-button-left back-button-physics"
              onClick={handleBackToSelector}
            >
              Back to Calculators
            </button>
            <Physics />
          </div>
        );
      case 'math':
        return (
          <div>
            <button
              className="back-button back-button-left back-button-math"
              onClick={handleBackToSelector}
            >
              Back to Calculators
            </button>
            <Math />
          </div>
        );
      default:
        return <CalculatorSelector onSelectCalculator={handleSelectCalculator} />;
    }
  };

  return (
    <div className="App">
      {renderContent()}
    </div>
  );
}

export default App;
