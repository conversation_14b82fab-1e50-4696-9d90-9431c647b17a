import React, { useState } from 'react';
import '../../App.css';
import '../../styles/index.css';
import Squares from '../../Squares/Squares.jsx';
import SimpleCarousel from './components/carousel/SimpleCarousel';
import SimpleTabSwitcher from './components/tabs/SimpleTabSwitcher';
import './TabSwitcherDemo.css';

function TabSwitcherDemo() {
  const [showTabSwitcher, setShowTabSwitcher] = useState(true);
  
  // Slides de exemplo
  const slides = [
    {
      id: 'slide1',
      title: 'Slide 1',
      component: (
        <div className="demo-slide">
          <h3>Slide 1 Content</h3>
          <p>This is the content of slide 1.</p>
          <div className="demo-content">
            <p>The TabSwitcher component offers several advantages:</p>
            <ul>
              <li>Smoother animations and transitions</li>
              <li>Better spacing of navigation buttons</li>
              <li>Independent control of menu and content sizes</li>
              <li>More accessible structure</li>
            </ul>
          </div>
        </div>
      )
    },
    {
      id: 'slide2',
      title: 'Slide 2',
      component: (
        <div className="demo-slide">
          <h3>Slide 2 Content</h3>
          <p>This is the content of slide 2.</p>
          <div className="demo-content">
            <p>The TabSwitcher maintains the same 3D rotation effect as the original carousel:</p>
            <ul>
              <li>Side tabs appear rotated in 3D space</li>
              <li>Smooth transitions between tabs</li>
              <li>Infinite navigation (loops around)</li>
              <li>Direct tab selection</li>
            </ul>
          </div>
        </div>
      )
    },
    {
      id: 'slide3',
      title: 'Slide 3',
      component: (
        <div className="demo-slide">
          <h3>Slide 3 Content</h3>
          <p>This is the content of slide 3.</p>
          <div className="demo-content">
            <p>The TabSwitcher allows for independent styling:</p>
            <ul>
              <li>Menu height can be adjusted separately</li>
              <li>Content height can be adjusted separately</li>
              <li>Custom styles can be applied to each section</li>
              <li>Custom classes can be added for more styling options</li>
            </ul>
          </div>
        </div>
      )
    }
  ];

  // Estilos personalizados para o TabSwitcher
  const tabSwitcherStyles = {
    menuStyle: { 
      height: '60px', 
      marginBottom: '15px',
      backgroundColor: 'rgba(0, 0, 0, 0.2)'
    },
    contentStyle: { 
      minHeight: '300px', 
      padding: '15px',
      backgroundColor: 'rgba(0, 0, 0, 0.1)',
      borderRadius: '8px'
    }
  };

  return (
    <div className="App">
      <div className="main-content-wrapper">
        <Squares
          speed={0.3}
          squareSize={30}
          direction="diagonal"
          borderColor="#222"
          hoverFillColor="#222"
          className="background-squares"
        />
        <div className="content-container">
          <h1>TabSwitcher vs Carousel Demo</h1>
          
          <div className="toggle-container">
            <button 
              className={`toggle-button ${showTabSwitcher ? 'active' : ''}`}
              onClick={() => setShowTabSwitcher(true)}
            >
              Show TabSwitcher
            </button>
            <button 
              className={`toggle-button ${!showTabSwitcher ? 'active' : ''}`}
              onClick={() => setShowTabSwitcher(false)}
            >
              Show Carousel
            </button>
          </div>
          
          <div className="demo-section">
            {showTabSwitcher ? (
              <div className="demo-container">
                <h2>TabSwitcher</h2>
                <SimpleTabSwitcher 
                  slides={slides} 
                  menuStyle={tabSwitcherStyles.menuStyle}
                  contentStyle={tabSwitcherStyles.contentStyle}
                />
              </div>
            ) : (
              <div className="demo-container">
                <h2>Carousel</h2>
                <SimpleCarousel slides={slides} />
              </div>
            )}
          </div>
          
          <div className="explanation">
            <h2>Diferenças entre TabSwitcher e Carousel</h2>
            <div className="comparison-table">
              <table>
                <thead>
                  <tr>
                    <th>Característica</th>
                    <th>TabSwitcher</th>
                    <th>Carousel</th>
                  </tr>
                </thead>
                <tbody>
                  <tr>
                    <td>Animação 3D</td>
                    <td>✓</td>
                    <td>✓</td>
                  </tr>
                  <tr>
                    <td>Transições suaves</td>
                    <td>✓ (mais fluidas)</td>
                    <td>✓</td>
                  </tr>
                  <tr>
                    <td>Espaçamento de botões</td>
                    <td>✓ (ajustável)</td>
                    <td>✓ (fixo)</td>
                  </tr>
                  <tr>
                    <td>Tamanho do menu ajustável</td>
                    <td>✓</td>
                    <td>✗</td>
                  </tr>
                  <tr>
                    <td>Tamanho do conteúdo ajustável</td>
                    <td>✓</td>
                    <td>✗</td>
                  </tr>
                  <tr>
                    <td>Navegação infinita</td>
                    <td>✓</td>
                    <td>✓</td>
                  </tr>
                  <tr>
                    <td>Seleção direta</td>
                    <td>✓</td>
                    <td>✓</td>
                  </tr>
                  <tr>
                    <td>Navegação por teclado</td>
                    <td>✓</td>
                    <td>✗</td>
                  </tr>
                </tbody>
              </table>
            </div>
          </div>
        </div>
      </div>
    </div>
  );
}

export default TabSwitcherDemo;
