.molecule-viewer-advanced {
  width: 100%;
  height: 100%;
  padding: 20px;
  background: rgba(0, 0, 0, 0.1);
  border-radius: 8px;
  display: flex;
  flex-direction: column;
  gap: 15px;
}

.viewer-controls {
  display: flex;
  flex-wrap: wrap;
  gap: 15px;
  align-items: center;
  padding: 15px;
  background: rgba(0, 0, 0, 0.3);
  border-radius: 6px;
  border: 1px solid rgba(255, 255, 255, 0.1);
}

.control-group {
  display: flex;
  flex-direction: column;
  gap: 5px;
}

.control-group label {
  font-size: 12px;
  color: #4CAF50;
  font-weight: 600;
}

.style-select,
.viewer-select {
  padding: 8px 12px;
  background: rgba(0, 0, 0, 0.4);
  border: 1px solid rgba(255, 255, 255, 0.2);
  border-radius: 4px;
  color: white;
  font-size: 14px;
  min-width: 120px;
}

.style-select:focus,
.viewer-select:focus {
  outline: none;
  border-color: #4CAF50;
}

.control-btn {
  padding: 8px 16px;
  background: linear-gradient(45deg, #4CAF50, #2196F3);
  border: none;
  border-radius: 4px;
  color: white;
  cursor: pointer;
  font-size: 14px;
  font-weight: 600;
  transition: all 0.3s ease;
}

.control-btn:hover {
  transform: translateY(-1px);
  box-shadow: 0 4px 12px rgba(76, 175, 80, 0.3);
}

.viewer-container {
  flex: 1;
  min-height: 400px;
  background: #000000;
  border-radius: 6px;
  border: 1px solid rgba(255, 255, 255, 0.2);
  position: relative;
  overflow: hidden;
}

.loading-state {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  height: 400px;
  color: white;
  gap: 15px;
}

.spinner {
  width: 50px;
  height: 50px;
  border: 4px solid rgba(255, 255, 255, 0.3);
  border-top: 4px solid #4CAF50;
  border-radius: 50%;
  animation: spin 1s linear infinite;
}

@keyframes spin {
  0% { transform: rotate(0deg); }
  100% { transform: rotate(360deg); }
}

.loading-state p {
  margin: 0;
  font-size: 16px;
  opacity: 0.8;
}

.error-state {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  height: 400px;
  color: #ff6b6b;
  text-align: center;
  padding: 20px;
  gap: 10px;
}

.error-state p {
  margin: 0;
  font-size: 16px;
}

.error-hint {
  color: rgba(255, 255, 255, 0.7) !important;
  font-size: 14px !important;
  font-style: italic;
}

.viewer-info {
  padding: 15px;
  background: rgba(0, 0, 0, 0.2);
  border-radius: 6px;
  border: 1px solid rgba(255, 255, 255, 0.1);
  color: white;
  text-align: center;
}

.viewer-info p {
  margin: 5px 0;
  font-size: 14px;
}

.viewer-info p:first-child {
  opacity: 0.7;
  font-size: 12px;
  font-style: italic;
}

.viewer-info strong {
  color: #4CAF50;
}

/* Responsividade */
@media (max-width: 768px) {
  .molecule-viewer-advanced {
    padding: 15px;
  }
  
  .viewer-controls {
    flex-direction: column;
    align-items: stretch;
    gap: 10px;
  }
  
  .control-group {
    flex-direction: row;
    align-items: center;
    justify-content: space-between;
  }
  
  .style-select,
  .viewer-select {
    min-width: 100px;
    flex: 1;
  }
  
  .viewer-container {
    min-height: 300px;
  }
  
  .loading-state,
  .error-state {
    height: 300px;
  }
}

/* Efeitos visuais */
.viewer-container::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: linear-gradient(45deg, 
    rgba(76, 175, 80, 0.1) 0%, 
    rgba(33, 150, 243, 0.1) 50%, 
    rgba(156, 39, 176, 0.1) 100%);
  opacity: 0;
  transition: opacity 0.3s ease;
  pointer-events: none;
  z-index: 0;
}

.viewer-container:hover::before {
  opacity: 1;
}

.viewer-container canvas {
  position: relative;
  z-index: 1;
  border-radius: 6px;
}

/* Animações */
.molecule-viewer-advanced > * {
  animation: fadeInUp 0.5s ease;
}

@keyframes fadeInUp {
  from {
    opacity: 0;
    transform: translateY(20px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

/* Estilo para selects customizados */
.style-select option,
.viewer-select option {
  background: #1a1a2e;
  color: white;
}

/* Hover effects */
.control-group:hover label {
  color: #66BB6A;
}

.style-select:hover,
.viewer-select:hover {
  border-color: rgba(76, 175, 80, 0.5);
}

/* Focus states */
.style-select:focus,
.viewer-select:focus {
  box-shadow: 0 0 10px rgba(76, 175, 80, 0.3);
}

.control-btn:focus {
  outline: none;
  box-shadow: 0 0 10px rgba(76, 175, 80, 0.5);
}

/* Loading animation enhancement */
.loading-state {
  background: radial-gradient(circle, rgba(76, 175, 80, 0.1) 0%, transparent 70%);
}

.spinner {
  box-shadow: 0 0 20px rgba(76, 175, 80, 0.3);
}

/* Error state enhancement */
.error-state {
  background: radial-gradient(circle, rgba(255, 107, 107, 0.1) 0%, transparent 70%);
}

/* Info panel enhancement */
.viewer-info {
  backdrop-filter: blur(10px);
  transition: all 0.3s ease;
}

.viewer-info:hover {
  background: rgba(0, 0, 0, 0.3);
  border-color: rgba(76, 175, 80, 0.3);
}
