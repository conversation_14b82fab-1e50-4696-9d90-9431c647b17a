import React, { useState } from 'react';
import './OrganicReactions.css';

const OrganicReactions = ({ currentMolecule, onMoleculeSelect }) => {
  const [selectedReaction, setSelectedReaction] = useState(null);

  // Reações orgânicas comuns
  const reactionTypes = [
    {
      id: 'combustion',
      name: 'Combustion',
      description: 'Complete oxidation of organic compounds',
      example: 'CH₄ + 2O₂ → CO₂ + 2H₂O',
      conditions: 'High temperature, oxygen'
    },
    {
      id: 'substitution',
      name: 'Substitution',
      description: 'Replacement of one atom/group with another',
      example: 'CH₄ + Cl₂ → CH₃Cl + HCl',
      conditions: 'UV light or heat'
    },
    {
      id: 'addition',
      name: 'Addition',
      description: 'Addition across double/triple bonds',
      example: 'C₂H₄ + H₂ → C₂H₆',
      conditions: 'Catalyst (Ni, Pt, Pd)'
    },
    {
      id: 'elimination',
      name: 'Elimination',
      description: 'Removal of atoms to form double bonds',
      example: 'C₂H₅OH → C₂H₄ + H₂O',
      conditions: 'Heat, acid catalyst'
    },
    {
      id: 'oxidation',
      name: 'Oxidation',
      description: 'Increase in oxidation state',
      example: 'C₂H₅OH → CH₃CHO → CH₃COOH',
      conditions: 'Oxidizing agent (KMnO₄, K₂Cr₂O₇)'
    },
    {
      id: 'reduction',
      name: 'Reduction',
      description: 'Decrease in oxidation state',
      example: 'CH₃CHO → C₂H₅OH',
      conditions: 'Reducing agent (LiAlH₄, NaBH₄)'
    }
  ];

  // Mecanismos de reação específicos
  const mechanisms = {
    substitution: [
      {
        name: 'SN1 Mechanism',
        steps: [
          'Formation of carbocation (slow)',
          'Nucleophilic attack (fast)'
        ],
        characteristics: ['Unimolecular', 'Racemization', 'Tertiary > Secondary']
      },
      {
        name: 'SN2 Mechanism',
        steps: [
          'Concerted nucleophilic attack',
          'Simultaneous bond breaking/forming'
        ],
        characteristics: ['Bimolecular', 'Inversion', 'Primary > Secondary']
      }
    ],
    addition: [
      {
        name: 'Markovnikov Addition',
        steps: [
          'Protonation of alkene',
          'Nucleophilic attack on carbocation'
        ],
        characteristics: ['H adds to less substituted carbon', 'More stable carbocation']
      },
      {
        name: 'Anti-Markovnikov Addition',
        steps: [
          'Radical initiation',
          'Radical addition'
        ],
        characteristics: ['H adds to more substituted carbon', 'Peroxide effect']
      }
    ]
  };

  const selectReaction = (reaction) => {
    setSelectedReaction(reaction);
  };

  const predictProducts = (reactant, reactionType) => {
    // Predição simples de produtos baseada no tipo de reação
    const predictions = {
      combustion: {
        products: ['CO₂', 'H₂O'],
        description: 'Complete combustion produces carbon dioxide and water'
      },
      substitution: {
        products: ['Substituted product', 'Leaving group'],
        description: 'One group replaces another'
      },
      addition: {
        products: ['Saturated product'],
        description: 'Addition across multiple bond'
      },
      elimination: {
        products: ['Alkene', 'Small molecule (H₂O, HX)'],
        description: 'Formation of double bond'
      },
      oxidation: {
        products: ['Oxidized product'],
        description: 'Increase in oxidation state'
      },
      reduction: {
        products: ['Reduced product'],
        description: 'Decrease in oxidation state'
      }
    };

    return predictions[reactionType] || { products: ['Unknown'], description: 'Reaction not recognized' };
  };

  return (
    <div className="organic-reactions">
      <div className="reactions-header">
        <h3>Organic Reactions</h3>
        <p>Explore common organic reaction types and mechanisms</p>
      </div>

      {currentMolecule && (
        <div className="current-molecule-info">
          <h4>Current Molecule: {currentMolecule.iupac || currentMolecule.formula}</h4>
          <p>Select a reaction type to see possible products</p>
        </div>
      )}

      <div className="reaction-types-grid">
        {reactionTypes.map(reaction => (
          <div 
            key={reaction.id}
            className={`reaction-card ${selectedReaction?.id === reaction.id ? 'selected' : ''}`}
            onClick={() => selectReaction(reaction)}
          >
            <div className="reaction-name">{reaction.name}</div>
            <div className="reaction-description">{reaction.description}</div>
            <div className="reaction-example">{reaction.example}</div>
            <div className="reaction-conditions">
              <strong>Conditions:</strong> {reaction.conditions}
            </div>
          </div>
        ))}
      </div>

      {selectedReaction && (
        <div className="reaction-details">
          <h4>{selectedReaction.name} Reaction</h4>
          
          <div className="reaction-info">
            <div className="info-section">
              <h5>General Information</h5>
              <p><strong>Description:</strong> {selectedReaction.description}</p>
              <p><strong>Conditions:</strong> {selectedReaction.conditions}</p>
              <p><strong>Example:</strong> {selectedReaction.example}</p>
            </div>

            {mechanisms[selectedReaction.id] && (
              <div className="mechanisms-section">
                <h5>Reaction Mechanisms</h5>
                {mechanisms[selectedReaction.id].map((mechanism, index) => (
                  <div key={index} className="mechanism-card">
                    <h6>{mechanism.name}</h6>
                    <div className="mechanism-steps">
                      <strong>Steps:</strong>
                      <ol>
                        {mechanism.steps.map((step, stepIndex) => (
                          <li key={stepIndex}>{step}</li>
                        ))}
                      </ol>
                    </div>
                    <div className="mechanism-characteristics">
                      <strong>Characteristics:</strong>
                      <ul>
                        {mechanism.characteristics.map((char, charIndex) => (
                          <li key={charIndex}>{char}</li>
                        ))}
                      </ul>
                    </div>
                  </div>
                ))}
              </div>
            )}

            {currentMolecule && (
              <div className="prediction-section">
                <h5>Predicted Products</h5>
                {(() => {
                  const prediction = predictProducts(currentMolecule, selectedReaction.id);
                  return (
                    <div className="prediction-result">
                      <p><strong>Products:</strong> {prediction.products.join(', ')}</p>
                      <p><strong>Explanation:</strong> {prediction.description}</p>
                    </div>
                  );
                })()}
              </div>
            )}
          </div>
        </div>
      )}

      <div className="reaction-tips">
        <h4>💡 Reaction Tips</h4>
        <div className="tips-grid">
          <div className="tip-card">
            <h5>Substitution Reactions</h5>
            <p>Primary carbons favor SN2, tertiary favor SN1</p>
          </div>
          <div className="tip-card">
            <h5>Addition Reactions</h5>
            <p>Follow Markovnikov's rule unless peroxides are present</p>
          </div>
          <div className="tip-card">
            <h5>Elimination Reactions</h5>
            <p>Zaitsev's rule: more substituted alkene is favored</p>
          </div>
          <div className="tip-card">
            <h5>Oxidation States</h5>
            <p>Alcohol → Aldehyde → Carboxylic Acid</p>
          </div>
        </div>
      </div>
    </div>
  );
};

export default OrganicReactions;
