.toggle-container {
  display: flex;
  justify-content: center;
  margin: 20px 0;
  gap: 15px;
}

.toggle-button {
  background-color: rgba(0, 0, 0, 0.2);
  border: 2px solid var(--border-color);
  border-radius: 8px;
  color: #aaa;
  font-size: 14px;
  font-weight: 600;
  cursor: pointer;
  padding: 8px 15px;
  transition: all 0.3s ease;
}

.toggle-button.active {
  background-color: rgba(76, 175, 80, 0.1);
  border-color: var(--primary-color);
  color: var(--primary-color);
  box-shadow: 0 0 10px rgba(76, 175, 80, 0.3);
}

.toggle-button:hover {
  color: white;
}

.test-section {
  margin-bottom: 30px;
  background-color: rgba(0, 0, 0, 0.1);
  border-radius: 10px;
  padding: 20px;
  box-shadow: 0 0 10px rgba(0, 0, 0, 0.2);
}

.test-section h2 {
  text-align: center;
  margin-bottom: 15px;
  color: var(--primary-color);
}

.test-container {
  border: 1px solid var(--border-color);
  border-radius: 8px;
  padding: 15px;
  background-color: rgba(0, 0, 0, 0.1);
}

.test-slide {
  padding: 20px;
  min-height: 150px;
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  text-align: center;
}

.test-slide h3 {
  color: var(--primary-color);
  margin-bottom: 10px;
}

.explanation {
  margin-top: 30px;
  background-color: rgba(0, 0, 0, 0.1);
  border-radius: 10px;
  padding: 20px;
  box-shadow: 0 0 10px rgba(0, 0, 0, 0.2);
}

.explanation h2 {
  text-align: center;
  margin-bottom: 15px;
  color: var(--primary-color);
}

.explanation ul {
  margin-left: 20px;
}

.explanation li {
  margin-bottom: 8px;
}
