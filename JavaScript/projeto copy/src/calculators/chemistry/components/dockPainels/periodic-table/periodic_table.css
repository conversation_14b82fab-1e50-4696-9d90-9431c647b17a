.element-box {
  width: 48px;
  height: 48px;
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  border-radius: 8px;
  border: 1.5px solid #444;
  font-weight: bold;
  font-size: 1.1em;
  margin: 2px;
  background: #333;
  color: #fff;
  cursor: pointer;
  transition: transform 0.15s, box-shadow 0.15s;
  position: relative;
}
.element-box:hover {
  transform: scale(1.18);
  z-index: 2;
}
.element-noble-gas:hover { box-shadow: 0 0 12px #4fc3f7;}
.element-nonmetal:hover { box-shadow: 0 0 12px #a5d6a7;}
.element-alkali-metal:hover { box-shadow: 0 0 12px #ffd54f;}
.element-alkaline-earth-metal:hover { box-shadow: 0 0 12px #ffcc80;}
.element-metalloid:hover { box-shadow: 0 0 12px #b39ddb;}
.element-transition-metal:hover { box-shadow: 0 0 12px #90caf9;}
.element-actinide:hover { box-shadow: 0 0 12px #cf63e2;}
.element-lanthanide:hover { box-shadow: 0 0 12px #e1bee7;}
.element-metal:hover { box-shadow: 0 0 12px #f48fb1;}

.element-noble-gas { background: #4fc3f7; color: #222; }
.element-nonmetal { background: #a5d6a7; color: #222; }
.element-alkali-metal { background: #ffd54f; color: #222; }
.element-alkaline-earth-metal { background: #ffcc80; color: #222; }
.element-metalloid { background: #b39ddb; color: #222; }
.element-transition-metal { background: #90caf9; color: #222; }
.element-actinide { background: #cf63e2; color: #222; }
.element-lanthanide { background: #e1bee7; color: #222; }
.element-metal { background: #f48fb1; color: #222; }

.element-symbol {
  font-size: 1.3em;
  font-weight: bold;
}
.element-number {
  position: absolute;
  top: 3px;
  left: 6px;
  font-size: 0.7em;
  opacity: 0.7;
}

/* Modal de informações do elemento */
.element-info-modal {
  position: fixed;
  top: 0;
  left: 0;
  width: 100vw;
  height: 100vh;
  background: rgba(0, 0, 0, 0.8);
  display: flex;
  align-items: center;
  justify-content: center;
  z-index: 10001;
}

.element-info-content {
  background: #2a2a2a;
  border-radius: 12px;
  padding: 24px;
  width: 650px;
  max-width: 90vw;
  max-height: 85vh;
  overflow-y: auto;
  color: white;
  position: relative;
  box-shadow: 0 8px 32px rgba(0, 0, 0, 0.5);
  border: 1px solid rgba(76, 175, 80, 0.3);
}

.element-info-content .close-button {
  position: absolute;
  top: 12px;
  right: 12px;
  background: rgba(255, 0, 0, 0.2);
  color: white;
  border: 1px solid rgba(255, 0, 0, 0.5);
  border-radius: 6px;
  padding: 6px 12px;
  cursor: pointer;
  font-weight: bold;
  transition: background-color 0.2s;
}

.element-info-content .close-button:hover {
  background: rgba(255, 0, 0, 0.4);
}

button .close-button:focus {
  box-shadow: 0 0 10px 5px rgba(255, 135, 98, 0.5) !important;
}

.element-info-content h2 {
  margin: 0 0 20px 0;
  color: #4fc3f7;
  font-size: 1.8em;
  border-bottom: 2px solid rgba(76, 175, 80, 0.3);
  padding-bottom: 10px;
}

.element-info-content h3 {
  margin: 20px 0 10px 0;
  color: #a5d6a7;
  font-size: 1.3em;
}

.element-info-content h4 {
  margin: 10px 0 8px 0;
  color: #90caf9;
  font-size: 1.1em;
}

.basic-info p {
  margin: 8px 0;
  line-height: 1.4;
}

.isotopes-section {
  border-left: 4px solid #2196f3;
  padding-left: 15px;
}

.isotope-buttons {
  width: 100%;
  max-width: 100%;
  overflow-x: auto;
  box-sizing: border-box;
}

.isotope-buttons button {
  white-space: nowrap;
  min-width: fit-content;
  flex-shrink: 0;
}

.isotope-display-container {
  width: 100% !important;
  box-sizing: border-box;
}

.isotopes-section {
  width: 100%;
  box-sizing: border-box;
}

.isotope-buttons button:hover {
  transform: translateY(-1px);
  transition: all 0.2s;
  box-shadow: 0 4px 8px rgba(0, 0, 0, 0.3);
}

.isotope-display-container {
  transition: all 0.3s ease;
}

.isotope-info {
  transition: all 0.3s ease;
}

.isotopes-table {
  border: 1px solid rgba(255, 255, 255, 0.1);
}

.isotopes-table th {
  background: rgba(76, 175, 80, 0.2);
  font-weight: bold;
  color: #a5d6a7;
}

.isotopes-table tr:hover {
  background: rgba(255, 255, 255, 0.05);
}

/* Scrollbar personalizada para o modal */
.element-info-content::-webkit-scrollbar {
  width: 8px;
}

.element-info-content::-webkit-scrollbar-track {
  background: rgba(255, 255, 255, 0.1);
  border-radius: 4px;
}

.element-info-content::-webkit-scrollbar-thumb {
  background: rgba(76, 175, 80, 0.5);
  border-radius: 4px;
}

.element-info-content::-webkit-scrollbar-thumb:hover {
  background: rgba(76, 175, 80, 0.7);
}

.isotopes-table::-webkit-scrollbar {
  width: 6px;
}

.isotopes-table::-webkit-scrollbar-track {
  background: rgba(255, 255, 255, 0.05);
  border-radius: 3px;
}

.isotopes-table::-webkit-scrollbar-thumb {
  background: rgba(33, 150, 243, 0.5);
  border-radius: 3px;
}

.isotopes-table::-webkit-scrollbar-thumb:hover {
  background: rgba(33, 150, 243, 0.7);
}