import React, { useState, useRef } from 'react';
import { motion, useMotionValue, useSpring } from 'framer-motion';
import './periodic_table.css';
import { PeriodicTableData } from '../../../constants/chemistryConstants';

const molarMass = PeriodicTableData.molarMass;
const elementsName = PeriodicTableData.elementsName;
const elementsNumber = PeriodicTableData.elementsNumber;
const elementsCategory = PeriodicTableData.elementsCategory;
const electronConfiguration = PeriodicTableData.electronConfiguration;
const simplifiedElectronConfiguration = PeriodicTableData.simplifiedElectronConfiguration;
const ionicCharge = PeriodicTableData.possibleIons;
const isotopes = PeriodicTableData.isotopes;

function getElementInfo(symbol) {
  if (!symbol) return null;
  return {
    symbol,
    name: elementsName[symbol] || '',
    number: elementsNumber[symbol] || '',
    category: elementsCategory[symbol] || '',
    mass: molarMass[symbol] || '',
    electronConfiguration: electronConfiguration[symbol] || '',
  };
}


function getElementClass(symbol) {
  const info = getElementInfo(symbol);
  if (!info || !info.category) return 'element-box';
  // Normaliza para classe CSS (ex: Noble gas -> noble-gas)
  return `element-box element-${info.category.toLowerCase().replace(/ /g, '-')}`;
}

function getElementColor(symbol) {
  const info = getElementInfo(symbol);
  if (!info || !info.category) return '#4fc3f7';

  const categoryColors = {
    'noble gas': '#4fc3f7',
    'nonmetal': '#a5d6a7',
    'alkali metal': '#ffd54f',
    'alkaline earth metal': '#ffcc80',
    'metalloid': '#b39ddb',
    'transition metal': '#90caf9',
    'actinide': '#cf63e2',
    'lanthanide': '#e1bee7',
    'metal': '#f48fb1'
  };

  return categoryColors[info.category.toLowerCase()] || '#4fc3f7';
}

// Componente separado para o quadrado do elemento
function ElementBox({ symbol, onClick }) {
  const springValues = {
  damping: 20,
  stiffness: 80,
  mass: 2,
  };
  const ref = useRef(null);

  const rotateAmplitude = 12;
  const info = getElementInfo(symbol);
  // Valores iniciais para motion values
  const rotateX = useSpring(useMotionValue(0), springValues);
  const rotateY = useSpring(useMotionValue(0), springValues);
  const scale = useSpring(1, springValues);
  // Simplificando para manter apenas os valores necessários

  const handleMouseMove = (e) => {
    if (!ref.current) return;
    const rect = ref.current.getBoundingClientRect();
    const offsetX = e.clientX - rect.left - rect.width / 2;
    const offsetY = e.clientY - rect.top - rect.height / 2;
    const rotationX = (offsetY / (rect.height / 2)) * -rotateAmplitude;
    const rotationY = (offsetX / (rect.width / 2)) * rotateAmplitude;
    rotateX.set(rotationX);
    rotateY.set(rotationY);
  };
  const handleMouseEnter = () => {
    scale.set(1.05);
  };
  const handleMouseLeave = () => {
    scale.set(1);
    rotateX.set(0);
    rotateY.set(0);
  };
  return (
    <motion.div
      ref={ref}
      className={getElementClass(symbol)}
      onClick={onClick}
      title={info?.name}
      style={{
        rotateX,
        rotateY,
        scale,
        perspective: 600,
        willChange: 'transform',
      }}
      onMouseMove={handleMouseMove}
      onMouseEnter={handleMouseEnter}
      onMouseLeave={handleMouseLeave}
    >
      <span className="element-number">{info?.number}</span>
      <span className="element-symbol">{symbol}</span>
    </motion.div>
  );
}

const PeriodicTable = ({ onClose }) => {
  const [selectedElement, setSelectedElement] = useState(null);
  const [selectedIsotope, setSelectedIsotope] = useState(null);
  const renderElementTd = (symbol) => (
    <td key={symbol}>
      {symbol ? (
        <ElementBox
          symbol={symbol}
          onClick={() => {
            setSelectedElement(symbol);
            setSelectedIsotope(null);
          }}
        />
      ) : null}
    </td>
  );
  return (
    <div className="periodic-table-modal" style={{
      position: 'fixed',
      top: 0,
      left: 0,
      width: '100vw',
      height: '100vh',
      background: 'rgba(0,0,0,0.7)',
      zIndex: 10000,
      display: 'flex',
      alignItems: 'center',
      justifyContent: 'center',
    }}>
      <div style={{
        background: '#222',
        borderRadius: 12,
        padding: 24,
        position: 'relative',
        boxShadow: '0 4px 32px #000a',
        maxHeight: '90vh',
        overflow: 'auto',
      }}>
        <button
          onClick={onClose}
          style={{
            position: 'absolute',
            top: 10,
            right: 10,
            background: 'rgba(76, 175, 80, 0.3)',
            color: 'white',
            border: 'none',
            borderRadius: 6,
            padding: '6px 14px',
            fontWeight: 'bold',
            cursor: 'pointer',
            fontSize: 16,
            zIndex: 2,
          }}
        >X</button>
        <p></p>
        <div className="periodic-table">
          <table>
        <thead>
          <tr>
            <th></th>
            <th>1</th>
            <th>2</th>
            <th>3</th>
            <th>4</th>
            <th>5</th>
            <th>6</th>
            <th>7</th>
            <th>8</th>
            <th>9</th>
            <th>10</th>
            <th>11</th>
            <th>12</th>
            <th>13</th>
            <th>14</th>
            <th>15</th>
            <th>16</th>
            <th>17</th>
            <th>18</th>
          </tr>
        </thead>
        <tbody>
          <tr>
            <td>1</td>
            {renderElementTd('H')}
            {Array(16).fill(null).map((_, i) => <td key={i}></td>)}
            {renderElementTd('He')}
          </tr>
          <tr>
            <td>2</td>
            {renderElementTd('Li')}
            {renderElementTd('Be')}
            {Array(10).fill(null).map((_, i) => <td key={i}></td>)}
            {renderElementTd('B')}
            {renderElementTd('C')}
            {renderElementTd('N')}
            {renderElementTd('O')}
            {renderElementTd('F')}
            {renderElementTd('Ne')}
          </tr>
          <tr>
            <td>3</td>
            {renderElementTd('Na')}
            {renderElementTd('Mg')}
            {Array(10).fill(null).map((_, i) => <td key={i}></td>)}
            {renderElementTd('Al')}
            {renderElementTd('Si')}
            {renderElementTd('P')}
            {renderElementTd('S')}
            {renderElementTd('Cl')}
            {renderElementTd('Ar')}
          </tr>
          <tr>
            <td>4</td>
            {renderElementTd('K')}
            {renderElementTd('Ca')}
            {renderElementTd('Sc')}
            {renderElementTd('Ti')}
            {renderElementTd('V')}
            {renderElementTd('Cr')}
            {renderElementTd('Mn')}
            {renderElementTd('Fe')}
            {renderElementTd('Co')}
            {renderElementTd('Ni')}
            {renderElementTd('Cu')}
            {renderElementTd('Zn')}
            {renderElementTd('Ga')}
            {renderElementTd('Ge')}
            {renderElementTd('As')}
            {renderElementTd('Se')}
            {renderElementTd('Br')}
            {renderElementTd('Kr')}
          </tr>
          <tr>
            <td>5</td>
            {renderElementTd('Rb')}
            {renderElementTd('Sr')}
            {renderElementTd('Y')}
            {renderElementTd('Zr')}
            {renderElementTd('Nb')}
            {renderElementTd('Mo')}
            {renderElementTd('Tc')}
            {renderElementTd('Ru')}
            {renderElementTd('Rh')}
            {renderElementTd('Pd')}
            {renderElementTd('Ag')}
            {renderElementTd('Cd')}
            {renderElementTd('In')}
            {renderElementTd('Sn')}
            {renderElementTd('Sb')}
            {renderElementTd('Te')}
            {renderElementTd('I')}
            {renderElementTd('Xe')}
          </tr>
          <tr>
            <td>6</td>
            {renderElementTd('Cs')}
            {renderElementTd('Ba')}
            {renderElementTd('La')}
            {renderElementTd('Hf')}
            {renderElementTd('Ta')}
            {renderElementTd('W')}
            {renderElementTd('Re')}
            {renderElementTd('Os')}
            {renderElementTd('Ir')}
            {renderElementTd('Pt')}
            {renderElementTd('Au')}
            {renderElementTd('Hg')}
            {renderElementTd('Tl')}
            {renderElementTd('Pb')}
            {renderElementTd('Bi')}
            {renderElementTd('Po')}
            {renderElementTd('At')}
            {renderElementTd('Rn')}
          </tr>
          <tr>
            <td>7</td>
            {renderElementTd('Fr')}
            {renderElementTd('Ra')}
            {renderElementTd('Ac')}
            {renderElementTd('Rf')}
            {renderElementTd('Db')}
            {renderElementTd('Sg')}
            {renderElementTd('Bh')}
            {renderElementTd('Hs')}
            {renderElementTd('Mt')}
            {renderElementTd('Ds')}
            {renderElementTd('Rg')}
            {renderElementTd('Cn')}
            {renderElementTd('Nh')}
            {renderElementTd('Fl')}
            {renderElementTd('Mc')}
            {renderElementTd('Lv')}
            {renderElementTd('Ts')}
            {renderElementTd('Og')}
          </tr>
          <tr>
            {Array(4).fill(null).map((_, i) => <td key={i}></td>)}
            {renderElementTd('Ce')}
            {renderElementTd('Pr')}
            {renderElementTd('Nd')}
            {renderElementTd('Pm')}
            {renderElementTd('Sm')}
            {renderElementTd('Eu')}
            {renderElementTd('Gd')}
            {renderElementTd('Tb')}
            {renderElementTd('Dy')}
            {renderElementTd('Ho')}
            {renderElementTd('Er')}
            {renderElementTd('Tm')}
            {renderElementTd('Yb')}
            {renderElementTd('Lu')}
            {Array(3).fill(null).map((_, i) => <td key={i}></td>)}
          </tr>
          <tr>
            {Array(4).fill(null).map((_, i) => <td key={i}></td>)}
            {renderElementTd('Th')}
            {renderElementTd('Pa')}
            {renderElementTd('U')}
            {renderElementTd('Np')}
            {renderElementTd('Pu')}
            {renderElementTd('Am')}
            {renderElementTd('Cm')}
            {renderElementTd('Bk')}
            {renderElementTd('Cf')}
            {renderElementTd('Es')}
            {renderElementTd('Fm')}
            {renderElementTd('Md')}
            {renderElementTd('No')}
            {renderElementTd('Lr')}
            {Array(3).fill(null).map((_, i) => <td key={i}></td>)}
          </tr>
        </tbody>
      </table>
        </div>
        {selectedElement && (
          <div className="element-info-modal">
            <div className="element-info-content" style={{
              borderColor: getElementColor(selectedElement)
            }}>
              <button className="close-button" onClick={() => {
                setSelectedElement(null);
                setSelectedIsotope(null);
              }}>X</button>

              <h2 style={{
                color: getElementColor(selectedElement),
                borderBottomColor: getElementColor(selectedElement)
              }}>{getElementInfo(selectedElement).name} ({selectedElement})</h2>

              {/* Informações básicas do elemento */}
              <div className="basic-info">
                <p><strong>Atomic Number:</strong> {getElementInfo(selectedElement).number}</p>
                <p><strong>Atomic Mass:</strong> {getElementInfo(selectedElement).mass}</p>
                <p><strong>Category:</strong> {getElementInfo(selectedElement).category}</p>
                <p><strong>Electron Configuration:</strong> {getElementInfo(selectedElement).electronConfiguration}</p>
                <p><strong>Simplified Electron Configuration:</strong> {simplifiedElectronConfiguration[selectedElement] || ''}</p>
                <p><strong>Possible Ionic Charges:</strong> {ionicCharge[selectedElement] ? ionicCharge[selectedElement].join(', ') : 'N/A'}</p>
              </div>

              {/* Seção de isótopos */}
              {isotopes[selectedElement] && (
                <div className="isotopes-section" style={{
                  marginTop: '20px',
                  borderLeftColor: getElementColor(selectedElement)
                }}>
                  <h3 style={{ color: getElementColor(selectedElement) }}>Isotopes</h3>

                  {/* Botões para selecionar isótopos */}
                  <div className="isotope-buttons" style={{
                    display: 'flex',
                    flexWrap: 'wrap',
                    gap: '8px',
                    marginBottom: '15px',
                    justifyContent: 'flex-start',
                    alignItems: 'center',
                    overflow: 'visible',
                  }}>
                    <button
                      onClick={() => setSelectedIsotope(null)}
                      onMouseEnter={(e) => {
                        e.target.style.backgroundColor = `${getElementColor(selectedElement)}60`;
                        e.target.style.boxShadow = `0 4px 8px ${getElementColor(selectedElement)}40`;
                      }}
                      onMouseLeave={(e) => {
                        e.target.style.backgroundColor = !selectedIsotope ? `${getElementColor(selectedElement)}40` : 'rgba(255, 255, 255, 0.1)';
                        e.target.style.boxShadow = 'none';
                      }}
                      style={{
                        padding: '6px 12px',
                        backgroundColor: !selectedIsotope ? `${getElementColor(selectedElement)}40` : 'rgba(255, 255, 255, 0.1)',
                        color: 'white',
                        border: `1px solid ${getElementColor(selectedElement)}80`,
                        borderRadius: '4px',
                        cursor: 'pointer',
                        fontSize: '12px',
                        transition: 'all 0.2s'
                      }}
                    >
                      Average
                    </button>
                    {isotopes[selectedElement].map((isotope, index) => (
                      <button
                        key={index}
                        onClick={() => setSelectedIsotope(isotope)}
                        onMouseEnter={(e) => {
                          e.target.style.backgroundColor = `${getElementColor(selectedElement)}60`;
                          e.target.style.boxShadow = `0 4px 8px ${getElementColor(selectedElement)}40`;
                        }}
                        onMouseLeave={(e) => {
                          e.target.style.backgroundColor = selectedIsotope === isotope ? `${getElementColor(selectedElement)}40` : 'rgba(255, 255, 255, 0.1)';
                          e.target.style.boxShadow = 'none';
                        }}
                        style={{
                          padding: '6px 12px',
                          backgroundColor: selectedIsotope === isotope ? `${getElementColor(selectedElement)}40` : 'rgba(255, 255, 255, 0.1)',
                          color: 'white',
                          border: `1px solid ${getElementColor(selectedElement)}80`,
                          borderRadius: '4px',
                          cursor: 'pointer',
                          fontSize: '12px',
                          transition: 'all 0.2s'
                        }}
                      >
                        {isotope.name}
                      </button>
                    ))}
                  </div>

                  {/* Container com altura e largura fixas para evitar mudança de tamanho */}
                  <div className="isotope-display-container" style={{
                    width: '100%',
                    minHeight: '280px',
                    maxHeight: '350px',
                    backgroundColor: 'rgba(255, 255, 255, 0.05)',
                    borderRadius: '8px',
                    padding: '15px',
                    overflow: 'auto',
                    boxSizing: 'border-box'
                  }}>
                    {selectedIsotope ? (
                      <div className="isotope-info" style={{
                        width: '100%',
                        padding: '20px',
                        backgroundColor: `${getElementColor(selectedElement)}20`,
                        border: `1px solid ${getElementColor(selectedElement)}60`,
                        borderRadius: '8px',
                        minHeight: '200px',
                        display: 'flex',
                        flexDirection: 'column',
                        justifyContent: 'center',
                        margin: '10px 0',
                        boxSizing: 'border-box'
                      }}>
                        <h4 style={{ margin: '0 0 20px 0', textAlign: 'center', fontSize: '1.3em' }}>{selectedIsotope.name}</h4>
                        <div style={{ textAlign: 'center', fontSize: '1.1em' }}>
                          <p style={{ margin: '15px 0' }}><strong>Mass:</strong> {selectedIsotope.mass.toFixed(3)} u</p>
                          <p style={{ margin: '15px 0' }}><strong>Natural Abundance:</strong> {selectedIsotope.abundance}%</p>
                          {selectedIsotope.abundance === 0 && (
                            <p style={{ color: '#ff6b6b', marginTop: '20px', fontSize: '1em' }}><strong>Note:</strong> This is a radioactive isotope</p>
                          )}
                        </div>
                      </div>
                    ) : (
                      <div className="isotopes-table" style={{
                        width: '100%',
                        minHeight: '200px',
                        maxHeight: '300px',
                        overflowY: 'auto',
                        margin: '10px 0',
                        boxSizing: 'border-box'
                      }}>
                        <table style={{ width: '100%', borderCollapse: 'collapse' }}>
                          <thead>
                            <tr style={{ backgroundColor: `${getElementColor(selectedElement)}20` }}>
                              <th style={{
                                padding: '8px',
                                textAlign: 'left',
                                borderBottom: `2px solid ${getElementColor(selectedElement)}60`,
                                color: getElementColor(selectedElement)
                              }}>Isotope</th>
                              <th style={{
                                padding: '8px',
                                textAlign: 'left',
                                borderBottom: `2px solid ${getElementColor(selectedElement)}60`,
                                color: getElementColor(selectedElement)
                              }}>Mass (u)</th>
                              <th style={{
                                padding: '8px',
                                textAlign: 'left',
                                borderBottom: `2px solid ${getElementColor(selectedElement)}60`,
                                color: getElementColor(selectedElement)
                              }}>Abundance (%)</th>
                            </tr>
                          </thead>
                          <tbody>
                            {isotopes[selectedElement].map((isotope, index) => (
                              <tr key={index}>
                                <td style={{ padding: '6px 8px', borderBottom: '1px solid rgba(255, 255, 255, 0.1)' }}>
                                  {isotope.name}
                                </td>
                                <td style={{ padding: '6px 8px', borderBottom: '1px solid rgba(255, 255, 255, 0.1)' }}>
                                  {isotope.mass.toFixed(3)}
                                </td>
                                <td style={{
                                  padding: '6px 8px',
                                  borderBottom: '1px solid rgba(255, 255, 255, 0.1)',
                                  color: isotope.abundance === 0 ? '#ff6b6b' : 'white'
                                }}>
                                  {isotope.abundance === 0 ? 'Radioactive' : isotope.abundance.toFixed(3)}
                                </td>
                              </tr>
                            ))}
                          </tbody>
                        </table>
                      </div>
                    )}
                  </div>
                </div>
              )}
            </div>
          </div>
        )}
      </div>
    </div>
  );
};

export default PeriodicTable;
