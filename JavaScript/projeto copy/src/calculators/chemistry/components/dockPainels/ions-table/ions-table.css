/* Container principal das tabelas */
.ions-table-container {
  display: flex;
  gap: 30px;
  justify-content: space-between;
  flex-wrap: wrap;
}

/* Seções de cátions e ânions */
.ions-section {
  flex: 1;
  min-width: 400px;
  background: rgba(255, 255, 255, 0.05);
  border-radius: 12px;
  padding: 20px;
  border: 1px solid rgba(255, 255, 255, 0.1);
}

/* Títulos das seções */
.section-title {
  margin: 0 0 20px 0;
  font-size: 1.3em;
  font-weight: bold;
  text-align: center;
  padding: 10px;
  border-radius: 8px;
  color: white;
}

.cations-title {
  background: linear-gradient(135deg, #4fc3f7, #29b6f6);
  border: 1px solid rgba(79, 195, 247, 0.5);
}

.anions-title {
  background: linear-gradient(135deg, #a5d6a7, #66bb6a);
  border: 1px solid rgba(165, 214, 167, 0.5);
}

/* Layout de duas colunas */
.two-column-layout {
  display: flex;
  gap: 15px;
  justify-content: space-between;
  font-size: 15px;
}

/* Wrapper da tabela */
.table-wrapper {
  flex: 1;
  overflow: hidden;
  border-radius: 8px;
  border: 1px solid rgba(255, 255, 255, 0.2);
}

/* Estilos da tabela */
.ions-grid-table {
  width: 100%;
  border-collapse: collapse;
  background: rgba(255, 255, 255, 0.02);
  font-family: 'Arial', sans-serif;
  table-layout: fixed;
}

/* Cabeçalho da tabela */
.table-header {
  background: linear-gradient(135deg, rgba(76, 175, 80, 0.3), rgba(76, 175, 80, 0.2));
  color: #a5d6a7;
  padding: 12px 16px;
  text-align: left;
  font-weight: bold;
  font-size: 0.95em;
  border-bottom: 2px solid rgba(76, 175, 80, 0.5);
  border-right: 1px solid rgba(255, 255, 255, 0.1);
}

.table-header:first-child {
  width: 30%;
}

.table-header:last-child {
  width: 70%;
  border-right: none;
}

/* Linhas da tabela */
.table-row {
  transition: all 0.2s ease;
  border-bottom: 1px solid rgba(255, 255, 255, 0.1);
}

.table-row:hover {
  background: rgba(76, 175, 80, 0.1);
  transform: scale(1.01);
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.3);
}

.even-row {
  background: rgba(255, 255, 255, 0.02);
}

.odd-row {
  background: rgba(255, 255, 255, 0.05);
}

/* Células da tabela */
.ion-symbol-cell {
  padding: 10px 16px;
  font-weight: bold;
  font-size: 1.1em;
  color: #4fc3f7;
  text-align: center;
  border-right: 1px solid rgba(255, 255, 255, 0.1);
  width: 30%;
  font-family: 'Courier New', monospace;
  white-space: nowrap;
  overflow: hidden;
  text-overflow: ellipsis;
}

.ion-name-cell {
  padding: 10px 16px;
  color: #e0e0e0;
  font-size: 0.95em;
  line-height: 1.4;
  width: 70%;
  white-space: nowrap;
  overflow-x: auto;
  overflow-y: hidden;
  position: relative;
}

/* Scrollbar das células completamente invisível */
.ion-name-cell::-webkit-scrollbar {
  display: none;
}

.ion-name-cell {
  -ms-overflow-style: none;  /* IE e Edge */
  scrollbar-width: none;     /* Firefox */
}

/* Todas as scrollbars invisíveis */

/* Responsividade */
@media (max-width: 1200px) {
  .ions-table-container {
    flex-direction: column;
    gap: 20px;
  }

  .ions-section {
    min-width: auto;
  }
}

@media (max-width: 800px) {
  .two-column-layout {
    flex-direction: column;
    gap: 15px;
  }
}

@media (max-width: 600px) {
  .ions-section {
    padding: 15px;
  }

  .table-header,
  .ion-symbol-cell,
  .ion-name-cell {
    padding: 8px 12px;
    font-size: 0.9em;
  }

  .section-title {
    font-size: 1.1em;
  }

  .two-column-layout {
    gap: 10px;
  }
}