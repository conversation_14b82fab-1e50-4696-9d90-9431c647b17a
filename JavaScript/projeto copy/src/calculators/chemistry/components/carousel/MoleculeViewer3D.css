.molecule-viewer-container {
  width: 100%;
  height: 100%;
  display: flex;
  flex-direction: column;
  background: linear-gradient(135deg, #1a1a2e 0%, #16213e 50%, #0f3460 100%);
  border-radius: 8px;
  overflow: hidden;
  position: relative;
}

.viewer-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 15px 20px;
  background: rgba(0, 0, 0, 0.3);
  border-bottom: 1px solid rgba(255, 255, 255, 0.1);
}

.viewer-header h4 {
  margin: 0;
  color: #ffffff;
  font-size: 1.1em;
  font-weight: 600;
}

.viewer-controls {
  display: flex;
  gap: 8px;
}

.control-button {
  background: rgba(255, 255, 255, 0.1);
  border: 1px solid rgba(255, 255, 255, 0.2);
  border-radius: 6px;
  padding: 8px 12px;
  color: #ffffff;
  cursor: pointer;
  font-size: 16px;
  transition: all 0.3s ease;
  backdrop-filter: blur(10px);
}

.control-button:hover {
  background: rgba(255, 255, 255, 0.2);
  border-color: rgba(255, 255, 255, 0.3);
  transform: translateY(-1px);
}

.control-button:active {
  transform: translateY(0);
}

.molecule-viewer {
  flex: 1;
  background: #000000;
  border-radius: 4px;
  margin: 10px;
  border: 1px solid rgba(255, 255, 255, 0.1);
  position: relative;
  overflow: hidden;
}

.loading-indicator {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  height: 300px;
  color: #ffffff;
}

.spinner {
  width: 40px;
  height: 40px;
  border: 3px solid rgba(255, 255, 255, 0.3);
  border-top: 3px solid #4CAF50;
  border-radius: 50%;
  animation: spin 1s linear infinite;
  margin-bottom: 15px;
}

@keyframes spin {
  0% { transform: rotate(0deg); }
  100% { transform: rotate(360deg); }
}

.loading-indicator p {
  margin: 0;
  font-size: 14px;
  opacity: 0.8;
}

.error-message {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  height: 300px;
  color: #ff6b6b;
  text-align: center;
  padding: 20px;
}

.error-message p {
  margin: 5px 0;
  font-size: 14px;
}

.error-details {
  color: #ffffff !important;
  opacity: 0.7;
  font-size: 12px !important;
}

.viewer-info {
  padding: 15px 20px;
  background: rgba(0, 0, 0, 0.2);
  border-top: 1px solid rgba(255, 255, 255, 0.1);
  color: #ffffff;
}

.viewer-info p {
  margin: 5px 0;
  font-size: 13px;
}

.viewer-info strong {
  color: #4CAF50;
}

.interaction-hint {
  opacity: 0.7;
  font-size: 11px !important;
  font-style: italic;
  margin-top: 8px !important;
}

.no-data {
  display: flex;
  align-items: center;
  justify-content: center;
  height: 300px;
  color: #ffffff;
  text-align: center;
  opacity: 0.7;
}

.no-data p {
  margin: 0;
  font-size: 14px;
}

/* Responsividade */
@media (max-width: 768px) {
  .viewer-header {
    padding: 10px 15px;
  }
  
  .viewer-header h4 {
    font-size: 1em;
  }
  
  .control-button {
    padding: 6px 10px;
    font-size: 14px;
  }
  
  .molecule-viewer {
    height: 250px;
    margin: 8px;
  }
  
  .viewer-info {
    padding: 10px 15px;
  }
  
  .viewer-info p {
    font-size: 12px;
  }
  
  .interaction-hint {
    font-size: 10px !important;
  }
}

/* Animações suaves */
.molecule-viewer-container * {
  transition: all 0.3s ease;
}

/* Efeito de hover no container */
.molecule-viewer-container:hover .molecule-viewer {
  border-color: rgba(76, 175, 80, 0.3);
}

/* Estilo para labels 3D (se necessário) */
.molecule-viewer canvas {
  border-radius: 4px;
}

/* Gradiente de fundo animado */
.molecule-viewer-container::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: linear-gradient(45deg, 
    rgba(76, 175, 80, 0.1) 0%, 
    rgba(33, 150, 243, 0.1) 50%, 
    rgba(156, 39, 176, 0.1) 100%);
  opacity: 0;
  transition: opacity 0.3s ease;
  pointer-events: none;
  z-index: 0;
}

.molecule-viewer-container:hover::before {
  opacity: 1;
}

.molecule-viewer-container > * {
  position: relative;
  z-index: 1;
}
