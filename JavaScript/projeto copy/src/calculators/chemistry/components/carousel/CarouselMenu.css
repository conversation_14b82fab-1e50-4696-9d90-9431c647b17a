/* Definição de variáveis para o carrossel */
:root {
  --carousel-transition-duration: 0.5s;
  --carousel-transition-timing: ease-in-out;
  --carousel-item-width: 120px;
  --carousel-item-height: 30px;
  --carousel-item-gap: 130px;
  --carousel-item-scale: 0.85;
  --carousel-item-z-distance: -30px;
  --carousel-animation-duration: 0.6s;
}

/* Wrapper para todo o carousel */
.carousel-wrapper {
  display: flex;
  flex-direction: column;
  width: 100%;
  margin-bottom: 10px;
  gap: 5px;
  align-items: center;
  position: relative;
  box-sizing: border-box;
}

/* Seção de navegação (menu) */
.carousel-navigation {
  margin-bottom: 5px;
  width: 100%;
  display: flex;
  flex-direction: column;
  align-items: center;
  position: relative;
  z-index: 10;
}

/* Container das abas */
.carousel-tabs {
  display: flex;
  align-items: center;
  justify-content: center;
  width: 100%;
  margin: 0;
  position: relative;
  background-color: rgba(0, 0, 0, 0.3);
  border-top: 1px solid var(--primary-color);  /* Alterado para usar a cor primária diretamente */
  border-bottom: 1px solid var(--primary-color);  /* Alterado para usar a cor primária diretamente */
  border-left: none;
  border-right: none;
  border-radius: 0;
  padding: 8px 0;
  perspective: 1200px;
  perspective-origin: center center;
  overflow: hidden;
}

/* Container das abas visíveis */
.carousel-tabs-container {
  display: flex;
  align-items: center;
  justify-content: center;
  transform-style: preserve-3d;
  position: relative;
  width: 100%;
  max-width: 500px;
  margin: 0 auto;
  height: 40px;
  transition: transform 0.3s ease;
}

/* Estilo das abas */
.carousel-tab {
  background-color: rgba(0, 0, 0, 0.2);
  border: 2px solid var(--primary-color);  /* Alterado para usar a cor primária diretamente */
  border-radius: 8px;
  color: #aaa;
  font-size: 13px;
  font-weight: 600;
  cursor: pointer;
  padding: 0;
  width: var(--carousel-item-width);
  height: var(--carousel-item-height);
  text-align: center;
  position: absolute;
  transform-style: preserve-3d;
  backface-visibility: hidden;
  display: flex;
  align-items: center;
  justify-content: center;
  transition: transform var(--carousel-transition-duration) var(--carousel-transition-timing),
              opacity var(--carousel-transition-duration) var(--carousel-transition-timing),
              color var(--carousel-transition-duration) var(--carousel-transition-timing),
              border-color var(--carousel-transition-duration) var(--carousel-transition-timing);
  will-change: transform, opacity, color, border-color;
}

/* Posicionamento das abas */
.carousel-tab.position-0 {
  transform: translateX(calc(-1 * var(--carousel-item-gap)))
             scale(var(--carousel-item-scale))
             translateZ(var(--carousel-item-z-distance))
             rotateY(25deg);
  opacity: 0.7;
}

.carousel-tab.position-1 {
  transform: translateX(0) scale(1) translateZ(0) rotateY(0);
  z-index: 2;
}

.carousel-tab.position-2 {
  transform: translateX(var(--carousel-item-gap))
             scale(var(--carousel-item-scale))
             translateZ(var(--carousel-item-z-distance))
             rotateY(-25deg);
  opacity: 0.7;
}

/* Efeito de roda - aba ativa (central) */
.carousel-tab.active {
  background-color: rgba(76, 175, 80, 0.1);
  border-color: var(--primary-color);
  color: var(--primary-color);
  box-shadow: 0 0 10px var(--primary-color);  /* Alterado para usar a cor primária diretamente */
}

/* Efeito de hover e foco nas abas */
.carousel-tab:hover,
.carousel-tab:focus {
  color: white;
  outline: none;
  background-color: transparent !important;
}

/* Preservar posição no hover para cada posição */
.carousel-tab.position-0:hover {
  transform: translateX(calc(-1 * var(--carousel-item-gap)))
             scale(var(--carousel-item-scale))
             translateZ(var(--carousel-item-z-distance))
             rotateY(25deg);
}

.carousel-tab.position-1:hover {
  transform: translateX(0) scale(1) translateZ(0) rotateY(0);
}

.carousel-tab.position-2:hover {
  transform: translateX(var(--carousel-item-gap))
             scale(var(--carousel-item-scale))
             translateZ(var(--carousel-item-z-distance))
             rotateY(-25deg);
}

/* Transições para todos os tabs */
.carousel-tab {
  transition: transform var(--carousel-animation-duration) cubic-bezier(0.25, 0.1, 0.25, 1),
              opacity var(--carousel-animation-duration) cubic-bezier(0.25, 0.1, 0.25, 1);
}

/* Garantir que a transição seja aplicada */
.carousel-tabs-container.transitioning {
  will-change: contents;
  animation-play-state: running !important;
  transition: none;
}

/* Forçar animações para serem aplicadas */
.carousel-tabs-container.transitioning .carousel-tab {
  animation-play-state: running !important;
  transition: transform var(--carousel-animation-duration) cubic-bezier(0.25, 0.1, 0.25, 1),
              opacity var(--carousel-animation-duration) cubic-bezier(0.25, 0.1, 0.25, 1) !important;
}

/* Efeito de transição para a direção 'next' */
.carousel-tabs-container.direction-next.transitioning .carousel-tab.position-0 {
  animation: slideOutToLeft var(--carousel-animation-duration) cubic-bezier(0.25, 0.1, 0.25, 1) forwards;
}

@keyframes slideOutToLeft {
  0% {
    transform: translateX(calc(-1 * var(--carousel-item-gap)))
               scale(var(--carousel-item-scale))
               translateZ(var(--carousel-item-z-distance))
               rotateY(25deg);
    opacity: 0.7;
  }
  100% {
    transform: translateX(calc(-1.2 * var(--carousel-item-gap)))
               scale(0.8)
               translateZ(var(--carousel-item-z-distance))
               rotateY(30deg);
    opacity: 0;
  }
}

.carousel-tabs-container.direction-next.transitioning .carousel-tab.position-1:not(.active) {
  transform: translateX(calc(-1 * var(--carousel-item-gap)))
             scale(var(--carousel-item-scale))
             translateZ(var(--carousel-item-z-distance))
             rotateY(25deg);
  opacity: 0.7;
}

.carousel-tabs-container.direction-next.transitioning .carousel-tab.position-2.active {
  transform: translateX(0) scale(1) translateZ(0) rotateY(0);
  opacity: 1;
  z-index: 3;
}

/* Efeito para novas opções entrando da direita */
.carousel-tabs-container.direction-next.transitioning .carousel-tab.entering-right {
  transform: translateX(calc(1.2 * var(--carousel-item-gap)))
             scale(0.8)
             translateZ(calc(1.2 * var(--carousel-item-z-distance)))
             rotateY(-30deg);
  opacity: 0;
  animation: slideInFromRight var(--carousel-animation-duration) cubic-bezier(0.25, 0.1, 0.25, 1) forwards;
}

@keyframes slideInFromRight {
  0% {
    transform: translateX(calc(1.2 * var(--carousel-item-gap)))
               scale(0.8)
               translateZ(calc(1.2 * var(--carousel-item-z-distance)))
               rotateY(-30deg);
    opacity: 0;
  }
  80% {
    transform: translateX(var(--carousel-item-gap))
               scale(var(--carousel-item-scale))
               translateZ(var(--carousel-item-z-distance))
               rotateY(-25deg);
    opacity: 0.7;
  }
  100% {
    transform: translateX(var(--carousel-item-gap))
               scale(var(--carousel-item-scale))
               translateZ(var(--carousel-item-z-distance))
               rotateY(-25deg);
    opacity: 0.7;
  }
}

/* Efeito de transição para a direção 'prev' */
.carousel-tabs-container.direction-prev.transitioning .carousel-tab.position-0.active {
  transform: translateX(0) scale(1) translateZ(0) rotateY(0);
  opacity: 1;
  z-index: 3;
}

.carousel-tabs-container.direction-prev.transitioning .carousel-tab.position-1:not(.active) {
  transform: translateX(var(--carousel-item-gap))
             scale(var(--carousel-item-scale))
             translateZ(var(--carousel-item-z-distance))
             rotateY(-25deg);
  opacity: 0.7;
}

.carousel-tabs-container.direction-prev.transitioning .carousel-tab.position-2 {
  animation: slideOutToRight var(--carousel-animation-duration) cubic-bezier(0.25, 0.1, 0.25, 1) forwards;
}

@keyframes slideOutToRight {
  0% {
    transform: translateX(var(--carousel-item-gap))
               scale(var(--carousel-item-scale))
               translateZ(var(--carousel-item-z-distance))
               rotateY(-25deg);
    opacity: 0.7;
  }
  100% {
    transform: translateX(calc(1.2 * var(--carousel-item-gap)))
               scale(0.8)
               translateZ(calc(1.2 * var(--carousel-item-z-distance)))
               rotateY(-30deg);
    opacity: 0;
  }
}

/* Efeito para novas opções entrando da esquerda */
.carousel-tabs-container.direction-prev.transitioning .carousel-tab.entering-left {
  transform: translateX(calc(-1.2 * var(--carousel-item-gap)))
             scale(0.8)
             translateZ(calc(1.2 * var(--carousel-item-z-distance)))
             rotateY(30deg);
  opacity: 0;
  animation: slideInFromLeft var(--carousel-animation-duration) cubic-bezier(0.25, 0.1, 0.25, 1) forwards;
}

@keyframes slideInFromLeft {
  0% {
    transform: translateX(calc(-1.2 * var(--carousel-item-gap)))
               scale(0.8)
               translateZ(calc(1.2 * var(--carousel-item-z-distance)))
               rotateY(30deg);
    opacity: 0;
  }
  80% {
    transform: translateX(calc(-1 * var(--carousel-item-gap)))
               scale(var(--carousel-item-scale))
               translateZ(var(--carousel-item-z-distance))
               rotateY(25deg);
    opacity: 0.7;
  }
  100% {
    transform: translateX(calc(-1 * var(--carousel-item-gap)))
               scale(var(--carousel-item-scale))
               translateZ(var(--carousel-item-z-distance))
               rotateY(25deg);
    opacity: 0.7;
  }
}

/* Botões de navegação */
.carousel-tab-button {
  background: none;
  border: none;
  color: #aaa;
  font-size: 16px;
  cursor: pointer;
  padding: 0;
  width: 25px;
  height: 25px;
  display: flex;
  align-items: center;
  justify-content: center;
  position: absolute;
  top: 50%;
  transform: translateY(-50%);
  z-index: 10;
  transition: color 0.3s ease;
  transform-style: flat;
  backface-visibility: visible;
  perspective: none;
  transform-origin: 50% 50%;
}

.carousel-tab-button.prev {
  left: 30%;
}

.carousel-tab-button.next {
  right: 30%;
}

.carousel-tab-button:hover {
  color: var(--primary-color);
  transform: translateY(-50%) !important;
}

/* Sobrescrever qualquer transformação que possa ser aplicada por outros estilos */
.carousel-tab-button:hover,
.carousel-tab-button:focus,
.carousel-tab-button:active {
  transform: translateY(-50%) !important;
}

/* Garantir que as setas não se movam no hover - regra mais específica */
.compound-carousel-navigation .compound-carousel-tab-button:hover,
.compound-carousel-navigation .compound-carousel-tab-button:focus,
.compound-carousel-navigation .compound-carousel-tab-button:active {
  transform: translateY(-50%) !important;
  transition: color 0.3s ease !important;
}

/* Efeito de transição para o conteúdo do slide */
.slide-content-wrapper {
  width: 100%;
  transition: opacity 0.4s ease;
}

.slide-content-wrapper.fade {
  opacity: 0.5;
}

.slide-content-wrapper.visible {
  opacity: 1;
}

/* Conteúdo dos slides */
.carousel-content {
  padding: 0;
  background-color: transparent;
  border: none;
  width: 100%;
  display: flex;
  justify-content: center;
  position: relative;
  min-height: 150px;
  overflow: visible;
  margin: 0 auto;
}