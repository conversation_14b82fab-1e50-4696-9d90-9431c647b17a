/* Correção para o fundo verde do carousel do compound */
.carousel-tabs {
  background-color: rgba(0, 0, 0, 0.3) !important;
  border-top: 1px solid var(--border-color) !important;
  border-bottom: 1px solid var(--border-color) !important;
  border-left: none !important;
  border-right: none !important;
}

/* Garan<PERSON>r que os botões não tenham fundo */
.carousel-tab-button {
  background: none !important;
}

/* Corrigir o estilo das abas */
.carousel-tab {
  background-color: rgba(0, 0, 0, 0.2) !important;
  border: 2px solid var(--border-color) !important;
  color: #aaa !important;
}

/* Corrigir o estilo da aba ativa */
.carousel-tab.active {
  background-color: rgba(0, 0, 0, 0.3) !important;
  border-color: var(--primary-color) !important;
  color: var(--primary-color) !important;
  box-shadow: 0 0 10px rgba(0, 0, 0, 0.3) !important;
}

/* Correção específica para as setas do carousel - evitar movimento no hover */
.compound-carousel-navigation .compound-carousel-tab-button {
  position: absolute !important;
  top: 50% !important;
  transform: translateY(-50%) !important;
  transition: color 0.3s ease !important;
}

.compound-carousel-navigation .compound-carousel-tab-button:hover,
.compound-carousel-navigation .compound-carousel-tab-button:focus,
.compound-carousel-navigation .compound-carousel-tab-button:active {
  transform: translateY(-50%) !important;
  color: var(--primary-color) !important;
}

.compound-carousel-navigation .compound-carousel-tab-button.prev {
  left: 30% !important;
}

.compound-carousel-navigation .compound-carousel-tab-button.next {
  right: 30% !important;
}
