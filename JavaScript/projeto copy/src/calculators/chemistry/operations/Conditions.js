import React from 'react';

const Conditions = ({
  pressure,
  setPressure,
  temperature,
  setTemperature,
  pressureUnit,
  temperatureUnit,
  selectedPreset,
  setSelectedPreset,
  handlePresetChange,
  getPresetButtonText
}) => {
  return (
    <div style={{ width: '100%', display: 'flex', flexDirection: 'column', alignItems: 'center', position: 'static' }}>
      <div className="conditions-container operation-container" style={{
        display: 'flex',
        flexDirection: 'column',
        justifyContent: 'space-between',
        alignItems: 'center',
        width: '100%',
        padding: '20px',
        backgroundColor: 'rgba(255, 255, 255, 0.15)', // Fundo mais claro (mais branco)
        borderRadius: '8px',
        border: '1px solid var(--primary-color)',
        boxShadow: '0 4px 8px rgba(0, 0, 0, 0.2)'
      }}>
      <div className="condition-row">
        <div className="condition-input">
          <label>Pressure:</label>
          <div className="parameter-input-container">
            <input
              type="text"
              value={pressure || ''}
              onChange={(e) => {
                const value = e.target.value;
                const numValue = value === '' ? 0 : parseFloat(value);
                if (!isNaN(numValue)) {
                  setPressure(numValue);
                  setSelectedPreset(null);
                }
              }}
              onFocus={() => {
                // Limpar o campo quando o usuário clica nele
                setPressure(null);
                setSelectedPreset(null);
              }}
              style={{ textAlign: 'center', height: '35px', fontSize: '14px' }}
            />
            <span className="parameter-unit">{pressureUnit}</span>
          </div>
        </div>
        <div className="condition-input">
          <label>Temperature:</label>
          <div className="parameter-input-container">
            <input
              type="text"
              value={temperature || ''}
              onChange={(e) => {
                const value = e.target.value;
                const numValue = value === '' ? 0 : parseFloat(value);
                if (!isNaN(numValue)) {
                  setTemperature(numValue);
                  setSelectedPreset(null);
                }
              }}
              onFocus={() => {
                // Limpar o campo quando o usuário clica nele
                setTemperature(null);
                setSelectedPreset(null);
              }}
              style={{ textAlign: 'center', height: '35px', fontSize: '14px' }}
            />
            <span className="parameter-unit">{temperatureUnit}</span>
          </div>
        </div>
      </div>

      <div className="presets-container">
        <button
          className={`preset-button ${selectedPreset === 'STP' ? 'active' : ''}`}
          onClick={() => handlePresetChange('STP')}
        >
          {getPresetButtonText('STP')}
        </button>
        <button
          className={`preset-button ${selectedPreset === 'SATP' ? 'active' : ''}`}
          onClick={() => handlePresetChange('SATP')}
        >
          {getPresetButtonText('SATP')}
        </button>
        <button
          className={`preset-button ${selectedPreset === 'custom' ? 'active' : ''}`}
          onClick={() => setSelectedPreset('custom')}
        >
          Custom
        </button>
      </div>
    </div>
    </div>
  );
};

export default Conditions;
