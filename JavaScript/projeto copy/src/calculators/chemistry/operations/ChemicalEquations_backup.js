import React, { useState, useEffect, useCallback } from 'react';
import { calcularMassaMolar, parseComposto, formatChemicalFormula, formatScientificNotation } from '../constants/chemistryUtils';
import Select from 'react-select';

// Custom styles for selects
const selectStyles = {
  control: (base) => ({
    ...base,
    backgroundColor: 'rgba(0, 0, 0, 0.3)',
    border: '1px solid var(--border-color)',
    borderRadius: '4px',
    minHeight: '35px',
    fontSize: '0.9em'
  }),
  menu: (base) => ({
    ...base,
    backgroundColor: '#000000',
    border: '1px solid var(--border-color)',
    borderRadius: '0 0 4px 4px',
    zIndex: 9999
  }),
  option: (base, state) => ({
    ...base,
    backgroundColor: state.isFocused ? 'rgba(76, 175, 80, 0.2)' : 'transparent',
    color: 'white',
    cursor: 'pointer',
    fontSize: '0.9em',
    padding: '8px 12px'
  }),
  singleValue: (base) => ({
    ...base,
    color: 'white',
    fontSize: '0.9em'
  }),
  placeholder: (base) => ({
    ...base,
    color: '#888',
    fontSize: '0.9em'
  }),
  dropdownIndicator: (base) => ({
    ...base,
    color: 'var(--border-color)',
    '&:hover': {
      color: 'white'
    }
  }),
  indicatorSeparator: () => ({
    display: 'none'
  })
};

// Tab styles
const tabStyles = {
  container: {
    width: '100%',
    backgroundColor: 'rgba(0, 0, 0, 0.2)',
    borderRadius: '8px',
    border: '1px solid var(--border-color)',
    overflow: 'hidden'
  },
  tabList: {
    display: 'flex',
    backgroundColor: 'rgba(0, 0, 0, 0.3)',
    borderBottom: '1px solid var(--border-color)'
  },
  tab: {
    flex: 1,
    padding: '12px 20px',
    backgroundColor: 'transparent',
    border: 'none',
    color: '#ccc',
    cursor: 'pointer',
    fontSize: '1em',
    transition: 'all 0.3s ease',
    borderRight: '1px solid var(--border-color)'
  },
  activeTab: {
    backgroundColor: 'var(--primary-color)',
    color: 'white'
  },
  tabContent: {
    padding: '20px'
  }
};

// Mass unit options
const massUnitOptions = [
  { value: 'g', label: 'g' },
  { value: 'kg', label: 'kg' },
  { value: 'mg', label: 'mg' },
  { value: 'μg', label: 'μg' },
  { value: 'ng', label: 'ng' },
  { value: 'pg', label: 'pg' }
];

// Function to parse a chemical equation
const parseEquation = (equation) => {
  try {
    // Remove extra spaces and normalize
    const normalized = equation.trim().replace(/\s+/g, ' ');

    // Split by arrow (-> or →)
    const parts = normalized.split(/\s*(?:->|→)\s*/);

    if (parts.length !== 2) {
      return { error: 'Invalid equation format. Use: reactants -> products' };
    }

    const [reactantsStr, productsStr] = parts;

    // Parse reactants
    const reactants = reactantsStr.split(/\s*\+\s*/).map(reactant => {
      const match = reactant.match(/^(\d*)(.+)$/);
      const coefficient = match[1] ? parseInt(match[1]) : 1;
      const formula = match[2];
      return { formula, coefficient };
    });

    // Parse products
    const products = productsStr.split(/\s*\+\s*/).map(product => {
      const match = product.match(/^(\d*)(.+)$/);
      const coefficient = match[1] ? parseInt(match[1]) : 1;
      const formula = match[2];
      return { formula, coefficient };
    });

    return { reactants, products };
  } catch (error) {
    return { error: 'Error parsing equation: ' + error.message };
  }
};

// Function to balance a chemical equation using matrix algebra
const balanceEquation = (reactants, products) => {
  try {
    // Get all unique elements
    const allElements = new Set();
    const allCompounds = [...reactants, ...products];

    allCompounds.forEach(compound => {
      const parseResult = parseComposto(compound.formula);
      const elements = parseResult.elementos || parseResult;
      elements.forEach(el => allElements.add(el.simbolo));
    });

    const elementsList = Array.from(allElements);
    const numCompounds = allCompounds.length;

    // Create matrix where each row is an element and each column is a compound
    const matrix = [];

    elementsList.forEach(element => {
      const row = [];
      allCompounds.forEach((compound, index) => {
        const parseResult = parseComposto(compound.formula);
        const elements = parseResult.elementos || parseResult;
        const elementData = elements.find(el => el.simbolo === element);
        const count = elementData ? elementData.quantidade : 0;

        // For products, use negative values (moved to left side of equation)
        if (index >= reactants.length) {
          row.push(-count);
        } else {
          row.push(count);
        }
      });
      matrix.push(row);
    });

    // Try to solve using simple integer solutions
    const coefficients = solveBalanceMatrix(matrix, numCompounds);

    if (coefficients) {
      // Apply coefficients to compounds
      const balancedReactants = reactants.map((reactant, index) => ({
        ...reactant,
        coefficient: coefficients[index]
      }));

      const balancedProducts = products.map((product, index) => ({
        ...product,
        coefficient: coefficients[reactants.length + index]
      }));

      // Verify the balance
      const elementCount = {};

      // Count elements in balanced reactants
      balancedReactants.forEach(reactant => {
        const parseResult = parseComposto(reactant.formula);
        const elements = parseResult.elementos || parseResult;
        elements.forEach(el => {
          const key = el.simbolo;
          if (!elementCount[key]) elementCount[key] = { reactants: 0, products: 0 };
          elementCount[key].reactants += el.quantidade * reactant.coefficient;
        });
      });

      // Count elements in balanced products
      balancedProducts.forEach(product => {
        const parseResult = parseComposto(product.formula);
        const elements = parseResult.elementos || parseResult;
        elements.forEach(el => {
          const key = el.simbolo;
          if (!elementCount[key]) elementCount[key] = { reactants: 0, products: 0 };
          elementCount[key].products += el.quantidade * product.coefficient;
        });
      });

      const isBalanced = Object.values(elementCount).every(
        count => count.reactants === count.products
      );

      return {
        isBalanced,
        elementCount,
        balancedReactants,
        balancedProducts
      };
    }

    // If automatic balancing fails, return original with balance check
    const elementCount = {};

    reactants.forEach(reactant => {
      const parseResult = parseComposto(reactant.formula);
      const elements = parseResult.elementos || parseResult;
      elements.forEach(el => {
        const key = el.simbolo;
        if (!elementCount[key]) elementCount[key] = { reactants: 0, products: 0 };
        elementCount[key].reactants += el.quantidade * reactant.coefficient;
      });
    });

    products.forEach(product => {
      const parseResult = parseComposto(product.formula);
      const elements = parseResult.elementos || parseResult;
      elements.forEach(el => {
        const key = el.simbolo;
        if (!elementCount[key]) elementCount[key] = { reactants: 0, products: 0 };
        elementCount[key].products += el.quantidade * product.coefficient;
      });
    });

    const isBalanced = Object.values(elementCount).every(
      count => count.reactants === count.products
    );

    return {
      isBalanced,
      elementCount,
      balancedReactants: [...reactants],
      balancedProducts: [...products]
    };

  } catch (error) {
    console.error('Error balancing equation:', error);
    return {
      isBalanced: false,
      elementCount: {},
      balancedReactants: [...reactants],
      balancedProducts: [...products]
    };
  }
};

// Helper function to solve the balance matrix
const solveBalanceMatrix = (matrix, numCompounds) => {
  // Try to solve systematically for common equations

  // Special cases for common equation patterns
  if (numCompounds === 3) {
    // A + B -> C pattern
    return trySimpleBalance(matrix, numCompounds);
  } else if (numCompounds === 4) {
    // A + B -> C + D pattern
    return trySimpleBalance(matrix, numCompounds);
  }

  // For more complex equations, use brute force with optimized search
  return tryBruteForceBalance(matrix, numCompounds);
};

// Try simple balancing for common patterns
const trySimpleBalance = (matrix, numCompounds) => {
  const maxCoeff = 20;

  // Try systematic approach starting with coefficient 1 for first compound
  for (let c1 = 1; c1 <= maxCoeff; c1++) {
    for (let c2 = 1; c2 <= maxCoeff; c2++) {
      if (numCompounds === 3) {
        for (let c3 = 1; c3 <= maxCoeff; c3++) {
          const coeffs = [c1, c2, c3];
          if (testCoefficients(matrix, coeffs)) {
            return reduceCoefficients(coeffs);
          }
        }
      } else if (numCompounds === 4) {
        for (let c3 = 1; c3 <= maxCoeff; c3++) {
          for (let c4 = 1; c4 <= maxCoeff; c4++) {
            const coeffs = [c1, c2, c3, c4];
            if (testCoefficients(matrix, coeffs)) {
              return reduceCoefficients(coeffs);
            }
          }
        }
      }
    }
  }

  return null;
};

// Try brute force for complex equations
const tryBruteForceBalance = (matrix, numCompounds) => {
  const maxCoeff = 10;

  // Generate combinations more efficiently
  const testCombination = (coeffs) => {
    return testCoefficients(matrix, coeffs);
  };

  // Recursive function to generate and test combinations
  const findSolution = (current, remaining) => {
    if (remaining === 0) {
      if (current.every(c => c > 0) && testCombination(current)) {
        return reduceCoefficients([...current]);
      }
      return null;
    }

    for (let i = 1; i <= maxCoeff; i++) {
      current.push(i);
      const result = findSolution(current, remaining - 1);
      if (result) {
        return result;
      }
      current.pop();
    }

    return null;
  };

  return findSolution([], numCompounds);
};

// Test if coefficients satisfy the balance equations
const testCoefficients = (matrix, coeffs) => {
  for (const row of matrix) {
    let sum = 0;
    for (let i = 0; i < row.length && i < coeffs.length; i++) {
      sum += row[i] * coeffs[i];
    }
    if (Math.abs(sum) > 0.001) {
      return false;
    }
  }
  return true;
};

// Reduce coefficients to smallest integers
const reduceCoefficients = (coeffs) => {
  const gcd = (a, b) => b === 0 ? Math.abs(a) : gcd(b, a % b);
  const gcdAll = coeffs.reduce((acc, val) => gcd(acc, val));

  if (gcdAll > 1) {
    return coeffs.map(c => c / gcdAll);
  }
  return coeffs;
};

// Database of chemical reactions for product prediction
const reactionDatabase = [
  // Acid-Base reactions
  {
    type: 'acid-base',
    pattern: ['acid', 'base'],
    products: ['salt', 'water'],
    examples: [
      { reactants: ['HCl', 'NaOH'], products: ['NaCl', 'H2O'] },
      { reactants: ['H2SO4', 'Ca(OH)2'], products: ['CaSO4', 'H2O'] }
    ]
  },
  // Synthesis reactions
  {
    type: 'synthesis',
    pattern: ['element', 'element'],
    products: ['compound'],
    examples: [
      { reactants: ['H2', 'O2'], products: ['H2O'] },
      { reactants: ['Na', 'Cl2'], products: ['NaCl'] }
    ]
  },
  // Single displacement
  {
    type: 'single-displacement',
    pattern: ['element', 'compound'],
    products: ['element', 'compound'],
    examples: [
      { reactants: ['Zn', 'CuSO4'], products: ['Cu', 'ZnSO4'] },
      { reactants: ['Mg', 'HCl'], products: ['H2', 'MgCl2'] }
    ]
  },
  // Double displacement
  {
    type: 'double-displacement',
    pattern: ['compound', 'compound'],
    products: ['compound', 'compound'],
    examples: [
      { reactants: ['AgNO3', 'NaCl'], products: ['AgCl', 'NaNO3'] },
      { reactants: ['BaCl2', 'Na2SO4'], products: ['BaSO4', 'NaCl'] }
    ]
  },
  // Combustion
  {
    type: 'combustion',
    pattern: ['hydrocarbon', 'O2'],
    products: ['CO2', 'H2O'],
    examples: [
      { reactants: ['CH4', 'O2'], products: ['CO2', 'H2O'] },
      { reactants: ['C2H6', 'O2'], products: ['CO2', 'H2O'] }
    ]
  }
];

// Function to analyze the chemical structure of a compound
const parseChemicalStructure = (compound) => {
  // Remove spaces and normalize
  const normalizedCompound = compound.trim();

  // Check if it's a simple element (1-2 letters, possibly with a number)
  const elementRegex = /^([A-Z][a-z]?)(\d*)$/;
  const elementMatch = normalizedCompound.match(elementRegex);

  if (elementMatch) {
    const element = elementMatch[1];
    const count = elementMatch[2] ? parseInt(elementMatch[2]) : 1;
    return {
      type: 'element',
      elements: [{ symbol: element, count }]
    };
  }

  // Parse compound using existing parser
  try {
    const parseResult = parseComposto(normalizedCompound);
    const elements = parseResult.elementos || parseResult;

    // Determine compound type based on elements
    const elementSymbols = elements.map(el => el.simbolo);

    // Check for acid (starts with H)
    if (normalizedCompound.startsWith('H') && elementSymbols.length > 1) {
      return { type: 'acid', elements };
    }

    // Check for base (contains OH)
    if (normalizedCompound.includes('OH')) {
      return { type: 'base', elements };
    }

    // Check for hydrocarbon (contains only C and H)
    if (elementSymbols.every(symbol => ['C', 'H'].includes(symbol))) {
      return { type: 'hydrocarbon', elements };
    }

    // Check for salt (ionic compound)
    if (elementSymbols.length >= 2) {
      return { type: 'salt', elements };
    }

    return { type: 'compound', elements };
  } catch (error) {
    return { type: 'unknown', elements: [] };
  }
};

// Function to predict reaction products
const predictProducts = (reactant1, reactant2) => {
  if (!reactant1 || !reactant2) {
    return { error: 'Please provide both reactants' };
  }

  const structure1 = parseChemicalStructure(reactant1);
  const structure2 = parseChemicalStructure(reactant2);

  // Find matching reaction patterns
  const possibleReactions = reactionDatabase.filter(reaction => {
    const pattern = reaction.pattern;
    return (
      (pattern[0] === structure1.type && pattern[1] === structure2.type) ||
      (pattern[0] === structure2.type && pattern[1] === structure1.type)
    );
  });

  if (possibleReactions.length === 0) {
    return { error: 'No known reaction pattern found for these reactants' };
  }

  // For now, return the first matching reaction
  const selectedReaction = possibleReactions[0];

  // Generate predicted products based on reaction type
  let predictedProducts = [];

  switch (selectedReaction.type) {
    case 'acid-base':
      // Simple acid-base: acid + base -> salt + water
      predictedProducts = generateAcidBaseProducts(reactant1, reactant2, structure1, structure2);
      break;

    case 'synthesis':
      // Simple synthesis: element + element -> compound
      predictedProducts = generateSynthesisProducts(reactant1, reactant2, structure1, structure2);
      break;

    case 'single-displacement':
      // Single displacement: element + compound -> element + compound
      predictedProducts = generateSingleDisplacementProducts(reactant1, reactant2, structure1, structure2);
      break;

    case 'double-displacement':
      // Double displacement: compound + compound -> compound + compound
      predictedProducts = generateDoubleDisplacementProducts(reactant1, reactant2, structure1, structure2);
      break;

    case 'combustion':
      // Combustion: hydrocarbon + O2 -> CO2 + H2O
      predictedProducts = generateCombustionProducts(reactant1, reactant2, structure1, structure2);
      break;

    default:
      return { error: 'Unknown reaction type' };
  }

  return {
    type: selectedReaction.type,
    products: predictedProducts,
    reactants: [reactant1, reactant2]
  };
};

// Helper functions for generating products
const generateAcidBaseProducts = (reactant1, reactant2, structure1, structure2) => {
  // Simplified acid-base product generation
  // This would need more sophisticated logic for real implementation
  return ['Salt', 'H2O'];
};

const generateSynthesisProducts = (reactant1, reactant2, structure1, structure2) => {
  // Simple combination of elements
  const elements1 = structure1.elements || [];
  const elements2 = structure2.elements || [];

  if (elements1.length === 1 && elements2.length === 1) {
    const element1 = elements1[0].symbol;
    const element2 = elements2[0].symbol;
    return [`${element1}${element2}`];
  }

  return ['Compound'];
};

const generateSingleDisplacementProducts = (reactant1, reactant2, structure1, structure2) => {
  // Simplified single displacement
  return ['Element', 'Compound'];
};

const generateDoubleDisplacementProducts = (reactant1, reactant2, structure1, structure2) => {
  // Simplified double displacement
  return ['Compound1', 'Compound2'];
};

const generateCombustionProducts = (reactant1, reactant2, structure1, structure2) => {
  // Combustion always produces CO2 and H2O for hydrocarbons
  return ['CO2', 'H2O'];
};
