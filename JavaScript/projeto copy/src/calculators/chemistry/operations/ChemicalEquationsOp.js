import React from 'react';
import ChemicalEquations from './ChemicalEquations';

const ChemicalEquationsOp = () => {
  return (
    <div style={{ width: '100%', display: 'flex', flexDirection: 'column', alignItems: 'center', position: 'static' }}>
      <div className="operation-container" style={{
        display: 'flex',
        flexDirection: 'column',
        justifyContent: 'space-between',
        alignItems: 'center',
        width: '100%',
        padding: '20px',
        backgroundColor: 'rgba(255, 255, 255, 0.15)', // Fundo mais claro (mais branco)
        borderRadius: '8px',
        border: '1px solid var(--primary-color)',
        boxShadow: '0 4px 8px rgba(0, 0, 0, 0.2)'
      }}>
        <ChemicalEquations />
      </div>
    </div>
  );
};

export default ChemicalEquationsOp;
