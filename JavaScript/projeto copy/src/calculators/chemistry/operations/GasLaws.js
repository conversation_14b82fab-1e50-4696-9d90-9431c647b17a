import React, { useEffect } from 'react';

const GasLaws = ({
  activeGasLaw,
  setActiveGasLaw,
  R_IDEAL_GAS_CONSTANT,
  quantityValue,
  setQuantityValue,
  volumeValue,
  setVolumeValue,
  pressure,
  setPressure,
  temperature,
  setTemperature,
  quantityUnit,
  volumeUnit,
  pressureUnit,
  temperatureUnit,
  setLastIdealGasField,
  setIsTypingIdealGas,
  calculateIdealGasLaw,
  selectedPreset,
  handlePresetChange,
  getPresetButtonText,
  setSelectedPreset,
  checkForPresetMatch,
  pressure1,
  setPressure1,
  volume1,
  setVolume1,
  pressure2,
  setPressure2,
  volume2,
  setVolume2,
  setLastBoyleField,
  setIsTypingBoyle,
  calculateBoyleLaw,
  volume1Charles,
  setVolume1Charles,
  temperature1,
  setTemperature1,
  volume2Charles,
  setVolume2Charles,
  temperature2,
  setTemperature2,
  setLastCharlesField,
  setIsTypingCharles,
  calculateCharlesLaw,
  gasLawResult,
  gasLawError,
  clearGasLawResults
}) => {
  // Adiciona validação de entrada para números
  const validateNumericInput = (value) => {
    if (value === '') return true;
    return !isNaN(value) && value >= 0;
  };

  // Função padrão para texto dos presets
  const defaultGetPresetButtonText = (preset) => {
    switch (preset) {
      case 'STP':
        return 'STP (0°C, 1 atm)';
      case 'SATP':
        return 'SATP (25°C, 1 atm)';
      default:
        return preset;
    }
  };

  // Usa a função passada como prop ou a função padrão
  const finalGetPresetButtonText = getPresetButtonText || defaultGetPresetButtonText;

  // Limpa os timeouts quando o componente é desmontado
  useEffect(() => {
    return () => {
      const timeouts = [checkForPresetMatch].filter(Boolean);
      timeouts.forEach(clearTimeout);
    };
  }, [checkForPresetMatch]);

  // Função de validação para inputs (sem cálculo automático)
  const handleNumericInput = (value, setValue, setLastField, field, isTyping) => {
    if (validateNumericInput(value)) {
      setValue(value);
      setLastField(field);
      isTyping(true);
      // Limpar resultados quando o usuário começar a digitar
      if (clearGasLawResults) {
        clearGasLawResults();
      }
    }
  };

  return (
    <div style={{ width: '100%', display: 'flex', flexDirection: 'column', alignItems: 'center', position: 'static' }}>
      <div className="gas-laws-container operation-container" style={{
        display: 'flex',
        flexDirection: 'column',
        justifyContent: 'space-between',
        alignItems: 'center',
        width: '100%',
        padding: '20px',
        backgroundColor: 'rgba(255, 255, 255, 0.15)', // Fundo mais claro (mais branco)
        borderRadius: '8px',
        border: '1px solid var(--primary-color)',
        boxShadow: '0 4px 8px rgba(0, 0, 0, 0.2)'
      }}>
      <div className="gas-law-tabs"
      style={{
        display: 'flex',
        justifyContent: 'space-between',
        alignItems: 'center',
        width: '100%',
        marginBottom: '20px',
        boxShadow: '0 4px 8px rgba(0, 0, 0, 0.2)'
      }}>
        <button
          className={`gas-law-tab ${activeGasLaw === 'ideal' ? 'active' : ''}`}
          onClick={() => setActiveGasLaw('ideal')}
        >
          <span className="law-name">Ideal Gas Law</span>
          <span className="law-formula">(PV = nRT)</span>
        </button>
        <button
          className={`gas-law-tab ${activeGasLaw === 'boyle' ? 'active' : ''}`}
          onClick={() => setActiveGasLaw('boyle')}
        >
          <span className="law-name">Boyle's Law</span>
          <span className="law-formula">(P₁V₁ = P₂V₂)</span>
        </button>
        <button
          className={`gas-law-tab ${activeGasLaw === 'charles' ? 'active' : ''}`}
          onClick={() => setActiveGasLaw('charles')}
        >
          <span className="law-name">Charles's Law</span>
          <span className="law-formula">(V₁/T₁ = V₂/T₂)</span>
        </button>
      </div>

      <div className="gas-law-content" style={{ padding: '15px' }}>
        {activeGasLaw === 'ideal' && (
          <>
            <div className="gas-law-equation">
              <p className="r-constant">R = {R_IDEAL_GAS_CONSTANT} L·atm/(mol·K)</p>
              <p className="equation-main">PV = nRT</p>
              <p className="equation-description">Ideal Gas Law</p>
            </div>

            <div className="gas-law-parameters"
            style={{
              gap: '15px',
              marginBottom: '15px',
              display: 'flex',
              justifyContent: 'space-between',
              width: '100%'
            }}>
              <div className="gas-law-row" style={{ gap: '15px' }}>
                <div className="gas-law-parameter">
                  <label>Moles (n):</label>
                  <div className="parameter-input-container">
                    <input
                      type="text"
                      value={quantityValue || ''}
                      onChange={(e) => {
                        handleNumericInput(
                          e.target.value,
                          setQuantityValue,
                          setLastIdealGasField,
                          'quantity',
                          setIsTypingIdealGas
                        );
                      }}
                      onFocus={() => {
                        setQuantityValue('');
                      }}
                      onKeyDown={(e) => {
                        if (e.key === 'Enter') {
                          setIsTypingIdealGas(false);
                          calculateIdealGasLaw();
                        }
                      }}

                    />
                    <span className="parameter-unit">{quantityUnit}</span>
                  </div>
                </div>
                <div className="gas-law-parameter">
                  <label>Volume (V):</label>
                  <div className="parameter-input-container">
                    <input
                      type="text"
                      value={volumeValue || ''}
                      onChange={(e) => {
                        const value = e.target.value;
                        setVolumeValue(value);
                        setLastIdealGasField('volume');
                        setIsTypingIdealGas(true);
                        if (clearGasLawResults) {
                          clearGasLawResults();
                        }
                      }}
                      onFocus={() => {
                        setVolumeValue('');
                      }}
                      onKeyDown={(e) => {
                        if (e.key === 'Enter') {
                          setIsTypingIdealGas(false);
                          calculateIdealGasLaw();
                        }
                      }}

                    />
                    <span className="parameter-unit">{volumeUnit}</span>
                  </div>
                </div>
              </div>

              <div className="gas-law-row" style={{ gap: '15px' }}>
                <div className="gas-law-parameter">
                  <label>Pressure (P):</label>
                  <div className="parameter-input-container">
                    <input
                      type="text"
                      value={pressure || ''}
                      onChange={(e) => {
                        const value = e.target.value;
                        setPressure(value);
                        setLastIdealGasField('pressure');
                        setIsTypingIdealGas(true);
                        if (clearGasLawResults) {
                          clearGasLawResults();
                        }
                        setTimeout(() => checkForPresetMatch(), 100);
                      }}
                      onFocus={() => {
                        setPressure('');
                      }}
                      onKeyDown={(e) => {
                        if (e.key === 'Enter') {
                          setIsTypingIdealGas(false);
                          calculateIdealGasLaw();
                        }
                      }}

                    />
                    <span className="parameter-unit">{pressureUnit}</span>
                  </div>
                </div>
                <div className="gas-law-parameter">
                  <label>Temperature (T):</label>
                  <div className="parameter-input-container">
                    <input
                      type="text"
                      value={temperature || ''}
                      onChange={(e) => {
                        const value = e.target.value;
                        setTemperature(value);
                        setLastIdealGasField('temperature');
                        setIsTypingIdealGas(true);
                        if (clearGasLawResults) {
                          clearGasLawResults();
                        }
                        setTimeout(() => checkForPresetMatch(), 100);
                      }}
                      onFocus={() => {
                        setTemperature('');
                      }}
                      onKeyDown={(e) => {
                        if (e.key === 'Enter') {
                          setIsTypingIdealGas(false);
                          calculateIdealGasLaw();
                        }
                      }}

                    />
                    <span className="parameter-unit">{temperatureUnit}</span>
                  </div>
                </div>
              </div>
            </div>

            <div className="presets-container">
              <button
                className={`preset-button ${selectedPreset === 'STP' ? 'active' : ''}`}
                onClick={() => handlePresetChange('STP')}
              >
                {finalGetPresetButtonText('STP')}
              </button>
              <button
                className={`preset-button ${selectedPreset === 'SATP' ? 'active' : ''}`}
                onClick={() => handlePresetChange('SATP')}
              >
                {finalGetPresetButtonText('SATP')}
              </button>
              <button
                className={`preset-button ${selectedPreset === 'custom' ? 'active' : ''}`}
                onClick={() => setSelectedPreset('custom')}
              >
                Custom
              </button>
            </div>

            <button
              className="calculate-button"
              onClick={() => {
                setIsTypingIdealGas(false);
                calculateIdealGasLaw();
                checkForPresetMatch();
              }}
              data-action="calculate"
              data-tooltip="Calculate (Alt+C)"
              style={{ padding: '8px 20px', fontSize: '14px' }}
            >
              Calculate
            </button>

            {/* Área de resultados */}
            {(gasLawResult || gasLawError) && (
              <div className="gas-law-results" style={{
                marginTop: '20px',
                padding: '15px',
                borderRadius: '8px',
                backgroundColor: gasLawError ? 'rgba(255, 107, 107, 0.1)' : 'rgba(76, 175, 80, 0.1)',
                border: `1px solid ${gasLawError ? '#ff6b6b' : 'var(--primary-color)'}`,
                textAlign: 'center'
              }}>
                {gasLawError ? (
                  <p style={{ color: '#ff6b6b', margin: 0, fontSize: '14px' }}>
                    ⚠️ {gasLawError}
                  </p>
                ) : (
                  <p style={{ color: 'var(--primary-color)', margin: 0, fontSize: '16px', fontWeight: 'bold' }}>
                    ✓ {gasLawResult}
                  </p>
                )}
              </div>
            )}
          </>
        )}

        {activeGasLaw === 'boyle' && (
          <>
            <div className="gas-law-equation">
              <p className="equation-main">P₁V₁ = P₂V₂</p>
              <p className="equation-description">Temperature and moles must remain constant</p>
            </div>

            <div className="gas-law-parameters"
            style={{
              gap: '15px',
              marginBottom: '15px',
              display: 'flex',
              justifyContent: 'space-between',
              width: '100%'
            }}>
              <div className="gas-law-row" style={{ gap: '15px' }}>
                <div className="gas-law-parameter">
                  <label>Pressure 1 (P₁):</label>
                  <div className="parameter-input-container">
                    <input
                      type="text"
                      value={pressure1 || ''}
                      onChange={(e) => {
                        const value = e.target.value;
                        setPressure1(value);
                        setLastBoyleField('p1');
                        setIsTypingBoyle(true);
                        if (clearGasLawResults) {
                          clearGasLawResults();
                        }
                      }}
                      onFocus={() => {
                        setPressure1('');
                      }}
                      onKeyDown={(e) => {
                        if (e.key === 'Enter') {
                          setIsTypingBoyle(false);
                          calculateBoyleLaw();
                        }
                      }}

                    />
                    <span className="parameter-unit">{pressureUnit}</span>
                  </div>
                </div>
                <div className="gas-law-parameter">
                  <label>Volume 1 (V₁):</label>
                  <div className="parameter-input-container">
                    <input
                      type="text"
                      value={volume1 || ''}
                      onChange={(e) => {
                        const value = e.target.value;
                        setVolume1(value);
                        setLastBoyleField('v1');
                        setIsTypingBoyle(true);
                        if (clearGasLawResults) {
                          clearGasLawResults();
                        }
                      }}
                      onFocus={() => {
                        setVolume1('');
                      }}
                      onKeyDown={(e) => {
                        if (e.key === 'Enter') {
                          setIsTypingBoyle(false);
                          calculateBoyleLaw();
                        }
                      }}

                    />
                    <span className="parameter-unit">{volumeUnit}</span>
                  </div>
                </div>
              </div>

              <div className="gas-law-row">
                <div className="gas-law-parameter">
                  <label>Pressure 2 (P₂):</label>
                  <div className="parameter-input-container">
                    <input
                      type="text"
                      value={pressure2 || ''}
                      onChange={(e) => {
                        const value = e.target.value;
                        setPressure2(value);
                        setLastBoyleField('p2');
                        setIsTypingBoyle(true);
                        if (clearGasLawResults) {
                          clearGasLawResults();
                        }
                      }}
                      onFocus={() => {
                        setPressure2('');
                      }}
                      onKeyDown={(e) => {
                        if (e.key === 'Enter') {
                          setIsTypingBoyle(false);
                          calculateBoyleLaw();
                        }
                      }}

                    />
                    <span className="parameter-unit">{pressureUnit}</span>
                  </div>
                </div>
                <div className="gas-law-parameter">
                  <label>Volume 2 (V₂):</label>
                  <div className="parameter-input-container">
                    <input
                      type="text"
                      value={volume2 || ''}
                      onChange={(e) => {
                        const value = e.target.value;
                        setVolume2(value);
                        setLastBoyleField('v2');
                        setIsTypingBoyle(true);
                        if (clearGasLawResults) {
                          clearGasLawResults();
                        }
                      }}
                      onFocus={() => {
                        setVolume2('');
                      }}
                      onKeyDown={(e) => {
                        if (e.key === 'Enter') {
                          setIsTypingBoyle(false);
                          calculateBoyleLaw();
                        }
                      }}

                    />
                    <span className="parameter-unit">{volumeUnit}</span>
                  </div>
                </div>
              </div>
            </div>

            <button
              className="calculate-button"
              onClick={() => {
                setIsTypingBoyle(false);
                calculateBoyleLaw();
              }}
              data-action="calculate"
              data-tooltip="Calculate (Alt+C)"
              style={{ padding: '8px 20px', fontSize: '14px' }}
            >
              Calculate
            </button>

            {/* Área de resultados */}
            {(gasLawResult || gasLawError) && (
              <div className="gas-law-results" style={{
                marginTop: '20px',
                padding: '15px',
                borderRadius: '8px',
                backgroundColor: gasLawError ? 'rgba(255, 107, 107, 0.1)' : 'rgba(76, 175, 80, 0.1)',
                border: `1px solid ${gasLawError ? '#ff6b6b' : 'var(--primary-color)'}`,
                textAlign: 'center'
              }}>
                {gasLawError ? (
                  <p style={{ color: '#ff6b6b', margin: 0, fontSize: '14px' }}>
                    ⚠️ {gasLawError}
                  </p>
                ) : (
                  <p style={{ color: 'var(--primary-color)', margin: 0, fontSize: '16px', fontWeight: 'bold' }}>
                    ✓ {gasLawResult}
                  </p>
                )}
              </div>
            )}
          </>
        )}

        {activeGasLaw === 'charles' && (
          <>
            <div className="gas-law-equation">
              <p className="equation-main">
                <span className="fraction">
                  <span className="numerator">V₁</span>
                  <span className="denominator">T₁</span>
                </span>
                =
                <span className="fraction">
                  <span className="numerator">V₂</span>
                  <span className="denominator">T₂</span>
                </span>
              </p>
              <p className="equation-description">Pressure and moles must remain constant</p>
            </div>

            <div className="gas-law-parameters"
            style={{
              gap: '15px',
              marginBottom: '15px',
              display: 'flex',
              justifyContent: 'space-between',
              width: '100%'
            }}>
              <div className="gas-law-row" style={{ gap: '15px' }}>
                <div className="gas-law-parameter">
                  <label>Volume 1 (V₁):</label>
                  <div className="parameter-input-container">
                    <input
                      type="text"
                      value={volume1Charles || ''}
                      onChange={(e) => {
                        const value = e.target.value;
                        setVolume1Charles(value);
                        setLastCharlesField('v1');
                        setIsTypingCharles(true);
                        if (clearGasLawResults) {
                          clearGasLawResults();
                        }
                      }}
                      onFocus={() => {
                        setVolume1Charles('');
                      }}
                      onKeyDown={(e) => {
                        if (e.key === 'Enter') {
                          setIsTypingCharles(false);
                          calculateCharlesLaw();
                        }
                      }}

                    />
                    <span className="parameter-unit">{volumeUnit}</span>
                  </div>
                </div>
                <div className="gas-law-parameter">
                  <label>Temperature 1 (T₁):</label>
                  <div className="parameter-input-container">
                    <input
                      type="text"
                      value={temperature1 || ''}
                      onChange={(e) => {
                        const value = e.target.value;
                        setTemperature1(value);
                        setLastCharlesField('t1');
                        setIsTypingCharles(true);
                        if (clearGasLawResults) {
                          clearGasLawResults();
                        }
                      }}
                      onFocus={() => {
                        setTemperature1('');
                      }}
                      onKeyDown={(e) => {
                        if (e.key === 'Enter') {
                          setIsTypingCharles(false);
                          calculateCharlesLaw();
                        }
                      }}

                    />
                    <span className="parameter-unit">{temperatureUnit}</span>
                  </div>
                </div>
              </div>

              <div className="gas-law-row">
                <div className="gas-law-parameter">
                  <label>Volume 2 (V₂):</label>
                  <div className="parameter-input-container">
                    <input
                      type="text"
                      value={volume2Charles || ''}
                      onChange={(e) => {
                        const value = e.target.value;
                        setVolume2Charles(value);
                        setLastCharlesField('v2');
                        setIsTypingCharles(true);
                        if (clearGasLawResults) {
                          clearGasLawResults();
                        }
                      }}
                      onFocus={() => {
                        setVolume2Charles('');
                      }}
                      onKeyDown={(e) => {
                        if (e.key === 'Enter') {
                          setIsTypingCharles(false);
                          calculateCharlesLaw();
                        }
                      }}

                    />
                    <span className="parameter-unit">{volumeUnit}</span>
                  </div>
                </div>
                <div className="gas-law-parameter">
                  <label>Temperature 2 (T₂):</label>
                  <div className="parameter-input-container">
                    <input
                      type="text"
                      value={temperature2 || ''}
                      onChange={(e) => {
                        const value = e.target.value;
                        setTemperature2(value);
                        setLastCharlesField('t2');
                        setIsTypingCharles(true);
                        if (clearGasLawResults) {
                          clearGasLawResults();
                        }
                      }}
                      onFocus={() => {
                        setTemperature2('');
                      }}
                      onKeyDown={(e) => {
                        if (e.key === 'Enter') {
                          setIsTypingCharles(false);
                          calculateCharlesLaw();
                        }
                      }}

                    />
                    <span className="parameter-unit">{temperatureUnit}</span>
                  </div>
                </div>
              </div>
            </div>

            <button
              className="calculate-button"
              onClick={() => {
                setIsTypingCharles(false);
                calculateCharlesLaw();
              }}
              data-action="calculate"
              data-tooltip="Calculate (Alt+C)"
              style={{ padding: '8px 20px', fontSize: '14px' }}
            >
              Calculate
            </button>

            {/* Área de resultados */}
            {(gasLawResult || gasLawError) && (
              <div className="gas-law-results" style={{
                marginTop: '20px',
                padding: '15px',
                borderRadius: '8px',
                backgroundColor: gasLawError ? 'rgba(255, 107, 107, 0.1)' : 'rgba(76, 175, 80, 0.1)',
                border: `1px solid ${gasLawError ? '#ff6b6b' : 'var(--primary-color)'}`,
                textAlign: 'center'
              }}>
                {gasLawError ? (
                  <p style={{ color: '#ff6b6b', margin: 0, fontSize: '14px' }}>
                    ⚠️ {gasLawError}
                  </p>
                ) : (
                  <p style={{ color: 'var(--primary-color)', margin: 0, fontSize: '16px', fontWeight: 'bold' }}>
                    ✓ {gasLawResult}
                  </p>
                )}
              </div>
            )}
          </>
        )}
      </div>
    </div>
    </div>
  );
};

GasLaws.defaultProps = {
  quantityValue: '',
  volumeValue: '',
  pressure: '',
  temperature: '',
  pressure1: '',
  volume1: '',
  pressure2: '',
  volume2: '',
  volume1Charles: '',
  temperature1: '',
  volume2Charles: '',
  temperature2: '',
  quantityUnit: 'mol',
  volumeUnit: 'L',
  pressureUnit: 'atm',
  temperatureUnit: 'K',
  selectedPreset: 'custom',
  gasLawResult: '',
  gasLawError: '',
  clearGasLawResults: () => {}, // Função vazia por padrão
  getPresetButtonText: null, // Permite usar a função padrão se não for fornecida
  handlePresetChange: () => {}, // Função vazia por padrão
  checkForPresetMatch: () => {} // Função vazia por padrão
};

export default GasLaws;
