import React, { useState, useEffect } from 'react';
import Select from 'react-select';
import { components } from 'react-select';
// Estilos personalizados para os selects de conversão
const conversionSelectStyles = {
  // Controle principal do select (a caixa de seleção)
  control: (provided, state) => ({
    ...provided,
    backgroundColor: 'rgba(0, 0, 0, 0.8)', // Fundo mais escuro (preto)
    border: 'none',
    borderRadius: 0,
    minHeight: '50px',
    height: '50px',
    boxShadow: 'none',
    cursor: 'pointer',
    width: '100%', // Garante que ocupe toda a largura disponível
    display: 'flex',
    '&:hover': {
      backgroundColor: 'rgba(0, 0, 0, 0.9)'
    }
  }),

  // Menu dropdown
  menu: (provided) => ({
    ...provided,
    backgroundColor: '#000000', // Fundo preto
    border: '1px solid #4CAF50',
    borderTop: 'none',
    borderRadius: '0 0 4px 4px',
    marginTop: '-1px',
    boxShadow: '0 4px 6px rgba(0, 0, 0, 0.1)',
    zIndex: 9999999, // Aumentado para garantir que fique acima de todos os elementos
    width: '100%', // Mesma largura do select
    position: 'absolute', // Alterado para absolute para garantir posicionamento correto
  }),

  // Configurações do portal do menu (quando renderizado no document.body)
  menuPortal: (provided) => ({
    ...provided,
    zIndex: 9999999 // Aumentado para garantir que fique acima de todos os elementos
  }),

  // Lista de opções
  menuList: (provided) => ({
    ...provided,
    padding: '0',
    maxHeight: '200px',
    whiteSpace: 'nowrap', // Evita quebra de linha
  }),

  // Cada opção individual no menu dropdown
  option: (provided, state) => ({
    ...provided,
    padding: '10px 15px',
    cursor: 'pointer',
    backgroundColor: state.isSelected ? '#4CAF50' :
                     state.isFocused ? 'rgba(76, 175, 80, 0.2)' : 'transparent',
    color: 'white',
    transition: 'background-color 0.2s ease',
    fontSize: '14px',
    fontWeight: '500',
    letterSpacing: '0.5px',
    textAlign: 'left',
    fontFamily: '-apple-system, BlinkMacSystemFont, "Segoe UI", Roboto, Oxygen, Ubuntu, Cantarell, "Fira Sans", "Droid Sans", "Helvetica Neue", sans-serif',
    borderBottom: '1px solid #222',
    whiteSpace: 'nowrap', // Evita quebra de linha
    overflow: 'visible', // Evita que o texto seja cortado
    textOverflow: 'clip', // Evita que o texto seja substituído por "..."
  }),

  // Container do select
  container: (provided) => ({
    ...provided,
    width: '100%', // Garante que ocupe toda a largura disponível
    position: 'relative',
  }),

  // Valor selecionado
  singleValue: (provided) => ({
    ...provided,
    color: 'white',
    fontSize: '14px',
    fontWeight: 'normal',
    textAlign: 'left',
    marginLeft: '0',
    paddingLeft: '0',
  }),

  // Indicador de dropdown
  dropdownIndicator: (provided) => ({
    ...provided,
    color: 'var(--primary-color)',
    padding: '0 8px',
  }),

  // Separador do indicador
  indicatorSeparator: () => ({
    display: 'none',
  }),

  // Container de valor
  valueContainer: (provided) => ({
    ...provided,
    padding: '0 15px',
  }),
};

// Componente personalizado para o indicador de dropdown
const CustomDropdownIndicator = (props) => {
  return (
    <components.DropdownIndicator {...props}>
      <span
        style={{
          color: 'white',
          fontSize: '12px',
          opacity: 0.7,
          transform: props.selectProps.menuIsOpen ? 'rotate(180deg)' : 'rotate(0deg)',
          transition: 'transform 0.2s ease',
          display: 'inline-block'
        }}
      >
        ▼
      </span>
    </components.DropdownIndicator>
  );
};

// Componente personalizado para as opções do dropdown
const CustomConversionOption = (props) => {
  const { children, ...rest } = props;

  return (
    <components.Option
      {...rest}
      className="custom-option"
    >
      <div style={{
        fontSize: '14px',
        fontWeight: '500',
        padding: '8px 12px',
        textAlign: 'left',
        whiteSpace: 'nowrap',
        color: 'white',
        display: 'block',
        width: '100%'
      }}>
        {children}
      </div>
    </components.Option>
  );
};

// Componente personalizado para o valor selecionado
const CustomSingleValue = (props) => {
  const { children, ...rest } = props;

  return (
    <components.SingleValue
      {...rest}
      className="custom-single-value"
    >
      <div style={{
        fontSize: '18px',
        fontWeight: '600',
        textAlign: 'center',
        whiteSpace: 'nowrap',
        color: 'white',
        display: 'flex',
        justifyContent: 'flex-start',
        alignItems: 'center',
        width: '100%',
        height: '100%',
        padding: 0
      }}>
        {children}
      </div>
    </components.SingleValue>
  );
};

// Componente personalizado para o menu dropdown
const CustomMenu = (props) => {
  return (
    <components.Menu
      {...props}
      className="custom-menu"
      style={{
        ...props.style,
        backgroundColor: '#000000',
        border: '1px solid rgba(76, 175, 80, 0.5)',
        borderRadius: '0 0 4px 4px',
        marginTop: '-1px',
        zIndex: 9999999,
        position: 'absolute',
        width: '100%',
        boxSizing: 'border-box',
        left: '0',
        right: 'auto'
      }}
    />
  );
};

const Conversions = ({
  unitOptions,
  fromUnit,
  setFromUnit,
  toUnit,
  setToUnit,
  inputValue,
  handleInputChange,
  result,
  error,
  isLoading,
  handleConvert,
  handleSwapUnits,
  isRotating,
  setInputValue,
  setUnitUpdateTrigger,
  getSpecificUnit,
  setMassValue,
  setVolumeValue,
  setQuantityValue,
  massUnit,
  volumeUnit,
  quantityUnit,
  massValue,
  volumeValue,
  quantityValue
}) => {
  const [isUpdatingFromUnitChange, setIsUpdatingFromUnitChange] = useState(false);

  // Sincronizar inputValue com os valores dos parâmetros quando as unidades mudam
  useEffect(() => {
    if (isUpdatingFromUnitChange) {
      let currentValue = '';
      if (fromUnit === 'mass' && massValue) {
        currentValue = massValue;
      } else if (fromUnit === 'moles' && quantityValue) {
        currentValue = quantityValue;
      } else if (fromUnit === 'volume' && volumeValue) {
        currentValue = volumeValue;
      }

      if (currentValue && currentValue !== inputValue) {
        setInputValue(currentValue);
        setIsUpdatingFromUnitChange(false);
      }
    }
  }, [massValue, quantityValue, volumeValue, fromUnit, inputValue, setInputValue, isUpdatingFromUnitChange]);

  // Função para realizar a conversão e atualizar os parâmetros automaticamente
  const updateConversion = (value) => {
    // Garantir que o valor seja um número
    const numValue = typeof value === 'string' ? parseFloat(value) : value;

    // Verificar se o valor é válido
    if (isNaN(numValue)) {
      return;
    }

    // Formatar o valor para exibição (sem ponto decimal no final)
    const formattedValue = numValue.toString();

    // Atualizar o valor de entrada para garantir consistência
    setInputValue(formattedValue);

    // Atualizar os valores nos parâmetros
    if (fromUnit === 'mass') {
      setMassValue(formattedValue);
    } else if (fromUnit === 'volume') {
      setVolumeValue(formattedValue);
    } else if (fromUnit === 'moles') {
      setQuantityValue(formattedValue);
    }
    handleConvert();
  };

  return (
    <div style={{ width: '100%', display: 'flex', flexDirection: 'column', alignItems: 'center', position: 'static' }}>
      <div className="conversion-container operation-container" style={{
        display: 'flex',
        flexDirection: 'row',
        justifyContent: 'space-between',
        alignItems: 'center',
        width: '100%',
        padding: '20px',
        backgroundColor: 'rgba(255, 255, 255, 0.15)', // Fundo mais claro (mais branco)
        borderRadius: '8px',
        border: '1px solid var(--primary-color)',
        boxShadow: '0 4px 8px rgba(0, 0, 0, 0.2)'
      }}>
        <div className="conversion-box" style={{
          width: '44%', // Aumentado para dar mais espaço
          backgroundColor: 'rgba(0, 0, 0, 0.8)', // Fundo mais escuro (preto)
          borderRadius: '6px',
          border: '1px solid rgba(76, 175, 80, 0.5)',
          padding: '15px 15px 15px 15px', /* Ajustado para alinhar melhor */
          overflow: 'hidden',
          boxShadow: '0 2px 4px rgba(0, 0, 0, 0.1)'
        }}>
        <div className="conversion-header" style={{
          width: 'calc(100% - 4px)',
          height: '50px',
          display: 'flex',
          padding: 0,
          margin: '0px 0px 0px',
          overflow: 'visible',
          position: 'relative',
          borderTopLeftRadius: '4px',
          borderTopRightRadius: '4px',
          border: '1px solid rgba(76, 175, 80, 0.5)',
          marginBottom: '0',
          backgroundColor: 'rgba(0, 0, 0, 0.8)',
          zIndex: 1, /* Garante que o select fique acima de outros elementos */
          left: '1px'
        }}>
          <div style={{ width: '100%', height: '100%', position: 'relative' }}>
            <Select
              value={unitOptions.conversion.find(option => option.value === fromUnit)}
              onChange={(option) => {
                // Define a nova unidade
                const newUnit = option.value;
                setFromUnit(newUnit);

                // Força a atualização da interface
                setUnitUpdateTrigger(prev => prev + 1);

                // Se houver um valor de entrada, converte-o para a nova unidade
                if (inputValue) {
                  const value = parseFloat(inputValue);
                  if (!isNaN(value)) {
                    // Atualiza os valores e realiza a conversão automaticamente
                    updateConversion(inputValue);
                  }
                }
              }}
              options={unitOptions.conversion}
              classNamePrefix="react-select"
              className="react-select-container conversion-select"
              id="conversion-from-select"
              styles={{
                ...conversionSelectStyles,
                control: (base, state) => ({
                  ...base,
                  backgroundColor: 'rgba(0, 0, 0, 0.8)',
                  border: 'none',
                  borderRadius: 0,
                  minHeight: '50px',
                  height: '50px',
                  boxShadow: 'none',
                  cursor: 'pointer',
                  width: '100%',
                  display: 'flex',
                  transition: 'none'
                }),
                menu: (base) => ({
                  ...base,
                  backgroundColor: '#000000',
                  border: '1px solid #4CAF50',
                  borderTop: 'none',
                  borderRadius: '0 0 4px 4px',
                  marginTop: '0px',
                  boxShadow: '0 4px 6px rgba(0, 0, 0, 0.1)',
                  zIndex: 99999,
                  width: '100%',
                  position: 'absolute',
                  boxSizing: 'border-box'
                }),
                container: (base) => ({
                  ...base,
                  width: '100%',
                  transition: 'none'
                }),
                valueContainer: (base) => ({
                  ...base,
                  padding: '0 15px',
                  transition: 'none'
                }),
                singleValue: (base) => ({
                  ...base,
                  color: 'white',
                  fontSize: '14px',
                  fontWeight: 'normal',
                  textAlign: 'left',
                  marginLeft: '0',
                  paddingLeft: '0',
                  transition: 'none'
                }),
                dropdownIndicator: (base) => ({
                  ...base,
                  color: 'var(--primary-color)',
                  padding: '0 8px',
                  transition: 'none'
                })
              }}
              components={{
                DropdownIndicator: CustomDropdownIndicator,
                Option: CustomConversionOption,
                SingleValue: CustomSingleValue,
                Menu: CustomMenu,
                IndicatorSeparator: () => null
              }}
              isSearchable={false}
              menuPlacement="bottom"
            />
          </div>
        </div>
        <div className="input-with-unit">
          <div className="conversion-input-container" style={{ position: 'relative', width: 'calc(100% - 0px)', margin: '0px 0px', padding: '0px' }}>
            <textarea
              value={inputValue}
              onChange={(e) => {
                // Atualizar o valor de entrada
                handleInputChange(e);

                // Verificar se o campo está vazio
                if (!e.target.value.trim()) {
                  // Se o campo estiver vazio, limpar os valores e o resultado
                  if (fromUnit === 'mass') {
                    setMassValue('');
                  } else if (fromUnit === 'moles') {
                    setQuantityValue('');
                  } else if (fromUnit === 'volume') {
                    setVolumeValue('');
                  }

                  return;
                }

                // Só atualizar os valores se não estivermos no meio de uma atualização de unidade
                if (!isUpdatingFromUnitChange) {
                  // Extrair o valor numérico, ignorando qualquer texto (como unidades)
                  const numericValue = parseFloat(e.target.value);

                  // Verificar se o valor é um número válido
                  if (!isNaN(numericValue)) {
                    console.log(`Valor extraído e convertido para número: ${numericValue}`);

                    // Forçar uma nova conversão com o valor numérico
                    if (fromUnit === 'mass') {
                      setMassValue(numericValue.toString());
                    } else if (fromUnit === 'moles') {
                      setQuantityValue(numericValue.toString());
                    } else if (fromUnit === 'volume') {
                      setVolumeValue(numericValue.toString());
                    }

                    // Executar a conversão imediatamente
                    handleConvert();
                  }
                }
              }}
              placeholder="Enter value to convert"
              className="conversion-input"
              style={{
                textAlign: 'center',
                background: 'rgba(0, 0, 0, 0.8)',
                borderWidth: '1px',
                borderStyle: 'solid',
                borderColor: 'rgba(76, 175, 80, 0.5)',
                borderTopWidth: '0px',
                borderBottomLeftRadius: '4px',
                borderBottomRightRadius: '4px',
                padding: '15px',
                color: 'white',
                fontSize: '16px',
                fontFamily: 'monospace',
                width: 'calc(100% - 2px)',
                boxSizing: 'border-box',
                height: '100px',
                margin: '0px 1px',
                outline: 'none',
                boxShadow: 'none'
              }}
              onFocus={() => {
                // Limpar o campo quando o usuário clica nele
                setInputValue('');
              }}
            />
            {inputValue && (
              <span style={{
                position: 'absolute',
                display: 'inline-block',
                color: 'var(--text-color)',
                pointerEvents: 'none',
                fontSize: '16px',
                marginLeft: '5px',
                opacity: '0.9',
                right: '15px',
                top: '50%',
                transform: 'translateY(-50%)',
                fontWeight: '500',
                fontFamily: 'monospace'
              }}>
                {fromUnit === 'moles' ? quantityUnit : fromUnit === 'mass' ? massUnit : fromUnit === 'volume' ? volumeUnit : fromUnit}
              </span>
            )}
          </div>
        </div>
      </div>

      <div className="conversion-controls" style={{ width: '10%', display: 'flex', flexDirection: 'column', justifyContent: 'center', alignItems: 'center' }}>
        <button
          className={`swap-button ${isRotating ? 'rotating' : ''}`}
          onClick={handleSwapUnits}
          title="Swap units"
          data-action="swap"
          data-tooltip="Swap units (Alt+S)"
          style={{
            width: '36px',
            height: '36px',
            borderRadius: '50%',
            backgroundColor: 'rgba(0, 0, 0, 0.8)',
            border: '1px solid rgba(76, 175, 80, 0.5)',
            color: 'white',
            display: 'flex',
            alignItems: 'center',
            justifyContent: 'center',
            cursor: 'pointer',
            fontSize: '18px',
            padding: 0,
            transition: 'all 0.3s ease'
          }}
        >
          ⇄
        </button>
        {result && (
          <button
            className="copy-button"
            onClick={() => {
              navigator.clipboard.writeText(result);
            }}
            title="Copy result"
            data-action="copy"
            data-tooltip="Copy result to clipboard"
            style={{
              width: '36px',
              height: '36px',
              borderRadius: '50%',
              backgroundColor: 'rgba(0, 0, 0, 0.8)',
              border: '1px solid rgba(76, 175, 80, 0.5)',
              color: 'white',
              display: 'flex',
              alignItems: 'center',
              justifyContent: 'center',
              cursor: 'pointer',
              fontSize: '16px',
              padding: 0,
              marginTop: '10px',
              transition: 'all 0.3s ease'
            }}
          >
            📋
          </button>
        )}
      </div>

      <div className="conversion-box" style={{
          width: '44%', // Aumentado para dar mais espaço
          backgroundColor: 'rgba(0, 0, 0, 0.8)', // Fundo mais escuro (preto)
          borderRadius: '6px',
          border: '1px solid rgba(76, 175, 80, 0.5)',
          padding: '15px 15px 15px 15px', /* Ajustado para alinhar melhor */
          overflow: 'hidden',
          boxShadow: '0 2px 4px rgba(0, 0, 0, 0.1)'
        }}>
        <div className="conversion-header" style={{
          width: 'calc(100% - 4px)',
          height: '50px',
          display: 'flex',
          padding: '0px',
          margin: '0px 0px 0px',
          overflow: 'visible',
          position: 'relative',
          borderTopLeftRadius: '4px',
          borderTopRightRadius: '4px',
          border: '1px solid rgba(76, 175, 80, 0.5)',
          backgroundColor: 'rgba(0, 0, 0, 0.8)',
          zIndex: 1, /* Garante que o select fique acima de outros elementos */
          left: '1px'
        }}>
          <div style={{ width: '100%', height: '100%', position: 'relative' }}>
            <Select
              value={unitOptions.conversion.find(option => option.value === toUnit)}
              onChange={(option) => {
                // Define a nova unidade
                const newUnit = option.value;
                setToUnit(newUnit);

                // Força a atualização da interface
                setUnitUpdateTrigger(prev => prev + 1);

                // Se houver um valor de entrada, converte-o para a nova unidade
                if (inputValue) {
                  const value = parseFloat(inputValue);
                  if (!isNaN(value)) {
                    // Atualiza os valores e realiza a conversão automaticamente
                    updateConversion(inputValue);
                  }
                }
              }}
              options={unitOptions.conversion}
              classNamePrefix="react-select"
              className="react-select-container conversion-select"
              id="conversion-to-select"
              styles={{
                ...conversionSelectStyles,
                control: (base, state) => ({
                  ...base,
                  backgroundColor: 'rgba(0, 0, 0, 0.8)',
                  border: 'none',
                  borderRadius: 0,
                  minHeight: '50px',
                  height: '50px',
                  boxShadow: 'none',
                  cursor: 'pointer',
                  width: '100%',
                  display: 'flex',
                  transition: 'none'
                }),
                menu: (base) => ({
                  ...base,
                  backgroundColor: '#000000',
                  border: '1px solid #4CAF50',
                  borderTop: 'none',
                  borderRadius: '0 0 4px 4px',
                  marginTop: '0px',
                  boxShadow: '0 4px 6px rgba(0, 0, 0, 0.1)',
                  zIndex: 99999,
                  width: '100%',
                  position: 'absolute',
                  boxSizing: 'border-box'
                }),
                container: (base) => ({
                  ...base,
                  width: '100%',
                  transition: 'none'
                }),
                valueContainer: (base) => ({
                  ...base,
                  padding: '0 15px',
                  transition: 'none'
                }),
                singleValue: (base) => ({
                  ...base,
                  color: 'white',
                  fontSize: '14px',
                  fontWeight: 'normal',
                  textAlign: 'left',
                  marginLeft: '0',
                  paddingLeft: '0',
                  transition: 'none'
                }),
                dropdownIndicator: (base) => ({
                  ...base,
                  color: 'var(--primary-color)',
                  padding: '0 8px',
                  transition: 'none'
                })
              }}
              components={{
                DropdownIndicator: CustomDropdownIndicator,
                Option: CustomConversionOption,
                SingleValue: CustomSingleValue,
                Menu: CustomMenu,
                IndicatorSeparator: () => null
              }}
              isSearchable={false}
              menuPlacement="bottom"
            />
          </div>
        </div>
        <div className="input-with-unit">
          <div className="conversion-input-container" style={{ position: 'relative', width: 'calc(100% - 0px)', margin: '0px 0px', padding: '0px' }}>
            <textarea
              readOnly
              className="conversion-input"
              style={{
                textAlign: 'center',
                background: 'rgba(0, 0, 0, 0.8)',
                borderWidth: '1px',
                borderStyle: 'solid',
                borderColor: 'rgba(76, 175, 80, 0.5)',
                borderTopWidth: '0px',
                borderBottomLeftRadius: '4px',
                borderBottomRightRadius: '4px',
                padding: '15px',
                color: 'white',
                fontSize: '16px',
                fontFamily: 'monospace',
                width: 'calc(100% - 2px)',
                boxSizing: 'border-box',
                height: '100px',
                margin: '0px 1px',
                outline: 'none',
                boxShadow: 'none'
              }}
              value={isLoading ? 'Converting...' : error ? error : result ? result : ''}
            />
            {result && !error && !isLoading && (
              <span style={{
                position: 'absolute',
                display: 'inline-block',
                color: 'var(--text-color)',
                pointerEvents: 'none',
                fontSize: '16px',
                marginLeft: '5px',
                opacity: '0.9',
                right: '15px',
                top: '50%',
                transform: 'translateY(-50%)',
                fontWeight: '500',
                fontFamily: 'monospace'
              }}>
                {toUnit === 'moles' ? quantityUnit : toUnit === 'mass' ? massUnit : toUnit === 'volume' ? volumeUnit : toUnit}
              </span>
            )}
          </div>
        </div>
      </div>
    </div>
    </div>
  );
};

export default Conversions;
