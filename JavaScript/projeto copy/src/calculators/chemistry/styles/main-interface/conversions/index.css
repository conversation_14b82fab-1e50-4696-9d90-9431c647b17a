/**
 * Estilos para o componente de conversão
 */

@import './conversion-select.css';
@import './invert-button.css';
@import './conversion-styles.css';

/* Container principal de conversão */
.conversion-container {
  width: 100%;
  display: flex;
  flex-direction: column;
  align-items: center;
}

/* Cabeçalho de conversão com selects */
.conversion-header {
  width: 100%;
  display: flex;
  align-items: center;
  justify-content: space-between;
  margin-bottom: 15px;
  background-color: rgba(0, 0, 0, 0.3);
  border-radius: 10px 10px 0 0;
  border: 2px solid var(--border-color);
  border-bottom: none;
  overflow: hidden;
}

/* Container para os inputs e o botão de inverter */
.conversion-inputs {
  width: 100%;
  display: flex;
  align-items: center;
  justify-content: space-between;
  margin-bottom: 15px;
  background-color: rgba(0, 0, 0, 0.3);
  border-radius: 0 0 10px 10px;
  border: 2px solid var(--border-color);
  border-top: none;
  padding: 15px;
}

/* Estilo para os inputs de conversão */
.conversion-input {
  width: 45%;
  height: 35px;
  padding: 0 15px;
  border-radius: 8px;
  border: 1px solid var(--border-color);
  background: rgba(255, 255, 255, 0.05);
  color: white;
  font-size: 14px;
  font-weight: 500;
  transition: all 0.3s ease;
  text-align: center;
}

.conversion-input:focus {
  outline: none;
  border-color: var(--primary-color);
  box-shadow: 0 0 10px rgba(76, 175, 80, 0.5);
}

/* Estilo para o container de cada select */
.conversion-select-container {
  width: 45%;
  position: relative;
}

/* Estilo para o label de unidade */
.unit-label {
  position: absolute;
  right: 10px;
  top: 50%;
  transform: translateY(-50%);
  color: rgba(255, 255, 255, 0.7);
  font-size: 12px;
  pointer-events: none;
}

/* Responsividade */
@media (max-width: 768px) {
  .conversion-inputs {
    flex-direction: column;
    padding: 10px;
  }

  .conversion-input {
    width: 100%;
    margin-bottom: 10px;
  }

  .invert-button {
    margin: 10px 0;
  }

  .conversion-select-container {
    width: 100%;
  }
}
