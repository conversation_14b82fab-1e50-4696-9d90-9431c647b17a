/**
 * Estilos para a interface de conversões
 */

/* Estilos para o container de conversões */
.conversion-box:focus-within {
  border-color: var(--primary-color);
  box-shadow: 0 0 15px rgba(76, 175, 80, 0.5);
}

.conversion-header {
  display: flex;
  align-items: center;
  border-bottom: 1px solid var(--border-color);
}

.conversion-input {
  width: 100%;
  flex: 1; /* Faz o input crescer para preencher o espaço */
  min-height: 150px; /* Altura mínima do input */
  height: 150px; /* Altura fixa para garantir consistência */
  padding: 0px 0 0 0; /* Ajuste para centralizar verticalmente */
  background: transparent;
  border: none;
  color: white;
  font-size: 18px;
  resize: none;
  display: block;
  text-align: center;
  font-family: monospace; /* Fonte monoespaçada para melhor alinhamento */
  outline: none;
  box-sizing: border-box;
}

.result-value {
  font-size: 18px;
  text-align: center;
  animation: fadeIn 0.3s ease;
}

.loading-indicator,
.error-message {
  height: 100%;
  display: flex;
  align-items: center;
  justify-content: center;
}

/* Animação de rotação para o botão de troca */
@keyframes rotate180 {
  0% { transform: rotate(0deg); }
  100% { transform: rotate(180deg); }
}

.swap-button.rotating {
  animation: rotate180 0.5s cubic-bezier(0.34, 1.56, 0.64, 1);
}

/* Animações */
@keyframes fadeIn {
  from { opacity: 0; transform: translateY(10px); }
  to { opacity: 1; transform: translateY(0); }
}

/* Animação de rotação para o botão de troca */
@keyframes rotate180 {
  0% { transform: rotate(0deg); }
  100% { transform: rotate(180deg); }
}

.swap-button.rotating {
  animation: rotate180 0.5s cubic-bezier(0.34, 1.56, 0.64, 1);
}

/* Botões de limpar e copiar */
.clear-button,
.copy-button {
  padding: 8px;
  background: transparent;
  border: none;
  color: var(--text-color);
  cursor: pointer;
  opacity: 0.7;
  transition: opacity 0.3s;
}

.clear-button:hover,
.copy-button:hover {
  opacity: 1;
}

/* Painel de conversão - Regras Base */
.calculator-block .conversion-panel {
  background: rgba(0, 0, 0, 0.2);
  border-radius: 12px;
  border: 2px solid var(--border-color);
  overflow: visible; /* Permitir que dropdowns apareçam */
  display: flex;
  flex-direction: column;
  height: 100%;
  transition: all 0.3s ease;
  padding: 0; /* Remover padding do painel */
}

/* Estado de foco do painel mantido */
.calculator-block .conversion-panel:focus-within {
  border-color: var(--primary-color);
  box-shadow: 0 0 20px rgba(76, 175, 80, 0.5);
}

/* Definição única para o input de conversão */
.conversion-panel .conversion-input {
  width: 100%;                /* Ocupa toda a largura disponível */
  height: 150px;              /* ALTURA DA CAIXA DE TEXTO - AJUSTE ESTE VALOR */
  padding: 20px;              /* Espaçamento interno */
  background: transparent;    /* Fundo transparente */
  border: none;               /* Sem borda */
  color: white;               /* Cor do texto */
  font-size: 18px;            /* Tamanho da fonte */
  resize: none;               /* Impede redimensionamento pelo usuário */
  outline: none;              /* Remove contorno ao focar */
  box-sizing: border-box;     /* Inclui padding e border na largura/altura total */
  text-align: center !important; /* Força o alinhamento central */
}

/* Estado de foco do painel */
.conversion-panel:focus-within {
  border-color: var(--primary-color);
  box-shadow: 0 0 20px rgba(76, 175, 80, 0.5);
}

/* Background sutil quando o input está focado */
.conversion-panel:focus-within .conversion-input {
  background: rgba(255, 255, 255, 0.05);
}

/* Remover todos os estilos de hover e focus do input */
.conversion-panel .conversion-input:hover,
.conversion-panel .conversion-input:focus {
  background: rgba(255, 255, 255, 0.05);
  border: none;
  outline: none;
  box-shadow: none;
}

/* Remover transformações */
.conversion-panel,
.conversion-panel:hover,
.conversion-panel:focus-within {
  transform: none;
}

/* Animação do resultado */
.conversion-result span {
  animation: fadeIn 0.3s ease;
  display: block;
}

/* Placeholder do resultado */
.conversion-result .placeholder {
  color: rgba(255, 255, 255, 0.5);
}

/* Remove hover effects */
.conversion-panel:hover {
  transform: none;
  border-color: var(--border-color);
  box-shadow: none;
}

/* Melhorar o feedback visual dos selects */
.conversion-select {
  transition: background-color 0.3s ease, border-color 0.3s ease;
}

.conversion-select:hover {
  background: rgba(0, 0, 0, 0.4);
}

/* Input dentro do painel - Regras Base */
.calculator-block .conversion-panel .conversion-input {
  width: 100%;
  height: 100%; /* Ajustado para ocupar 100% da altura disponível */
  padding: 20px;
  background: transparent;
  border: none;
  color: white;
  font-size: 18px;
  resize: none;
  outline: none;
  box-sizing: border-box; /* Garantir que padding não adicione tamanho extra */
  margin: 0; /* Remover qualquer margem */
  display: block; /* Garantir que ocupe todo o espaço */
}

/* Estados de foco */
.compound-text-input:focus,
.conversion-input:focus,
.unit-select:focus {
  outline: none;
  border-color: var(--primary-color);
  box-shadow: 0 0 15px rgba(76, 175, 80, 0.3);
}

/* Responsividade */
@media (max-width: 768px) {
  .conversions-tab {
    margin: 10px;
    padding: 15px;
  }

  .conversion-input,
  .conversion-result {
    height: 50px;
  }

  .unit-select {
    padding: 8px;
  }
}

/* Estilizar o textarea para melhor feedback visual */
.conversion-input {
  width: 100%;
  height: 150px;
  padding: 20px;
  background: transparent;
  border: none;
  color: white;
  font-size: 18px;
  resize: none;
  transition: background-color 0.3s ease;
}

.conversion-input:focus {
  background: rgba(255, 255, 255, 0.05);
}

/* Textarea (conversion-input) específico */
textarea.conversion-input {
  margin: 5px 0;
  padding: 10px;
  font-size: 16px;
  width: 100%;
  background-color: rgba(255, 255, 255, 0.05);
  border: 2px solid var(--border-color);
  color: var(--text-color);
  border-radius: 5px;
  box-sizing: border-box;
  transition: all 0.3s ease;
}

textarea.conversion-input:focus {
  outline: none;
  border-color: var(--primary-color);
  box-shadow: 0 0 15px rgba(76, 175, 80, 0.5);
}

/* Removendo estilos conflitantes */
textarea.conversion-input {
  margin: 0;
  border: none;
  border-radius: 0;
  background: transparent;
  outline: none;
}

.conversion-input:focus {
  outline: none;
  background: rgba(255, 255, 255, 0.05);
  box-shadow: none; /* Removendo box-shadow individual */
}
