/**
 * Estilos específicos para os selects da interface de conversões
 */

/* Estilos para o container do select */
.conversion-select {
  width: 100%;
  height: 100%;
}

/* Estilos para o controle do select */
.conversion-select .react-select__control,
.conversion-select div[class*="control"],
.conversion-select div[class*="Control"] {
  background-color: rgba(0, 0, 0, 0.8) !important;
  border: 1px solid transparent !important;
  border-radius: 4px 4px 0 0 !important;
  min-height: 50px !important;
  height: 50px !important;
  box-shadow: none !important;
  cursor: pointer !important;
  width: 100% !important;
  transition: border-color 0.2s ease !important;
}

.conversion-select .react-select__control:hover,
.conversion-select div[class*="control"]:hover,
.conversion-select div[class*="Control"]:hover {
  background-color: rgba(0, 0, 0, 0.9) !important;
}

.conversion-select div[class*="control--is-focused"],
.conversion-select div[class*="control--menu-is-open"],
.conversion-select div[class*="Control--is-focused"],
.conversion-select div[class*="Control--menu-is-open"] {
  box-shadow: none !important;
}

/* Estilos para o menu dropdown */
.conversion-select .react-select__menu,
.conversion-select div[class*="menu"],
.conversion-select div[class*="Menu"],
.conversion-select .css-26l3qy-menu,
.conversion-select .custom-menu {
  background-color: #000000 !important;
  border-radius: 4px 4px 4px 4px !important;
  box-shadow: 0 4px 6px rgba(0, 0, 0, 0.1) !important;
  z-index: 1001 !important;
  width: 100% !important;
  position: absolute !important;
  left: 0 !important;
  right: auto !important;
  box-sizing: border-box !important;
  margin-left: 0 !important;
  margin-right: 0 !important;
}

/* Estilos para a lista de opções */
.conversion-select .react-select__menu-list {
  padding: 0;
  max-height: 200px;
  white-space: nowrap;
}

/* Estilos para cada opção */
.conversion-select .react-select__option {
  padding: 10px 15px;
  cursor: pointer;
  color: white;
  transition: background-color 0.2s ease;
  font-size: 14px;
  font-weight: 500;
  letter-spacing: 0.5px;
  text-align: left;
  font-family: -apple-system, BlinkMacSystemFont, "Segoe UI", Roboto, Oxygen, Ubuntu, Cantarell, "Fira Sans", "Droid Sans", "Helvetica Neue", sans-serif;
  border-bottom: 1px solid #222;
  white-space: nowrap;
  overflow: visible;
  text-overflow: clip;
}

.conversion-select .react-select__option--is-selected {
  background-color: #4CAF50;
}

.conversion-select .react-select__option--is-focused:not(.react-select__option--is-selected) {
  background-color: rgba(76, 175, 80, 0.2);
}

/* Estilos para o valor selecionado */
.conversion-select .react-select__single-value {
  color: white;
  font-size: 14px;
  font-weight: normal;
  text-align: left;
  margin-left: 0;
  padding-left: 0;
}

/* Estilos para o indicador de dropdown */
.conversion-select .react-select__dropdown-indicator {
  color: var(--primary-color);
  padding: 0 8px;
}

/* Estilos para o container de valor */
.conversion-select .react-select__value-container {
  padding: 0 15px;
}

/* Remove o separador entre o valor e o indicador */
.conversion-select .react-select__indicator-separator {
  display: none;
}

/* Garantir que o menu dropdown apareça na frente de todos os elementos */
#react-select-menu-portal,
div[id^="react-select"][id$="-listbox"],
div[id^="react-select"][id$="-menu"] {
  z-index: 1002 !important;
  position: absolute !important;
  width: 100% !important;
  left: 0 !important;
  right: auto !important;
}

/* Remover qualquer transição que possa causar mudanças visuais */
.conversion-select * {
  transition: none !important;
}
