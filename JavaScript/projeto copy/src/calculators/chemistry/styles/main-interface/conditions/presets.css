/* Estilos para os botões de presets (STP/SATP) */
.presets-buttons {
  display: grid;
  grid-template-columns: 1fr 1fr;
  gap: 15px;
  margin-bottom: 20px;
  justify-content: center;
  width: 100%;
  max-width: 600px; /* Ajuste conforme necessário */
}

.preset-button {
  padding: 10px 20px;
  border-radius: 6px;
  border: 1px solid var(--border-color);
  background: rgba(255, 255, 255, 0.05);
  color: white;
  cursor: pointer;
  transition: all 0.3s ease;
  width: 100%;
  text-align: center;
  min-width: 200px; /* Largura mínima para os botões */
}

.preset-button:hover {
  background: rgba(255, 255, 255, 0.1);
  border-color: var(--primary-color);
}

.preset-button.active {
  background: var(--primary-color);
  border-color: var(--primary-color);
}

/* Responsividade */
@media (max-width: 768px) {
  .presets-buttons {
    flex-direction: column;
    align-items: stretch;
  }

  .preset-button {
    width: 100%;
    text-align: center;
  }
}
