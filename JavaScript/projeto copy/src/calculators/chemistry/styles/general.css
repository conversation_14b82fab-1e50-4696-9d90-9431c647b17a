/* =========================================================================
   INPUTS NUMÉRICOS
   ========================================================================= */
input[type="number"] {
  -moz-appearance: textfield;             /* Remove setas no Firefox */
  appearance: textfield;                  /* Remove setas em outros navegadores */
}

input[type="number"]::-webkit-outer-spin-button,
input[type="number"]::-webkit-inner-spin-button {
  -webkit-appearance: none;               /* Remove setas no Chrome/Safari */
  margin: 0;                              /* Remove margem */
}

/* =========================================================================
   BOTÕES DE AÇÃO
   ========================================================================= */
.clear-button,
.copy-button {
  padding: 8px;                           /* Espaçamento interno */
  background: transparent;                /* Fundo transparente */
  border: none;                           /* Sem borda */
  color: var(--text-color);               /* Cor do texto */
  cursor: pointer;                        /* Cursor de ponteiro */
  opacity: 0.7;                           /* Semi-transparente */
  transition: opacity 0.3s;               /* Transição suave */
}

.clear-button:hover,
.copy-button:hover {
  opacity: 1;                             /* Opacidade total no hover */
}

.swap-button,
.calculate-button {
  width: 100%;                            /* Largura total */
  margin-top: 15px;                       /* Margem superior */
}

button {
  margin: 0;                              /* Sem margem */
  padding: 10px 15px;                     /* Espaçamento interno */
  border: none;                           /* Sem borda */
  border-radius: 5px;                     /* Bordas arredondadas */
  background-color: var(--primary-color); /* Fundo verde */
  color: var(--text-color);               /* Cor do texto */
  cursor: pointer;                        /* Cursor de ponteiro */
  font-size: 16px;                        /* Tamanho da fonte */
  transition: all 0.3s ease;              /* Transição suave */
  box-shadow: 0 2px 5px rgba(0, 0, 0, 0.2); /* Sombra suave */
  pointer-events: auto;                   /* Permite eventos de ponteiro */
}

button:hover {
  background-color: var(--primary-dark);  /* Fundo verde escuro no hover */
  transform: translateY(-2px);            /* Efeito de elevação */
  box-shadow: 0 4px 8px rgba(0, 0, 0, 0.3); /* Sombra mais intensa */
}

/* =========================================================================
   RESULTADOS E TEXTOS
   ========================================================================= */
.result {
  width: 100%;                            /* Largura total */
  max-width: 400px;                       /* Largura máxima */
  text-align: center;                     /* Alinhamento centralizado */
  padding: 10px;                          /* Espaçamento interno */
  background-color: rgba(76, 175, 80, 0.1); /* Fundo verde transparente */
  border-radius: 5px;                     /* Bordas arredondadas */
  color: var(--text-color);               /* Cor do texto */
  font-size: 1.1em;                       /* Tamanho da fonte */
  margin: 0 auto;                         /* Centralização horizontal */
}

h2 {
  text-align: center;                     /* Alinhamento centralizado */
  margin-bottom: 20px;                    /* Margem inferior */
  color: var(--primary-color);            /* Cor verde */
  width: 100%;                            /* Largura total */
}

p {
  line-height: 1.6;                       /* Altura da linha */
  color: rgba(255, 255, 255, 0.9);        /* Cor do texto */
  pointer-events: auto;                   /* Permite eventos de ponteiro */
}

.info-text {
  font-size: 0.9rem;                      /* Tamanho da fonte */
  font-style: italic;                     /* Estilo itálico */
  color: rgba(255, 255, 255, 0.7);        /* Cor do texto */
  margin-top: 10px;                       /* Margem superior */
  pointer-events: auto;                   /* Permite eventos de ponteiro */
}

/* =========================================================================
   GRUPOS DE ENTRADA
   ========================================================================= */
.input-group {
  width: 100%;                            /* Largura total */
  display: flex;                          /* Layout flexbox */
  flex-direction: column;                 /* Direção de coluna */
  align-items: center;                    /* Alinhamento centralizado */
  gap: 15px;                              /* Espaçamento entre elementos */
}

.input-group input {
  width: 100%;                            /* Largura total */
  max-width: 400px;                       /* Largura máxima */
  padding: 10px;                          /* Espaçamento interno */
  border-radius: 5px;                     /* Bordas arredondadas */
  border: 1px solid var(--border-color);  /* Borda verde sutil */
  background: rgba(255, 255, 255, 0.05);  /* Fundo branco transparente */
  color: var(--text-color);               /* Cor do texto */
  text-align: center;                     /* Alinhamento centralizado */
}

/* =========================================================================
   ESTADOS DE FOCO
   ========================================================================= */
.calculator-block input:focus,
.calculo-container input:focus,
.conditions-panel input:focus,
.dilution-inputs input:focus {
  outline: none;                          /* Remove contorno padrão */
  border-color: var(--primary-color);     /* Borda verde */
  box-shadow: 0 0 15px rgba(76, 175, 80, 0.5); /* Brilho verde */
}

*:focus {
  outline: none;                          /* Remove contorno padrão */
}

input:focus,
textarea:focus,
select:focus,
button:focus {
  box-shadow: 0 0 0 2px var(--primary-color); /* Contorno verde */
}

/* =========================================================================
   BARRA DE ROLAGEM
   ========================================================================= */
::-webkit-scrollbar {
  width: 8px;                             /* Largura da barra */
}

::-webkit-scrollbar-track {
  background: transparent;                /* Fundo transparente */
}

::-webkit-scrollbar-thumb {
  background: rgba(76, 175, 80, 0.3);     /* Fundo verde transparente */
  border-radius: 4px;                     /* Bordas arredondadas */
}

::-webkit-scrollbar-thumb:hover {
  background: rgba(76, 175, 80, 0.5);     /* Fundo verde mais intenso no hover */
}

/* =========================================================================
   SELEÇÃO DE TEXTO
   ========================================================================= */
* {
  -webkit-tap-highlight-color: transparent; /* Remove destaque ao tocar */
  -webkit-touch-callout: none;            /* Remove menu de contexto ao tocar */
  -webkit-user-select: none;              /* Impede seleção no Safari */
  -khtml-user-select: none;               /* Impede seleção no Konqueror */
  -moz-user-select: none;                 /* Impede seleção no Firefox */
  -ms-user-select: none;                  /* Impede seleção no IE/Edge */
  user-select: none;                      /* Impede seleção em navegadores modernos */
}

input, textarea {
  -webkit-user-select: text;              /* Permite seleção no Safari */
  -khtml-user-select: text;               /* Permite seleção no Konqueror */
  -moz-user-select: text;                 /* Permite seleção no Firefox */
  -ms-user-select: text;                  /* Permite seleção no IE/Edge */
  user-select: text;                      /* Permite seleção em navegadores modernos */
}
