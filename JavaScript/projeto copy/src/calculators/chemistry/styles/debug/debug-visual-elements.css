/**
 * Debug visual elements styles
 */

/* Debug grid lines */
.debug-grid-line {
  position: fixed;
  background-color: rgba(255, 255, 255, 0.1);
  pointer-events: none;
  z-index: 9993;
}

.debug-grid-line.horizontal {
  width: 100%;
  height: 1px;
  left: 0;
}

.debug-grid-line.vertical {
  height: 100%;
  width: 1px;
  top: 0;
}

/* Center lines */
.center-line-x,
.center-line-y {
  position: fixed;
  background-color: rgba(255, 0, 0, 0.5);
  pointer-events: none;
  z-index: 9996;
}

.center-line-x {
  width: 100%;
  height: 1px;
  left: 0;
}

.center-line-y {
  height: 100%;
  width: 1px;
  top: 0;
}

/* Thirds lines */
.debug-thirds-line {
  position: fixed;
  pointer-events: none;
  z-index: 9996;
}

.debug-thirds-line.horizontal {
  width: 100%;
  height: 1px;
  left: 0;
  background-color: rgba(0, 0, 255, 0.5);
}

.debug-thirds-line.vertical {
  height: 100%;
  width: 1px;
  top: 0;
  background-color: rgba(0, 0, 255, 0.5);
}

/* Element outlines */
.debug-outline {
  position: absolute;
  border: 1px dashed rgba(255, 0, 0, 0.5);
  pointer-events: none;
  z-index: 9994;
  box-sizing: border-box;
}

/* Dimension labels */
.debug-dimension-label {
  position: absolute;
  background-color: rgba(0, 0, 0, 0.7);
  color: white;
  padding: 2px 5px;
  border-radius: 2px;
  font-size: 10px;
  font-family: monospace;
  pointer-events: none;
  z-index: 9995;
  white-space: nowrap;
}

/* Center point */
.center-point {
  position: absolute;
  width: 6px;
  height: 6px;
  background-color: red;
  border-radius: 50%;
  transform: translate(-50%, -50%);
  pointer-events: none;
  z-index: 9997;
}

/* Debug elements container */
#debug-elements-container {
  position: fixed;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  pointer-events: none;
  z-index: 9990;
  opacity: 0;
  transition: opacity 0.3s ease;
}

/* Debug mode active body class */
body.debug-mode-active .debug-outline {
  display: block;
}

body.debug-mode-active .debug-dimension-label {
  display: block;
}

/* Hide debug elements by default */
.debug-outline,
.debug-dimension-label,
.debug-grid-line,
.center-line-x,
.center-line-y,
.debug-thirds-line,
.center-point {
  display: none;
}

body.debug-mode-active .debug-outline,
body.debug-mode-active .debug-dimension-label,
body.debug-mode-active .debug-grid-line,
body.debug-mode-active .center-line-x,
body.debug-mode-active .center-line-y,
body.debug-mode-active .debug-thirds-line,
body.debug-mode-active .center-point {
  display: block;
}
