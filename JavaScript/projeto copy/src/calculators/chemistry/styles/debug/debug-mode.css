/**
 * Debug mode styles
 */

/* Debug panel */
.debug-panel {
  position: fixed;
  top: 20px;
  right: 20px;
  background-color: rgba(0, 0, 0, 0.8);
  border: 1px solid var(--primary-color);
  border-radius: 4px;
  padding: 10px;
  z-index: 9999;
  width: 300px;
  max-height: 80vh;
  overflow-y: auto;
  color: white;
  font-family: monospace;
  font-size: 12px;
  box-shadow: 0 0 10px rgba(0, 0, 0, 0.5);
  transition: all 0.3s ease;
}

.debug-panel-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 10px;
  padding-bottom: 5px;
  border-bottom: 1px solid var(--primary-color);
}

.debug-panel-title {
  font-weight: bold;
  color: var(--primary-color);
  margin: 0;
}

.debug-panel-close {
  background: none;
  border: none;
  color: white;
  cursor: pointer;
  font-size: 16px;
  padding: 0;
}

.debug-panel-section {
  margin-bottom: 10px;
  padding-bottom: 5px;
  border-bottom: 1px solid rgba(255, 255, 255, 0.1);
}

.debug-panel-section-title {
  font-weight: bold;
  margin-bottom: 5px;
  color: var(--primary-color);
}

/* Debug elements */
.debug-dimension-label {
  position: absolute;
  background-color: rgba(0, 0, 0, 0.7);
  color: white;
  padding: 2px 5px;
  border-radius: 2px;
  font-size: 10px;
  pointer-events: none;
  z-index: 9995;
}

.debug-outline {
  position: absolute;
  border: 1px dashed rgba(255, 0, 0, 0.5);
  pointer-events: none;
  z-index: 9994;
}

.debug-grid-line {
  position: fixed;
  background-color: rgba(255, 255, 255, 0.2);
  pointer-events: none;
  z-index: 9993;
}

.debug-center-line {
  position: fixed;
  background-color: rgba(255, 0, 0, 0.5);
  pointer-events: none;
  z-index: 9996;
}

.debug-thirds-line {
  position: fixed;
  pointer-events: none;
  z-index: 9996;
}

.center-point {
  position: absolute;
  width: 6px;
  height: 6px;
  background-color: red;
  border-radius: 50%;
  transform: translate(-50%, -50%);
  pointer-events: none;
  z-index: 9997;
}

/* Debug mode toggle */
.debug-mode-toggle {
  position: fixed;
  bottom: 20px;
  right: 20px;
  background-color: rgba(0, 0, 0, 0.8);
  color: white;
  border: 1px solid var(--primary-color);
  border-radius: 4px;
  padding: 5px 10px;
  font-size: 12px;
  cursor: pointer;
  z-index: 9999;
  transition: all 0.3s ease;
}

.debug-mode-toggle:hover {
  background-color: var(--primary-color);
}

/* Debug info */
.debug-info {
  margin-bottom: 5px;
}

.debug-info-label {
  font-weight: bold;
  margin-right: 5px;
  color: var(--primary-color);
}

.debug-info-value {
  font-family: monospace;
}

/* Debug checkboxes */
.debug-checkbox-container {
  display: flex;
  align-items: center;
  margin-bottom: 5px;
}

.debug-checkbox-container input[type="checkbox"] {
  margin-right: 5px;
}

/* Debug mode active body class */
body.debug-mode-active {
  /* Add any global styles for when debug mode is active */
}

/* Debug elements container */
#debug-elements-container {
  position: fixed;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  pointer-events: none;
  z-index: 9990;
  opacity: 0;
  transition: opacity 0.3s ease;
}
