/**
 * Estilos para os selects
 */

/* Estilo base para o select de unidades */
.unit-select {
  width: 100%;
  padding: 10px;
  padding-right: 30px;
  background-color: black !important;
  background-image: url("data:image/svg+xml;charset=UTF-8,%3csvg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 24 24' fill='white'%3e%3cpath d='M7 10l5 5 5-5z'/%3e%3c/svg%3e") !important;
  background-repeat: no-repeat !important;
  background-position: right 10px center !important;
  background-size: 20px !important;
  border: none; /* Removida a borda individual */
  color: white;
  font-size: 16px;
  cursor: pointer;
  -webkit-appearance: none;
  -moz-appearance: none;
  appearance: none;
}

.unit-select:focus {
  background-color: black !important;
  background-image: url("data:image/svg+xml;charset=UTF-8,%3csvg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 24 24' fill='white'%3e%3cpath d='M7 10l5 5 5-5z'/%3e%3c/svg%3e") !important;
  background-repeat: no-repeat !important;
  background-position: right 10px center !important;
  background-size: 20px !important;
  outline: none; /* Remove o outline padrão */
  box-shadow: none; /* Remove qualquer box-shadow individual */
}

/* Estilo para as options */
.unit-select option {
  background-color: black !important;
  color: white;
  padding: 12px;
}

/* Removendo a borda azul padrão do focus */
.unit-select:focus-visible {
  outline: none;
  border: none;
}

/* Removendo a seta padrão do select em navegadores específicos */
.unit-select::-ms-expand {
  display: none;
}

/* Estados de foco */
.unit-select:focus {
  outline: none;
  border-color: var(--primary-color);
  box-shadow: 0 0 15px rgba(76, 175, 80, 0.3);
}

/* Responsividade */
@media (max-width: 768px) {
  .unit-select {
    padding: 8px;
  }
}
