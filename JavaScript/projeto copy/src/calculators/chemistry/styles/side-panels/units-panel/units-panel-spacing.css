/* =========================================================================
   BLOCO DO PAINEL DE UNIDADES
   ========================================================================= */
.units-panel .panel-block {
  padding: 15px !important;                /* Espaçamento interno */
  margin-bottom: 20px !important;          /* Margem inferior */
  background-color: rgba(255, 255, 255, 0.1) !important; /* Fundo semi-transparente */
  border: 2px solid rgba(76, 175, 80, 0.5) !important; /* Borda verde */
  border-radius: 12px !important;          /* Bordas arredondadas */
  box-shadow: 0 4px 8px rgba(0, 0, 0, 0.2) !important; /* So<PERSON>ra suave */
  overflow: visible !important;            /* Permite que dropdowns apareçam fora */
}

.units-panel .panel-block:hover {
  border-color: var(--primary-color) !important; /* Borda verde mais forte no hover */
  box-shadow: 0 0 15px rgba(76, 175, 80, 0.3) !important; /* Sombra mais intensa */
  background-color: rgba(255, 255, 255, 0.15) !important; /* Fundo mais claro */
}

.units-panel .panel-block-title {
  color: var(--primary-color) !important;  /* Cor verde */
  font-size: 1.2rem !important;            /* Tamanho da fonte */
  margin-bottom: 10px !important;          /* Margem inferior */
  text-align: center !important;           /* Alinhamento centralizado */
  border-bottom: 1px solid rgba(76, 175, 80, 0.3) !important; /* Borda inferior */
  padding-bottom: 8px !important;          /* Espaçamento inferior */
}

/* =========================================================================
   ITENS DO PAINEL DE UNIDADES
   ========================================================================= */
.units-panel .parameter-item {
  display: flex !important;                /* Layout flexbox */
  justify-content: space-between !important; /* Espaço entre elementos */
  align-items: center !important;          /* Alinhamento vertical */
  padding: 4px 0 !important;               /* Espaçamento vertical */
  margin: 0 !important;                    /* Sem margem */
  border-bottom: 1px solid rgba(255, 255, 255, 0.1) !important; /* Separador */
  min-height: 12.5px !important;             /* Altura mínima consistente */
}

.units-panel .parameter-item:last-child {
  border-bottom: none !important;          /* Remove borda do último item */
}

.units-panel .parameter-item + .parameter-item {
  margin-top: 2px !important;              /* Espaçamento entre itens */
}

/* =========================================================================
   LABELS DO PAINEL DE UNIDADES
   ========================================================================= */
.units-panel .parameter-label {
  color: var(--primary-color) !important;  /* Cor verde */
  font-size: 10.505px !important;          /* Tamanho da fonte */
  font-weight: 500 !important;             /* Peso da fonte */
  white-space: nowrap !important;          /* Sem quebra de linha */
  width: 90px !important;                  /* Largura fixa */
  text-align: left !important;             /* Alinhamento à esquerda */
  padding-left: 10px !important;           /* Espaçamento à esquerda */
  display: flex !important;                /* Layout flexbox */
  align-items: center !important;          /* Alinhamento vertical */
  height: 12px !important;                 /* Altura fixa para alinhamento */
}

/* =========================================================================
   VALORES DO PAINEL DE UNIDADES
   ========================================================================= */
.units-panel .parameter-value {
  color: var(--text-color) !important;     /* Cor do texto */
  font-family: monospace !important;       /* Fonte monoespaçada */
  font-size: 10.505px !important;          /* Tamanho da fonte */
  margin-left: 0 !important;               /* Sem margem à esquerda */
  padding: 0 !important;                   /* Sem padding */
  display: flex !important;                /* Layout flexbox */
  align-items: center !important;          /* Alinhamento vertical */
  gap: 5px !important;                     /* Espaçamento entre elementos */
  justify-content: flex-end !important;    /* Alinhamento à direita */
  width: 120px !important;                 /* Largura fixa */
  position: relative !important;           /* Posicionamento relativo */
  height: 22px !important;                 /* Altura fixa para alinhamento */
}

.units-panel .parameter-value > div {
  width: 100% !important;                  /* Largura total */
  background-color: transparent !important; /* Fundo transparente */
  position: relative !important;           /* Posicionamento relativo */
}

/* =========================================================================
   CONFIGURAÇÕES DE Z-INDEX E OVERFLOW
   ========================================================================= */
.units-panel {
  z-index: 1002 !important;                /* Z-index elevado */
  overflow: visible !important;            /* Overflow visível */
}

.units-panel,
.units-panel .panel-block,
.units-panel .parameter-item,
.units-panel .parameter-value {
  overflow: visible !important;            /* Overflow visível em todos elementos */
}

.units-panel .parameter-value {
  position: relative !important;           /* Posicionamento relativo */
}
