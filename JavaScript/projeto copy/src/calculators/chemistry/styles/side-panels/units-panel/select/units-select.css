/* =========================================================================
   CONTAINER E ALINHAMENTO DO PAINEL DE UNIDADES
   ========================================================================= */
.units-panel .parameter-value {
  display: flex !important;      /* Layout flexbox */
  justify-content: flex-end !important; /* Alinhamento à direita */
  width: 120px !important;       /* Largura fixa */
  position: relative !important; /* Posicionamento relativo para dropdown */
  height: 12px !important;       /* Altura fixa para alinhamento */
}

.units-panel .parameter-value > div {
  width: 100% !important;        /* Largura total */
  margin-left: auto !important;  /* Margem automática à esquerda */
  position: relative !important; /* Posicionamento relativo */
}

/* =========================================================================
   CONTROLE DO SELECT
   ========================================================================= */
.units-select__control,
.units-panel div[class*="control"] {
  flex-direction: row-reverse !important; /* Direção reversa */
  justify-content: flex-end !important;   /* Alinhamento à direita */
  width: 100% !important;                 /* Largura total */
  background-color: transparent !important; /* Fundo transparente */
  border: 1px solid rgba(0, 0, 0, 0) !important; /* Borda transparente */
  min-height: 22px !important;            /* Altura mínima */
  height: 22px !important;                /* Altura fixa */
  box-shadow: none !important;            /* Sem sombra */
  transition: border-color 0.2s ease !important; /* Transição suave */
}

.units-select__control:hover,
.units-panel div[class*="control"]:hover {
  border-color: rgba(0, 0, 0, 0) !important; /* Borda transparente no hover */
}

.units-select__control--menu-is-open,
.units-panel div[class*="control"][class*="menuIsOpen"] {
  border: 1px solid rgba(255, 255, 255, 0.05) !important; /* Borda sutil */
  border-bottom: none !important;          /* Sem borda inferior */
  border-radius: 4px 4px 0 0 !important;   /* Bordas arredondadas no topo */
  background-color: #000000 !important;    /* Fundo preto */
}

/* =========================================================================
   CONTAINER DE VALOR E TEXTO SELECIONADO
   ========================================================================= */
.units-select__value-container {
  justify-content: flex-end !important;    /* Alinhamento à direita */
  padding: 0 0px !important;               /* Sem padding */
  flex-direction: row-reverse !important;  /* Direção reversa */
  height: 22px !important;                 /* Altura fixa */
  display: flex !important;                /* Layout flexbox */
  align-items: center !important;          /* Centralização vertical */
}

.units-select__single-value {
  color: white !important;                 /* Cor do texto */
  font-size: 10.505px !important;          /* Tamanho da fonte */
  font-family: monospace !important;       /* Fonte monoespaçada */
  text-align: right !important;            /* Alinhamento à direita */
  margin-left: 0 !important;               /* Sem margem à esquerda */
  margin-right: 8px !important;            /* Margem à direita */
}

/* =========================================================================
   MENU DROPDOWN
   ========================================================================= */
.units-panel .units-select__menu,
.units-panel div[class*="menu"],
.units-panel .css-26l3qy-menu,
.units-panel .css-hlgwow,
.units-panel .custom-menu,
body > div[class*="react-select__menu"],
body > div[id^="react-select"][id$="-listbox"] {
  border: 1px solid rgba(255, 255, 255, 0.05) !important; /* Borda sutil */
  border-top: none !important;             /* Sem borda superior */
  border-radius: 0 0 4px 4px !important;   /* Bordas arredondadas na base */
  margin-top: 0 !important;                /* Sem margem superior */
  padding-top: 0 !important;               /* Sem padding superior */
  z-index: 1002 !important;                /* Camada z-index */
  box-shadow: 0 4px 8px rgba(0, 0, 0, 0.5) !important; /* Sombra */
  position: relative !important;           /* Posicionamento absoluto */
  top: 1px !important;                    /* Alinhado exatamente com o select */
  width: 120px !important;                 /* Largura fixa */
  left: auto !important;                   /* Não posicionar à esquerda */
  right: 0 !important;                     /* Alinhar à direita */
}

.units-panel .units-select__menu-list,
.units-panel div[class*="menuList"] {
  padding: 0 !important;                   /* Sem padding */
  margin: 0 !important;                    /* Sem margem */
  text-align: right !important;            /* Alinhamento à direita */
  white-space: nowrap !important;          /* Sem quebra de linha */
  display: flex !important;                /* Layout flexbox */
  flex-direction: column !important;       /* Direção de coluna */
  align-items: flex-end !important;        /* Alinhamento à direita */
  overflow: hidden !important;             /* Sem overflow */
}

/* =========================================================================
   OPÇÕES DO MENU
   ========================================================================= */
.units-panel .units-select__option,
.units-panel div[class*="option"] {
  background-color: transparent !important; /* Fundo transparente */
  color: white !important;                 /* Cor do texto */
  font-size: 10.505px !important;          /* Tamanho da fonte */
  font-family: monospace !important;       /* Fonte monoespaçada */
  text-align: right !important;            /* Alinhamento à direita */
  padding: 0px 0px !important;             /* Padding reduzido */
  margin: 0 !important;                    /* Sem margem */
  white-space: nowrap !important;          /* Sem quebra de linha */
  overflow: visible !important;            /* Overflow visível */
  text-overflow: clip !important;          /* Sem truncamento de texto */
  display: flex !important;                /* Layout flexbox */
  justify-content: flex-end !important;    /* Alinhamento à direita */
  align-items: center !important;          /* Centralização vertical */
  height: 0px !important;                 /* Altura fixa igual ao select */
  line-height: 0px !important;            /* Altura da linha igual à altura */
}

.units-panel .units-select__option--is-selected,
.units-panel div[class*="option"][class*="selected"] {
  background-color: rgba(76, 175, 80, 0.2) !important; /* Fundo verde selecionado */
}

.units-panel .units-select__option:hover,
.units-panel div[class*="option"]:hover {
  background-color: rgba(76, 175, 80, 0.1) !important; /* Fundo verde hover */
}

/* =========================================================================
   INDICADOR DE DROPDOWN (SETA)
   ========================================================================= */
.units-panel .units-select__dropdown-indicator,
.units-panel div[class*="dropdownIndicator"] {
  padding: 0 4px !important;               /* Padding horizontal */
  color: rgba(255, 255, 255, 0.5) !important; /* Cor semi-transparente */
  display: flex !important;                /* Layout flexbox */
  align-items: center !important;          /* Centralização vertical */
  justify-content: center !important;      /* Centralização horizontal */
  width: auto !important;                  /* Largura automática */
  height: 100% !important;                 /* Altura total */
  opacity: 1 !important;                   /* Opacidade total */
  visibility: visible !important;          /* Visibilidade garantida */
}

.units-panel .units-select__dropdown-indicator > span,
.units-panel div[class*="dropdownIndicator"] > span {
  display: block !important;               /* Exibição em bloco */
  visibility: visible !important;          /* Visibilidade garantida */
  opacity: 1 !important;                   /* Opacidade total */
  font-size: 10px !important;              /* Tamanho da fonte */
  line-height: 16px !important;            /* Altura da linha */
  font-weight: bold !important;            /* Negrito */
  width: 16px !important;                  /* Largura fixa */
  height: 16px !important;                 /* Altura fixa */
  text-align: center !important;           /* Centralização do texto */
  color: #4CAF50 !important;               /* Cor verde */
}

/* =========================================================================
   ESTADOS DE HOVER E FOCO
   ========================================================================= */
.units-panel .units-select__control:hover .units-select__dropdown-indicator,
.units-panel div[class*="control"]:hover div[class*="dropdownIndicator"],
.units-panel .units-select__control--is-focused .units-select__dropdown-indicator,
.units-panel div[class*="control"][class*="isFocused"] div[class*="dropdownIndicator"] {
  color: #4CAF50 !important;               /* Cor verde no hover/foco */
}

.units-panel .units-select__control:hover .units-select__dropdown-indicator > span,
.units-panel div[class*="control"]:hover div[class*="dropdownIndicator"] > span,
.units-panel .units-select__control--is-focused .units-select__dropdown-indicator > span,
.units-panel div[class*="control"][class*="isFocused"] div[class*="dropdownIndicator"] > span {
  color: #4CAF50 !important;               /* Cor verde no hover/foco */
  opacity: 1 !important;                   /* Opacidade total */
}

/* =========================================================================
   ANIMAÇÕES E TRANSIÇÕES
   ========================================================================= */
.units-panel .units-select__dropdown-indicator svg,
.units-panel div[class*="dropdownIndicator"] svg {
  transition: transform 0.3s ease !important; /* Transição suave */
  display: inline-block !important;        /* Exibição inline-block */
  visibility: visible !important;          /* Visibilidade garantida */
  opacity: 1 !important;                   /* Opacidade total */
}

.units-panel .units-select__control--menu-is-open .units-select__dropdown-indicator svg,
.units-panel div[class*="control"][class*="menuIsOpen"] div[class*="dropdownIndicator"] svg {
  transform: rotate(180deg) !important;    /* Rotação da seta */
}

/* =========================================================================
   ELEMENTOS ADICIONAIS E AJUSTES
   ========================================================================= */
.units-panel .units-select__indicator-separator,
.units-panel div[class*="indicatorSeparator"] {
  display: none !important;                /* Ocultar separador */
}

.units-panel,
.units-panel .panel-block,
.units-panel .parameter-item,
.units-panel .parameter-value,
.units-panel .parameter-value > div {
  overflow: visible !important;            /* Overflow visível */
}
