/**
 * Correção para remover o efeito de fundo verde no hover da classe calculator-options
 */

/* Remover qualquer efeito de hover na div calculator-options */
.calculator-options {
  display: flex;
  flex-wrap: wrap;
  justify-content: center;
  gap: 30px;
  width: 100%;
  background-color: transparent !important;
  transition: none !important;
}

.calculator-options:hover {
  background-color: transparent !important;
  border-color: transparent !important;
  box-shadow: none !important;
  transform: none !important;
}

/* Garantir que não haja efeitos de hover indesejados */
.calculator-options * {
  transition: all 0.3s ease;
}

.calculator-options:hover * {
  /* Manter as transições apenas para os elementos filhos */
  transition: all 0.3s ease;
}

/* Manter os efeitos de hover apenas para os elementos calculator-option */
.calculator-option:hover {
  transform: translateY(-10px);
  box-shadow: 0 10px 20px rgba(0, 0, 0, 0.3);
  /* Removida a linha de border-color para não interferir com as regras específicas */
}

/* Responsividade */
@media (max-width: 768px) {
  .calculator-options {
    flex-direction: column;
    align-items: center;
  }

  .calculator-option {
    width: 100%;
    max-width: 300px;
  }
}
