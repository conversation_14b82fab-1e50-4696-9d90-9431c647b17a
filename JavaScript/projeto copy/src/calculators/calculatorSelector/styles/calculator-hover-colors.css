/**
 * Estilos específicos para o hover dos botões de calculadora
 * - Química: verde
 * - Física: roxo
 * - Matemática: azul
 */

/* Remover qualquer efeito de hover padrão para os botões de calculadora */
.calculator-option:hover {
  transform: translateY(-10px);
  box-shadow: 0 10px 20px rgba(0, 0, 0, 0.3);
  background-color: transparent !important; /* Garantir que não haja fundo por padrão */
}

/* Estilos específicos para cada calculadora no hover */
.calculator-option-chemistry:hover {
  border-color: #4CAF50 !important; /* Verde */
  background-color: rgba(76, 175, 80, 0.1) !important; /* Verde com transparência */
}

.calculator-option-physics:hover {
  border-color: #9C27B0 !important; /* Roxo */
  background-color: rgba(156, 39, 176, 0.1) !important; /* Roxo com transparência */
}

.calculator-option-math:hover {
  border-color: #2196F3 !important; /* Azul */
  background-color: rgba(33, 150, 243, 0.1) !important; /* Azul com transparência */
}

/* Garantir que as regras acima tenham prioridade */
.calculator-selector-container .calculator-option-chemistry:hover {
  border-color: #4CAF50 !important; /* Verde */
  background-color: rgba(76, 175, 80, 0.1) !important; /* Verde com transparência */
}

.calculator-selector-container .calculator-option-physics:hover {
  border-color: #9C27B0 !important; /* Roxo */
  background-color: rgba(156, 39, 176, 0.1) !important; /* Roxo com transparência */
}

.calculator-selector-container .calculator-option-math:hover {
  border-color: #2196F3 !important; /* Azul */
  background-color: rgba(33, 150, 243, 0.1) !important; /* Azul com transparência */
}
