/**
 * Quality of Life (QOL) improvements CSS
 */

/* Notification styles */
.notifications-container {
  position: fixed;
  top: 20px;
  right: 20px;
  z-index: 9999;
  display: flex;
  flex-direction: column;
  align-items: flex-end;
  pointer-events: none;
}

.notification {
  background-color: rgba(0, 0, 0, 0.8);
  color: white;
  padding: 12px 20px;
  border-radius: 4px;
  margin-bottom: 10px;
  box-shadow: 0 2px 10px rgba(0, 0, 0, 0.2);
  transform: translateX(100%);
  opacity: 0;
  transition: transform 0.3s ease, opacity 0.3s ease;
  max-width: 300px;
  pointer-events: auto;
}

.notification.show {
  transform: translateX(0);
  opacity: 1;
}

.notification.success {
  border-left: 4px solid #4CAF50;
}

.notification.error {
  border-left: 4px solid #F44336;
}

.notification.info {
  border-left: 4px solid #2196F3;
}

.notification.warning {
  border-left: 4px solid #FF9800;
}

/* Tooltip styles */
.tooltip {
  position: absolute;
  background-color: rgba(0, 0, 0, 0.8);
  color: white;
  padding: 8px 12px;
  border-radius: 4px;
  font-size: 14px;
  z-index: 9999;
  max-width: 250px;
  pointer-events: none;
  white-space: normal;
  text-align: center;
  box-shadow: 0 2px 5px rgba(0, 0, 0, 0.2);
}

.tooltip::after {
  content: '';
  position: absolute;
  border-width: 5px;
  border-style: solid;
}

.tooltip-top::after {
  border-color: rgba(0, 0, 0, 0.8) transparent transparent transparent;
  top: 100%;
  left: 50%;
  margin-left: -5px;
}

.tooltip-bottom::after {
  border-color: transparent transparent rgba(0, 0, 0, 0.8) transparent;
  bottom: 100%;
  left: 50%;
  margin-left: -5px;
}

.tooltip-left::after {
  border-color: transparent transparent transparent rgba(0, 0, 0, 0.8);
  top: 50%;
  left: 100%;
  margin-top: -5px;
}

.tooltip-right::after {
  border-color: transparent rgba(0, 0, 0, 0.8) transparent transparent;
  top: 50%;
  right: 100%;
  margin-top: -5px;
}

/* Ripple effect */
.ripple {
  position: absolute;
  background: rgba(255, 255, 255, 0.3);
  border-radius: 50%;
  transform: scale(0);
  animation: ripple 0.6s linear;
  pointer-events: none;
}

@keyframes ripple {
  to {
    transform: scale(4);
    opacity: 0;
  }
}

/* Error styles */
.error-input {
  border-color: #F44336 !important;
  box-shadow: 0 0 0 1px #F44336 !important;
}

.error-message {
  color: #F44336;
  font-size: 12px;
  margin-top: 4px;
}

/* Loading indicator */
.loading {
  position: relative;
  pointer-events: none;
}

.loading::after {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background-color: rgba(0, 0, 0, 0.3);
  display: flex;
  align-items: center;
  justify-content: center;
  z-index: 10;
}

.loading::before {
  content: '';
  position: absolute;
  top: 50%;
  left: 50%;
  width: 20px;
  height: 20px;
  margin: -10px 0 0 -10px;
  border: 2px solid rgba(255, 255, 255, 0.3);
  border-top-color: white;
  border-radius: 50%;
  animation: spin 0.8s linear infinite;
  z-index: 11;
}

@keyframes spin {
  to {
    transform: rotate(360deg);
  }
}
