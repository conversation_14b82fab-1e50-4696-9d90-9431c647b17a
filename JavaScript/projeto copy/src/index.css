body {
  margin: 0;
  font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', '<PERSON><PERSON>', 'Oxygen',
    'Ubunt<PERSON>', 'Can<PERSON>ell', 'Fira Sans', 'Droid Sans', 'Helvetica Neue',
    sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
}

code {
  font-family: source-code-pro, Menlo, Monaco, Consolas, 'Courier New',
    monospace;
}

/* Estilos globais para o React Select */
#react-select-2-listbox,
#react-select-3-listbox,
#react-select-4-listbox,
#react-select-5-listbox,
#react-select-6-listbox,
#react-select-7-listbox,
[id*="react-select"][id*="-listbox"] {
  z-index: 9999999 !important; /* Aumentar o z-index para garantir que apareça na frente */
  background-color: #1a1a1a !important;
  border: 1px solid var(--border-color) !important;
  box-shadow: 0 4px 6px rgba(0, 0, 0, 0.1) !important;
  border-radius: 0 0 8px 8px !important;
  overflow-x: hidden !important;
  overflow-y: auto !important;
  margin-top: -1px !important;
  max-height: 200px !important;
  /* Não definir position, top, left para usar os valores padrão do React Select */
}

/* Estilos para os dropdowns da interface principal */
.conversion-header [id*="react-select"][id*="-listbox"] {
  width: 100% !important;
  min-width: 200px !important;
  background-color: #000000 !important;
  /* Não definir position, left, top para usar os valores padrão do React Select */
}

/* Estilos para os dropdowns dos painéis laterais */
.parameter-value [id*="react-select"][id*="-listbox"] {
  width: 120px !important;
  z-index: 9999999 !important; /* Aumentar o z-index para garantir que apareça na frente */
  background-color: #000000 !important;
  border: 1px solid rgba(76, 175, 80, 0.5) !important;
  border-radius: 0 0 4px 4px !important;
  box-shadow: 0 4px 8px rgba(0, 0, 0, 0.5) !important;
}

/* Estilos específicos para os dropdowns do painel de unidades */
.units-panel .parameter-value [id*="react-select"][id*="-listbox"],
.units-panel .parameter-value div[class*="menu"] {
  width: 120px !important;
  z-index: 9999999 !important;
  background-color: #000000 !important;
  border: 1px solid rgba(76, 175, 80, 0.5) !important;
  border-top: none !important; /* Remove a borda superior para conectar visualmente ao select */
  border-radius: 0 0 4px 4px !important;
  box-shadow: 0 4px 8px rgba(0, 0, 0, 0.5) !important;

  /* POSICIONAMENTO DO DROPDOWN: Ajuste position para controlar como o dropdown é posicionado */
  position: absolute !important; /* Alterado de fixed para absolute para melhor posicionamento */

  /* POSICIONAMENTO DO DROPDOWN: Ajuste este valor para aproximar ou afastar o dropdown do select */
  margin-top: -1px !important; /* Conecta perfeitamente ao select */
}

/* Estilo para a barra de rolagem dos dropdowns */
[id*="react-select"][id*="-listbox"]::-webkit-scrollbar {
  width: 4px !important;
  height: 4px !important;
}

/* Estilo para o track da barra de rolagem */
[id*="react-select"][id*="-listbox"]::-webkit-scrollbar-track {
  background: rgba(0, 0, 0, 0.2) !important;
  border-radius: 2px !important;
}

/* Estilo para o thumb da barra de rolagem */
[id*="react-select"][id*="-listbox"]::-webkit-scrollbar-thumb {
  background: var(--primary-color) !important;
  border-radius: 2px !important;
  opacity: 0.7 !important;
}

/* Hover no thumb da barra de rolagem */
[id*="react-select"][id*="-listbox"]::-webkit-scrollbar-thumb:hover {
  background: var(--primary-dark) !important;
}



/* Estilos para as opções do dropdown (exceto no painel de unidades) */
div:not(.units-panel) [class*="menu"] [class*="option"] {
  background-color: #1a1a1a !important;
  color: white !important;
  padding: 5px 0px !important;
  font-size: 10.505px !important;
  font-family: monospace !important;
  text-align: right !important;
  cursor: pointer !important;
  display: block !important;
  height: auto !important;
  min-height: 22px !important;
}

div:not(.units-panel) [class*="menu"] [class*="option"]:hover {
  background-color: rgba(76, 175, 80, 0.2) !important;
}

div:not(.units-panel) [class*="menu"] [class*="option"][aria-selected="true"] {
  background-color: var(--primary-color) !important;
}

/* Garantir que o portal do React Select apareça na frente de todos os elementos */
#react-select-menu-portal,
div[id^="react-select"][id$="-portal"],
div[id^="react-select"][id$="-menu"] {
  z-index: 9999999 !important;
}

/* Garantir que o menu dropdown do painel de unidades apareça acima de tudo */
body > div[class*="react-select__menu"],
body > div[id^="react-select"][id$="-listbox"] {
  z-index: 9999999 !important;
  position: absolute !important;
}

/* Estilo específico para as setas dos dropdowns */
.custom-dropdown-indicator > div,
div[class*="dropdownIndicator"] > div {
  display: block !important;
  visibility: visible !important;
  opacity: 1 !important;
  font-size: 12px !important;
  line-height: 1 !important;
}


