/**
 * Quality of Life (QOL) utility functions
 */

/**
 * Shows a notification message
 * @param {string} message - The message to display
 * @param {string} type - The type of notification ('success', 'error', 'info')
 * @param {number} duration - How long to show the notification in ms
 */
export const showNotification = (message, type = 'info', duration = 3000) => {
  // Remove any existing notifications
  const existingNotification = document.querySelector('.notification');
  if (existingNotification) {
    existingNotification.remove();
  }

  // Create notification element
  const notification = document.createElement('div');
  notification.className = `notification ${type}`;
  notification.textContent = message;
  
  // Add to DOM
  document.body.appendChild(notification);
  
  // Trigger animation
  setTimeout(() => {
    notification.classList.add('show');
  }, 10);
  
  // Remove after duration
  setTimeout(() => {
    notification.classList.remove('show');
    setTimeout(() => {
      notification.remove();
    }, 500);
  }, duration);
};

/**
 * Debounce function to limit how often a function can be called
 * @param {Function} func - The function to debounce
 * @param {number} wait - The debounce delay in ms
 * @returns {Function} - The debounced function
 */
export const debounce = (func, wait = 300) => {
  let timeout;
  
  return function executedFunction(...args) {
    const later = () => {
      clearTimeout(timeout);
      func(...args);
    };
    
    clearTimeout(timeout);
    timeout = setTimeout(later, wait);
  };
};

/**
 * Auto-clears an input field on focus
 * @param {Event} e - The focus event
 */
export const autoClearInput = (e) => {
  const input = e.target;
  const originalValue = input.value;
  
  // Store the original value as a data attribute
  input.setAttribute('data-original-value', originalValue);
  
  // Clear the input
  input.value = '';
  
  // Add a blur event listener to restore the value if nothing was entered
  input.addEventListener('blur', function onBlur() {
    if (input.value === '') {
      input.value = input.getAttribute('data-original-value');
    }
    
    // Remove this blur event listener
    input.removeEventListener('blur', onBlur);
  }, { once: true });
};

/**
 * Adds keyboard shortcuts to elements
 * @param {Object} shortcuts - Object mapping key combinations to element selectors
 * @example
 * // Usage:
 * addKeyboardShortcuts({
 *   'Alt+C': '#calculate-button',
 *   'Alt+S': '#save-button'
 * });
 */
export const addKeyboardShortcuts = (shortcuts) => {
  document.addEventListener('keydown', (e) => {
    // Build the key combination string (e.g., "Alt+C")
    const key = e.key.toLowerCase();
    const combo = [
      e.ctrlKey ? 'Ctrl' : '',
      e.altKey ? 'Alt' : '',
      e.shiftKey ? 'Shift' : '',
      key === ' ' ? 'Space' : key.charAt(0).toUpperCase() + key.slice(1)
    ].filter(Boolean).join('+');
    
    // Check if this combo matches any of our shortcuts
    const selector = shortcuts[combo];
    if (selector) {
      const element = document.querySelector(selector);
      if (element) {
        e.preventDefault(); // Prevent default browser behavior
        element.click();
        
        // Add a visual feedback
        element.classList.add('shortcut-activated');
        setTimeout(() => {
          element.classList.remove('shortcut-activated');
        }, 200);
      }
    }
  });
  
  // Add data-shortcut attributes to elements for visual indication
  Object.entries(shortcuts).forEach(([combo, selector]) => {
    const element = document.querySelector(selector);
    if (element) {
      element.setAttribute('data-shortcut', combo);
    }
  });
};

/**
 * Creates a custom checkbox element
 * @param {Object} options - Checkbox options
 * @param {string} options.id - Checkbox ID
 * @param {string} options.label - Checkbox label
 * @param {boolean} options.checked - Initial checked state
 * @param {Function} options.onChange - Change event handler
 * @returns {HTMLElement} - The checkbox container element
 */
export const createCustomCheckbox = ({ id, label, checked = false, onChange }) => {
  // Create container
  const container = document.createElement('label');
  container.className = 'custom-checkbox';
  container.htmlFor = id;
  
  // Create hidden input
  const input = document.createElement('input');
  input.type = 'checkbox';
  input.id = id;
  input.checked = checked;
  input.addEventListener('change', onChange);
  
  // Create custom checkbox visual
  const checkmark = document.createElement('span');
  checkmark.className = 'checkmark';
  
  // Create label text
  const labelText = document.createElement('span');
  labelText.textContent = label;
  
  // Assemble the elements
  container.appendChild(input);
  container.appendChild(checkmark);
  container.appendChild(labelText);
  
  return container;
};

/**
 * Creates a custom radio button element
 * @param {Object} options - Radio button options
 * @param {string} options.id - Radio button ID
 * @param {string} options.name - Radio button group name
 * @param {string} options.label - Radio button label
 * @param {boolean} options.checked - Initial checked state
 * @param {Function} options.onChange - Change event handler
 * @returns {HTMLElement} - The radio button container element
 */
export const createCustomRadio = ({ id, name, label, checked = false, onChange }) => {
  // Create container
  const container = document.createElement('label');
  container.className = 'custom-radio';
  container.htmlFor = id;
  
  // Create hidden input
  const input = document.createElement('input');
  input.type = 'radio';
  input.id = id;
  input.name = name;
  input.checked = checked;
  input.addEventListener('change', onChange);
  
  // Create custom radio visual
  const radioMark = document.createElement('span');
  radioMark.className = 'radio-mark';
  
  // Create label text
  const labelText = document.createElement('span');
  labelText.textContent = label;
  
  // Assemble the elements
  container.appendChild(input);
  container.appendChild(radioMark);
  container.appendChild(labelText);
  
  return container;
};

/**
 * Adds a tooltip to an element
 * @param {HTMLElement} element - The element to add a tooltip to
 * @param {string} text - The tooltip text
 */
export const addTooltip = (element, text) => {
  element.setAttribute('data-tooltip', text);
};

/**
 * Adds error styling to an input element
 * @param {HTMLElement} inputElement - The input element
 * @param {string} errorMessage - The error message
 */
export const showInputError = (inputElement, errorMessage) => {
  // Add error class to input
  inputElement.classList.add('error-input');
  
  // Create error message element if it doesn't exist
  let errorElement = inputElement.nextElementSibling;
  if (!errorElement || !errorElement.classList.contains('error-message')) {
    errorElement = document.createElement('div');
    errorElement.className = 'error-message';
    inputElement.parentNode.insertBefore(errorElement, inputElement.nextSibling);
  }
  
  // Set error message
  errorElement.textContent = errorMessage;
  
  // Remove error after input changes
  const clearError = () => {
    inputElement.classList.remove('error-input');
    if (errorElement) {
      errorElement.remove();
    }
    
    // Remove event listeners
    inputElement.removeEventListener('input', clearError);
    inputElement.removeEventListener('focus', clearError);
  };
  
  inputElement.addEventListener('input', clearError);
  inputElement.addEventListener('focus', clearError);
};

/**
 * Shows a loading indicator on an element
 * @param {HTMLElement} element - The element to show loading on
 * @param {boolean} isLoading - Whether to show or hide the loading state
 */
export const setLoading = (element, isLoading) => {
  if (isLoading) {
    element.classList.add('loading');
    element.disabled = true;
  } else {
    element.classList.remove('loading');
    element.disabled = false;
  }
};

/**
 * Adds ripple effect to buttons
 * @param {HTMLElement} button - The button element
 */
export const addRippleEffect = (button) => {
  button.addEventListener('click', function(e) {
    const rect = button.getBoundingClientRect();
    const x = e.clientX - rect.left;
    const y = e.clientY - rect.top;
    
    const ripple = document.createElement('span');
    ripple.className = 'ripple';
    ripple.style.left = `${x}px`;
    ripple.style.top = `${y}px`;
    
    button.appendChild(ripple);
    
    setTimeout(() => {
      ripple.remove();
    }, 600);
  });
};

/**
 * Checks if the user prefers reduced motion
 * @returns {boolean} - Whether the user prefers reduced motion
 */
export const prefersReducedMotion = () => {
  return window.matchMedia('(prefers-reduced-motion: reduce)').matches;
};

/**
 * Adds auto-clear functionality to input fields
 */
export const setupAutoClearInputs = () => {
  const inputs = document.querySelectorAll('input.auto-clear');
  inputs.forEach(input => {
    input.addEventListener('focus', autoClearInput);
  });
};

/**
 * Initializes all QOL features
 */
export const initQOL = () => {
  // Setup auto-clear inputs
  setupAutoClearInputs();
  
  // Add ripple effect to all buttons
  document.querySelectorAll('button').forEach(addRippleEffect);
  
  // Add keyboard shortcuts
  addKeyboardShortcuts({
    'Alt+C': '#calculate-button',
    'Alt+R': '#reset-button',
    'Alt+S': '#save-button',
    'Alt+1': '#tab-1',
    'Alt+2': '#tab-2',
    'Alt+3': '#tab-3'
  });
};

export default {
  showNotification,
  debounce,
  autoClearInput,
  addKeyboardShortcuts,
  createCustomCheckbox,
  createCustomRadio,
  addTooltip,
  showInputError,
  setLoading,
  addRippleEffect,
  prefersReducedMotion,
  setupAutoClearInputs,
  initQOL
};
