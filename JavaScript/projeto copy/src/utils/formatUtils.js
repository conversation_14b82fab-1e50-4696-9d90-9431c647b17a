// Function to remove unnecessary zeros
export const removeTrailingZeros = (numberStr) => {
  // Garantir que o valor seja uma string antes de chamar replace
  if (numberStr === null || numberStr === undefined) {
    return '';
  }

  // Converter para string se não for
  const strValue = String(numberStr);

  // Remover zeros desnecessários
  return strValue.replace(/\.?0+$/, '');
};

// Função para formatar valores em notação científica no formato Y x 10^X
export const formatScientific = (value) => {
  if (value === null || value === undefined || isNaN(value)) {
    return '-';
  }

  // Converter para notação científica com mais precisão
  const exp = Number(value).toExponential(15);

  // Separar a base e o expoente
  const [base, exponent] = exp.split('e');

  // Limpar zeros desnecessários da base
  const cleanBase = removeTrailingZeros(base);

  // Retornar no formato Y x 10^X
  return `${cleanBase} x 10^${parseInt(exponent)}`;
};

// Função para formatar valores numéricos
export const formatValue = (value) => {
  // Verificar se o valor é nulo, indefinido ou não é um número válido
  if (value === null || value === undefined || value === '' || isNaN(Number(value))) {
    return '-';
  }

  // Converter para número para garantir que temos um valor numérico
  const numValue = Number(value);

  // Verificar novamente se o valor é válido após a conversão
  if (isNaN(numValue)) {
    return '-';
  }

  // Arredondar o valor para evitar problemas de precisão de ponto flutuante
  // Arredonda para 15 casas decimais para cálculos internos (aumentado para maior precisão)
  let roundedValue;
  try {
    roundedValue = numValue.toPrecision(15);
  } catch (error) {
    console.error('Erro ao formatar valor:', error);
    return '-';
  }

  // Converter de volta para número para remover zeros à direita
  const finalValue = Number(roundedValue);

  // Special case for zero
  if (finalValue === 0) {
    return '0';
  }

  // Verificar se o valor é muito pequeno (pode estar sendo arredondado para zero)
  if (Math.abs(finalValue) < 1e-12) {
    // Forçar notação científica para valores extremamente pequenos
    return formatScientific(finalValue);
  }

  // For very small or very large values, use scientific notation
  // Ajustado para capturar valores muito pequenos (< 0.001) ou muito grandes (> 999)
  if (Math.abs(finalValue) < 0.001 || Math.abs(finalValue) > 999) {
    // Usar a função formatScientific para formatar no formato Y x 10^X
    return formatScientific(finalValue);
  }

  // Para valores muito pequenos mas não o suficiente para notação científica
  if (Math.abs(finalValue) < 0.01) {
    const fixed = finalValue.toFixed(8); // Usar mais casas decimais
    return removeTrailingZeros(fixed);
  }

  // For normal values, use up to 4 decimal places and remove unnecessary zeros
  const fixed = finalValue.toFixed(4);
  return removeTrailingZeros(fixed);
};

// Função para formatar o resultado
export const formatResult = (value, unit) => {
  if (value === null || value === undefined || isNaN(value)) {
    return '-';
  }

  if (unit === 'atoms') {
    // Para átomos, usar notação científica no formato Y x 10^X
    return formatScientific(value);
  } else {
    // Para outras unidades, remover zeros desnecessários
    return removeTrailingZeros(value.toFixed(4));
  }
};

// Função para converter temperatura para Kelvin (para cálculos)
export const getTemperatureInKelvin = (value, unit) => {
  switch (unit) {
    case 'C':
      return value + 273.15;
    case 'F':
      return (value + 459.67) * (5/9);
    case 'K':
    default:
      return value;
  }
};
