<!DOCTYPE html>
<html lang="en">
  <head>
    <meta charset="utf-8" />
    <link rel="icon" href="%PUBLIC_URL%/favicon.ico" />
    <meta name="viewport" content="width=device-width, initial-scale=1" />
    <meta name="theme-color" content="#000000" />
    <meta
      name="description"
      content="Web site created using create-react-app"
    />
    <link rel="apple-touch-icon" href="%PUBLIC_URL%/logo192.png" />
    <!--
      manifest.json provides metadata used when your web app is installed on a
      user's mobile device or desktop. See https://developers.google.com/web/fundamentals/web-app-manifest/
    -->
    <link rel="manifest" href="%PUBLIC_URL%/manifest.json" />
    <!--
      Notice the use of %PUBLIC_URL% in the tags above.
      It will be replaced with the URL of the `public` folder during the build.
      Only files inside the `public` folder can be referenced from the HTML.

      Unlike "/favicon.ico" or "favicon.ico", "%PUBLIC_URL%/favicon.ico" will
      work correctly both with client-side routing and a non-root public URL.
      Learn how to configure a non-root public URL by running `npm run build`.
    -->
    <!-- AQUI: Mude o título da página que aparece na aba do navegador -->
    <title>Project Calculator</title>

    <!-- Supressão ultra-precoce do ResizeObserver -->
    <script>
      console.log('🚀 Aplicando supressão precoce do ResizeObserver...');

      // Interceptar console.error antes de qualquer coisa
      const originalConsoleError = console.error;
      console.error = function(...args) {
        const message = args.join(' ');
        if (message.includes('ResizeObserver') ||
            message.includes('loop completed') ||
            message.includes('undelivered notifications') ||
            message.includes('110072') ||
            message.includes('110091')) {
          return; // Silenciar completamente
        }
        return originalConsoleError.apply(console, args);
      };

      // Interceptar window.onerror antes do React
      window.onerror = function(message, source, lineno, colno, error) {
        if (String(message).includes('ResizeObserver') ||
            String(message).includes('loop completed') ||
            String(message).includes('undelivered notifications') ||
            lineno === 110072 || lineno === 110091) {
          return true; // Prevenir propagação
        }
        return false;
      };

      // Interceptar unhandledrejection
      window.onunhandledrejection = function(event) {
        const message = String(event.reason?.message || event.reason || '');
        if (message.includes('ResizeObserver') ||
            message.includes('loop completed') ||
            message.includes('undelivered notifications')) {
          event.preventDefault();
          return;
        }
      };

      console.log('✅ Supressão precoce ativada!');
    </script>
  </head>
  <body>
    <noscript>You need to enable JavaScript to run this app.</noscript>
    <div id="root"></div>
    <!--
      This HTML file is a template.
      If you open it directly in the browser, you will see an empty page.

      You can add webfonts, meta tags, or analytics to this file.
      The build step will place the bundled scripts into the <body> tag.

      To begin the development, run `npm start` or `yarn start`.
      To create a production bundle, use `npm run build` or `yarn build`.
    -->
  </body>
</html>
