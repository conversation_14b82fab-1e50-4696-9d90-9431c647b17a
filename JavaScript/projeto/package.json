{"name": "chemistry-apptest1", "version": "0.1.0", "private": true, "dependencies": {"@babylonjs/core": "^8.11.0", "@babylonjs/loaders": "^8.11.0", "@rdkit/rdkit": "^2024.3.5-1.0.0", "@testing-library/dom": "^10.4.0", "@testing-library/jest-dom": "^6.6.3", "@testing-library/react": "^16.3.0", "@testing-library/user-event": "^13.5.0", "3dmol": "^2.5.0", "babylonjs": "^8.11.0", "chemcalc": "^3.4.1", "framer-motion": "^11.18.2", "openchemlib": "^9.2.0", "react": "^19.1.0", "react-dev-inspector": "^2.0.1", "react-dom": "^19.1.0", "react-icons": "^5.5.0", "react-periodic-table": "^0.0.1", "react-router-dom": "^7.6.0", "react-scripts": "^5.0.1", "react-select": "^5.8.0", "react-slick": "^0.30.3", "slick-carousel": "^1.8.1", "web-vitals": "^2.1.4"}, "scripts": {"start": "react-scripts start", "build": "react-scripts build", "test": "react-scripts test", "eject": "react-scripts eject"}, "eslintConfig": {"extends": ["react-app", "react-app/jest"]}, "browserslist": {"production": [">0.2%", "not dead", "not op_mini all"], "development": ["last 1 chrome version", "last 1 firefox version", "last 1 safari version"]}}