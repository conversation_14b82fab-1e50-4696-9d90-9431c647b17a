import React from 'react';
import ReactDOM from 'react-dom/client';
import './index.css';
import App from './App';
import reportWebVitals from './reportWebVitals';
// Importar melhorias de QOL
import './qol';
import { NotificationProvider } from './components/NotificationProvider';
// Importar supressão definitiva do ResizeObserver
import './calculators/chemistry/utils/ultimateResizeObserverSuppressor.js';
import './calculators/chemistry/utils/bundleErrorSuppressor.js';

const root = ReactDOM.createRoot(document.getElementById('root'));
root.render(
  <React.StrictMode>
    <NotificationProvider>
      <App />
    </NotificationProvider>
  </React.StrictMode>
);

// If you want to start measuring performance in your app, pass a function
// to log results (for example: reportWebVitals(console.log))
// or send to an analytics endpoint. Learn more: https://bit.ly/CRA-vitals
reportWebVitals();
