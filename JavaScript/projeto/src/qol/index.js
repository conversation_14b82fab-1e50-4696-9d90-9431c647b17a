/**
 * QOL (Quality of Life) module entry point
 * This file exports all QOL-related functions and initializes QOL features
 */

// Import QOL CSS
import './styles/qol-improvements.css';

// Import QOL utilities
import {
  showNotification,
  debounce,
  autoClearInput,
  addKeyboardShortcuts,
  createCustomCheckbox,
  createCustomRadio,
  addTooltip,
  showInputError,
  setLoading,
  addRippleEffect,
  prefersReducedMotion,
  setupAutoClearInputs,
  initQOL
} from './utils';

// Initialize QOL features when the DOM is loaded
document.addEventListener('DOMContentLoaded', () => {
  // Initialize all QOL features
  initQOL();
});

// Export all QOL functions
export {
  showNotification,
  debounce,
  autoClearInput,
  addKeyboardShortcuts,
  createCustomCheckbox,
  createCustomRadio,
  addTooltip,
  showInputError,
  setLoading,
  addRippleEffect,
  prefersReducedMotion,
  setupAutoClearInputs,
  initQOL
};

// Default export for convenience
const QOLUtils = {
  showNotification,
  debounce,
  autoClearInput,
  addKeyboardShortcuts,
  createCustomCheckbox,
  createCustomRadio,
  addTooltip,
  showInputError,
  setLoading,
  addRippleEffect,
  prefersReducedMotion,
  setupAutoClearInputs,
  initQOL
};

export default QOLUtils;
