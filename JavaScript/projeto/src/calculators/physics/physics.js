import React, { useState } from 'react';

const mecanica = {
    // equação horária do espaço (S = So + v . t):
    equacaoHorariaEspaco: (So, v, t) => {
        return So + v * t;
    },
};

function Physics() {
    const [velocidade, setVelocidade] = useState('');
    const [aceleracao, setAceleracao] = useState('');
    const [tempo, setTempo] = useState('');
    const [posicaoInicial, setPosicaoInicial] = useState('');


    return (
        <div className="main"
            style={{
                display: 'flex',
                flexDirection: 'column',
                alignItems: 'center',
                height: '100vh',
                color: 'white'
            }}>
            <div className="title" style={{
                display: 'flex',
                justifyContent: 'center',
                alignItems: 'center',
                height: '10%',
                color: 'white',
                flexDirection: 'column'
            }}>
                <h1 style={{ color: '#9C27B0' }}>Physics Calculator</h1>
            </div>
            <div className="teste1"
                style={{
                    display: 'flex',
                    flexDirection: 'column',
                    alignItems: 'center',
                    overflow: 'hidden',
                    border: '1px solid violet',
                    borderRadius: '4px',
                    position: 'relative',
                    top: '10px',
                    left: '0px',
                    padding: '20px',
                    backgroundColor: 'rgba(34, 34, 34, 0.7)',
                }}>
                <div className="container-teste1">
                    velocidade:
                </div>
                <input
                    type="text"
                    placeholder="Enter value"
                    className="input-velocidade"
                    value={velocidade}
                    onChange={(e) => setVelocidade(e.target.value)}
                    style={{
                        display: 'flex',
                        alignSelf: 'flex-start',
                        background: 'rgba(0, 0, 0, 0.8)',
                        border: '1px solid var(--border-color)',
                        borderRadius: '4px',
                        color: 'white',
                        width: 'calc(100% - 10px)',
                        textAlign: 'center',
                    }}
                />
                aceleração:
                <input
                    type="text"
                    placeholder="Enter value"
                    className="input-aceleracao"
                    value={aceleracao}
                    onChange={(e) => setAceleracao(e.target.value)}
                    style={{
                        display: 'flex',
                        alignSelf: 'flex-start',
                        background: 'rgba(0, 0, 0, 0.8)',
                        border: '1px solid var(--border-color)',
                        borderRadius: '4px',
                        color: 'white',
                        width: 'calc(100% - 10px)',
                        textAlign: 'center',
                    }}
                />
                tempo:
                <input
                    type="text"
                    placeholder="Enter value"
                    className="input-tempo"
                    value={tempo}
                    onChange={(e) => setTempo(e.target.value)}
                    style={{
                        display: 'flex',
                        alignSelf: 'flex-start',
                        background: 'rgba(0, 0, 0, 0.8)',
                        border: '1px solid var(--border-color)',
                        borderRadius: '4px',
                        color: 'white',
                        width: 'calc(100% - 10px)',
                        textAlign: 'center',
                    }}
                />
                posição inicial:
                <input
                    type="text"
                    placeholder="Enter value"
                    className="input-posicao-inicial"
                    value={posicaoInicial}
                    onChange={(e) => setPosicaoInicial(e.target.value)}
                    style={{
                        display: 'flex',
                        alignSelf: 'flex-start',
                        background: 'rgba(0, 0, 0, 0.8)',
                        border: '1px solid var(--border-color)',
                        borderRadius: '4px',
                        color: 'white',
                        width: 'calc(100% - 10px)',
                        textAlign: 'center',
                    }}
                />
                resultado:
                <div className="input-resultado"
                    style={{
                        display: 'flex',
                        alignSelf: 'flex-start',
                        background: 'rgba(0, 0, 0, 0.8)',
                        border: '1px solid var(--border-color)',
                        borderRadius: '4px',
                        color: 'white',
                        width: 'calc(100% - 10px)',
                        textAlign: 'center',
                        justifyContent: 'center'
                    }}
                >
                    {mecanica.equacaoHorariaEspaco(posicaoInicial, velocidade, tempo)}
                </div>
            </div>
            <p style={{
                alignItems: 'left',
                position: 'fixed',
                bottom: '10px',
                left: '20px',
                color: 'white'
            }}>
                If I have time, I will do it.🥱
            </p>
        </div>
    );
}

export default Physics;