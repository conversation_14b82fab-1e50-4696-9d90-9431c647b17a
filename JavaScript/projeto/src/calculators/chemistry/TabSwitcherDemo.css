.toggle-container {
  display: flex;
  justify-content: center;
  margin: 20px 0;
  gap: 15px;
}

.toggle-button {
  background-color: rgba(0, 0, 0, 0.2);
  border: 2px solid var(--border-color, #444);
  border-radius: 8px;
  color: #aaa;
  font-size: 14px;
  font-weight: 600;
  cursor: pointer;
  padding: 8px 15px;
  transition: all 0.3s ease;
}

.toggle-button.active {
  background-color: rgba(76, 175, 80, 0.1);
  border-color: var(--primary-color, #4CAF50);
  color: var(--primary-color, #4CAF50);
  box-shadow: 0 0 10px rgba(76, 175, 80, 0.3);
}

.toggle-button:hover {
  color: white;
}

.demo-section {
  margin-bottom: 30px;
  background-color: rgba(0, 0, 0, 0.1);
  border-radius: 10px;
  padding: 20px;
  box-shadow: 0 0 10px rgba(0, 0, 0, 0.2);
}

.demo-section h2 {
  text-align: center;
  margin-bottom: 15px;
  color: var(--primary-color, #4CAF50);
}

.demo-container {
  border: 1px solid var(--border-color, #444);
  border-radius: 8px;
  padding: 15px;
  background-color: rgba(0, 0, 0, 0.1);
}

.demo-slide {
  padding: 20px;
  min-height: 200px;
  display: flex;
  flex-direction: column;
  align-items: center;
  text-align: center;
}

.demo-slide h3 {
  color: var(--primary-color, #4CAF50);
  margin-bottom: 10px;
}

.demo-content {
  margin-top: 20px;
  text-align: left;
  width: 100%;
  max-width: 600px;
}

.demo-content ul {
  margin-left: 20px;
  margin-top: 10px;
}

.demo-content li {
  margin-bottom: 8px;
}

.explanation {
  margin-top: 30px;
  background-color: rgba(0, 0, 0, 0.1);
  border-radius: 10px;
  padding: 20px;
  box-shadow: 0 0 10px rgba(0, 0, 0, 0.2);
}

.explanation h2 {
  text-align: center;
  margin-bottom: 15px;
  color: var(--primary-color, #4CAF50);
}

.comparison-table {
  overflow-x: auto;
  margin-top: 20px;
}

.comparison-table table {
  width: 100%;
  border-collapse: collapse;
  margin: 0 auto;
}

.comparison-table th,
.comparison-table td {
  padding: 10px;
  text-align: center;
  border: 1px solid var(--border-color, #444);
}

.comparison-table th {
  background-color: rgba(0, 0, 0, 0.2);
  color: var(--primary-color, #4CAF50);
}

.comparison-table tr:nth-child(even) {
  background-color: rgba(0, 0, 0, 0.05);
}

.comparison-table tr:hover {
  background-color: rgba(76, 175, 80, 0.05);
}
