import React, { useState, useEffect, useCallback, useRef } from 'react';
import '../../App.css';
import './styles/variables.css';
import Squares from '../../Squares/Squares.jsx';
import Dock from '../../Dock/Dock.jsx';
// import ChemistryTools from './components/ChemistryTools.js'; // Removido - agora importado de operations
import DebugMode from '../../components/debug/index.js'; // Importa o componente de modo debug fracionado
import SimpleCarousel from './components/carousel/SimpleCarousel.js'; // Importa o componente de carousel
import CompoundCarousel from './components/compound/CompoundCarousel.js'; // Importa o componente de carousel para o composto
import { Conversions, Conditions, GasLaws, ChemistryToolsOp, ChemicalEquationsOp } from './operations/index.js'; // Importa os componentes de operações
import { parseComposto, calcularMassaMolar, gramsToMoles, molesToGrams, molesToAtoms, atomsToMoles, convertMass, convertVolume, convertQuantity } from './utils/chemistryUtils.js';
import {
  VscHome,
  VscArchive,
  VscAccount,
  VscSettingsGear
} from 'react-icons/vsc';
import Select from 'react-select';
import { CustomMenu, CustomOption, CustomMenuList, CustomDropdownIndicator } from './components/Select.js';

// Estilos customizados para os selects do units panel
export const unitSelectStyles = {
  // PERSONALIZAÇÃO: Controle principal do select (a caixa de seleção)
  control: (provided, state) => ({
    ...provided,
    backgroundColor: 'transparent', // PERSONALIZAÇÃO: Fundo transparente
    border: 'none', // PERSONALIZAÇÃO: Sem borda
    borderRadius: state.menuIsOpen ? '4px 4px 0 0' : '4px', // Bordas arredondadas apenas no topo quando o menu está aberto
    minHeight: '22.5px', // PERSONALIZAÇÃO: Altura mínima
    height: '22.5px', // PERSONALIZAÇÃO: Altura fixa
    boxShadow: 'none', // PERSONALIZAÇÃO: Sem sombra
    cursor: 'pointer', // PERSONALIZAÇÃO: Cursor de mão ao passar o mouse
    display: 'flex',
    justifyContent: 'flex-end', // PERSONALIZAÇÃO: Alinhamento à direita
    '&:hover': {
      backgroundColor: 'rgba(255, 255, 255, 0.1)' // PERSONALIZAÇÃO: Cor de fundo ao passar o mouse
    }
  }),
  // =====================================================================
  // CONFIGURAÇÕES DO MENU DROPDOWN - MODIFIQUE AQUI
  // =====================================================================
  menu: (provided) => ({
    ...provided,
    backgroundColor: '#1a1a1a', // Cor de fundo do menu
    border: '1px solid var(--border-color)', // Borda do menu
    borderTop: '1px solid var(--border-color)', // Garante que a borda superior esteja presente
    borderRadius: '0 0 8px 8px', // Borda arredondada apenas nas bordas inferiores
    marginTop: '-1px', // Conecta o menu ao select
    boxShadow: '0 4px 6px rgba(0, 0, 0, 0.1)', // Sombra do menu
    zIndex: 9999, // Garante que o menu fique acima de outros elementos
    width: '100%', // Largura fixa igual ao select - DEVE SER IGUAL AO WIDTH DO CONTAINER
    padding: '0px', // Remove o espaçamento interno do menu
  }),
  // Configurações do portal do menu (quando renderizado no document.body)
  menuPortal: (provided) => ({
    ...provided,
    zIndex: 9999, // Garante que o menu fique acima de outros elementos
    position: 'absolute', // Posicionamento absoluto
  }),
  // =====================================================================
  // CONFIGURAÇÕES DA LISTA DE OPÇÕES - MODIFIQUE AQUI
  // =====================================================================
  menuList: (provided) => ({
    ...provided,
    padding: '0px', // Remove o espaçamento interno da lista (afeta todas as opções)
    margin: '0px', // Remove as margens externas da lista
    border: 'none', // Remove qualquer borda adicional
    borderRadius: '0 0 8px 8px', // Borda arredondada apenas nas bordas inferiores
    backgroundColor: '#1a1a1a', // Mesma cor de fundo do menu
    maxHeight: '200px', // Altura máxima da lista
  }),
  // PERSONALIZAÇÃO: Cada opção individual no menu dropdown
  option: (provided, state) => ({
    ...provided,
    // PERSONALIZAÇÃO: Cores de fundo diferentes para opções selecionadas/hover
    backgroundColor: state.isSelected ? 'var(--primary-color)' :
                    state.isFocused ? 'rgba(76, 175, 80, 0.2)' : 'transparent',
    color: 'white', // PERSONALIZAÇÃO: Cor do texto das opções
    cursor: 'pointer', // PERSONALIZAÇÃO: Cursor ao passar o mouse
    textAlign: 'right', // PERSONALIZAÇÃO: Alinhamento do texto à direita
    fontSize: '10.505px', // PERSONALIZAÇÃO: Tamanho da fonte
    fontFamily: 'monospace', // PERSONALIZAÇÃO: Fonte monoespaçada
    whiteSpace: 'nowrap', // Impede que o texto quebre em várias linhas
    overflow: 'hidden', // Esconde o texto que ultrapassar o tamanho
    // =====================================================================
    // ESPAÇAMENTO ENTRE OPÇÕES - MODIFIQUE AQUI
    // =====================================================================
    padding: '2px 0px', // Espaçamento interno: 2px acima/abaixo, 0px à direita/esquerda
    margin: '0px', // Espaçamento entre as opções (0 = sem espaço)
    borderRadius: 0, // Remove o arredondamento das bordas das opções individuais
    display: 'block', // Garante que a opção seja exibida como um bloco
    width: '100%', // Garante que a opção ocupe toda a largura do menu
    height: 'auto', // Altura automática
    minHeight: '22px', // Altura mínima
    direction: 'rtl', // Força o texto para a direita
    '&:hover': {
      backgroundColor: 'rgba(76, 175, 80, 0.2)' // PERSONALIZAÇÃO: Cor de fundo ao passar o mouse
    },
  }),
  // PERSONALIZAÇÃO: Lista de opções (container com scroll)
  menuListStyles: (base) => ({
    ...base,
    padding: '0px', // Remove o espaçamento interno da lista
    margin: '0px', // Remove as margens externas da lista
    maxHeight: '200px', // Altura máxima da lista
    width: '100%', // Garante que a lista ocupe toda a largura disponível
    overflowY: 'auto', // Habilita o scroll vertical
    scrollbarWidth: 'thin', // Define a largura da barra de rolagem
    '&::-webkit-scrollbar': {
      width: '4px', // Define a largura da barra de rolagem
      height: '4px', // Define a altura da barra de rolagem
    },
    '&::-webkit-scrollbar-track': {
      background: 'rgba(0, 0, 0, 0.2)', // Cor de fundo da trilha da barra de rolagem
      borderRadius: '2px', // Borda arredondada da trilha
    },
    '&::-webkit-scrollbar-thumb': {
      background: 'var(--primary-color)', // Cor da barra de rolagem
      borderRadius: '2px', // Borda arredondada da barra
      opacity: 0.7, // Transparência
    },
    '&::-webkit-scrollbar-thumb:hover': {
      background: 'var(--primary-dark)', // Cor da barra de rolagem ao passar o mouse
    },
  }),
  // PERSONALIZAÇÃO: Valor selecionado que aparece no select
  singleValue: (provided) => ({
    ...provided,
    color: 'var(--text-color)', // PERSONALIZAÇÃO: Cor do texto
    marginLeft: '0px',
    marginRight: '0px', // PERSONALIZAÇÃO: Espaço à direita para a seta
    textAlign: 'right', // PERSONALIZAÇÃO: Alinhamento do texto à direita
    fontSize: '10.505px', // PERSONALIZAÇÃO: Tamanho da fonte
    fontFamily: 'monospace', // PERSONALIZAÇÃO: Fonte monoespaçada
    width: '85px', // PERSONALIZAÇÃO: Largura ajustada para dar espaço à seta
    display: 'flex',
    justifyContent: 'flex-end', // PERSONALIZAÇÃO: Alinhamento à direita
    paddingRight: '0px', // PERSONALIZAÇÃO: Sem padding à direita
    whiteSpace: 'nowrap', // Impede que o texto quebre em várias linhas
    overflow: 'hidden', // Esconde o texto que ultrapassar o tamanho
    textOverflow: 'ellipsis' // Mostra '...' quando o texto for muito grande
  }),
  // PERSONALIZAÇÃO: Campo de input (usado quando isSearchable=true)
  input: (provided) => ({
    ...provided,
    color: 'white', // PERSONALIZAÇÃO: Cor do texto
    margin: '0px',
    padding: '0px',
    textAlign: 'right' // PERSONALIZAÇÃO: Alinhamento do texto à direita
  }),
  // PERSONALIZAÇÃO: Container dos indicadores (seta, separador, etc)
  indicatorsContainer: (provided) => ({
    ...provided,
    height: '22.5px',
    padding: '0',
    margin: '0',
    position: 'absolute', // PERSONALIZAÇÃO: Posicionamento absoluto
    left: '0px', // PERSONALIZAÇÃO: Posição à esquerda
    opacity: 0.7, // PERSONALIZAÇÃO: Transparência
    zIndex: 1 // PERSONALIZAÇÃO: Garante que fique acima do texto
  }),
  // PERSONALIZAÇÃO: Seta do dropdown (substituída por um componente personalizado)
  dropdownIndicator: (provided) => ({
    ...provided,
    padding: '0px 0px', // PERSONALIZAÇÃO: Espaçamento interno
    color: 'white', // PERSONALIZAÇÃO: Cor da seta
    width: '15px', // PERSONALIZAÇÃO: Largura fixa
    height: '15px', // PERSONALIZAÇÃO: Altura fixa
    '&:hover': {
      color: 'white' // PERSONALIZAÇÃO: Cor ao passar o mouse
    }
  }),
  // PERSONALIZAÇÃO: Separador entre a seta e o valor (removido)
  indicatorSeparator: () => ({
    display: 'none' // PERSONALIZAÇÃO: Oculta o separador
  }),
  // PERSONALIZAÇÃO: Container do valor selecionado
  valueContainer: (provided) => ({
    ...provided,
    padding: '0px',
    height: '22.5px',
    justifyContent: 'flex-end', // PERSONALIZAÇÃO: Alinhamento à direita
    flexDirection: 'row', // PERSONALIZAÇÃO: Direção da flexbox
    overflow: 'visible', // PERSONALIZAÇÃO: Permite que o conteúdo ultrapasse o container
    paddingLeft: '15px' // PERSONALIZAÇÃO: Espaço à esquerda para a seta
  }),
  // PERSONALIZAÇÃO: Container principal do select
  container: (provided) => ({
    ...provided,
    width: '120px', // PERSONALIZAÇÃO: Largura fixa - AJUSTE AQUI PARA MUDAR O TAMANHO DO SELECT
    minWidth: '110px', // PERSONALIZAÇÃO: Largura mínima - DEVE SER IGUAL AO WIDTH ACIMA
    maxWidth: '110px', // PERSONALIZAÇÃO: Largura máxima - DEVE SER IGUAL AO WIDTH ACIMA
    display: 'flex',
    justifyContent: 'flex-end', // PERSONALIZAÇÃO: Alinhamento à direita
    position: 'relative', // PERSONALIZAÇÃO: Posicionamento relativo para o absoluto funcionar
    overflow: 'visible'
  }),
};

// Substitua os options do select por objetos formatados para react-select
const unitOptions = {
  temperature: [
    { value: 'K', label: 'Kelvin (K)' },
    { value: 'C', label: 'Celsius (°C)' },
    { value: 'F', label: 'Fahrenheit (F)' }
  ],
  pressure: [
    { value: 'atm', label: 'Atmosphere (atm)' },
    { value: 'mmHg', label: 'Millimeters of mercury (mmHg)' },
    { value: 'Pa', label: 'Pascal (Pa)' },
    { value: 'bar', label: 'Bar (bar)' }
  ],
  volume: [
    { value: 'TL', label: 'Teraliters (TL)' },
    { value: 'GL', label: 'Gigaliters (GL)' },
    { value: 'ML', label: 'Megaliters (ML)' },
    { value: 'kL', label: 'Kiloliters (kL)' },
    { value: 'hL', label: 'Hectoliters (hL)' },
    { value: 'daL', label: 'Decaliters (daL)' },
    { value: 'L', label: 'Liters (L)' },
    { value: 'dL', label: 'Deciliters (dL)' },
    { value: 'cL', label: 'Centiliters (cL)' },
    { value: 'mL', label: 'Milliliters (mL)' },
    { value: 'µL', label: 'Microliters (µL)' },
    { value: 'nL', label: 'Nanoliters (nL)' },
    { value: 'pL', label: 'Picoliters (pL)' },
    { value: 'fL', label: 'Femtoliters (fL)' },
    { value: 'aL', label: 'Attoliters (aL)' },
    { value: 'm3', label: 'Cubic meters (m³)' }
  ],
  mass: [
    { value: 'Tg', label: 'Teragrams (Tg)' },
    { value: 'Gg', label: 'Gigagrams (Gg)' },
    { value: 'Mg', label: 'Megagrams (Mg)' },
    { value: 'kg', label: 'Kilograms (kg)' },
    { value: 'hg', label: 'Hectograms (hg)' },
    { value: 'dag', label: 'Decagrams (dag)' },
    { value: 'g', label: 'Grams (g)' },
    { value: 'dg', label: 'Decigrams (dg)' },
    { value: 'cg', label: 'Centigrams (cg)' },
    { value: 'mg', label: 'Milligrams (mg)' },
    { value: 'µg', label: 'Micrograms (µg)' },
    { value: 'ng', label: 'Nanograms (ng)' },
    { value: 'pg', label: 'Picograms (pg)' },
    { value: 'fg', label: 'Femtograms (fg)' },
    { value: 'ag', label: 'Attograms (ag)' }
  ],
  concentration: [
    { value: 'M', label: 'Molar (M)' },
    { value: 'mM', label: 'Millimolar (mM)' },
    { value: 'g/L', label: 'Grams/Liter (g/L)' }
  ],
  conversion: [
    { value: 'mass', label: 'Mass (g)' },
    { value: 'moles', label: 'Moles (mol)' },
    { value: 'volume', label: 'Volume (L)' },
    { value: 'atoms', label: 'Atoms' }
  ]
};

// Component for unit selection
// eslint-disable-next-line no-unused-vars
const UnitSelect = ({ value, onChange, options }) => {
  return (
    <Select
      value={options.find(opt => opt.value === value)}
      onChange={(option) => onChange(option.value)}
      options={options}
      styles={unitSelectStyles}
      isSearchable={false}
      components={{
        IndicatorSeparator: null
      }}
    />
  );
};

// Constantes
const R_IDEAL_GAS_CONSTANT = 0.0821; // L·atm/(mol·K)
// const CONDITIONS_PRESETS = {
//   STP: { temperature: 273.15, pressure: 1 }, // 0°C, 1 atm
//   SATP: { temperature: 298.15, pressure: 1 }, // 25°C, 1 atm
// };

// Cálculo de diluição
// function calcularDiluicao(concentracaoInicial, volumeInicial, volumeFinal) {
//   return (concentracaoInicial * volumeInicial) / volumeFinal;
// }

// function calcularVolumeParaDiluicao(concentracaoInicial, volumeInicial, concentracaoFinal) {
//   return (concentracaoInicial * volumeInicial) / concentracaoFinal;
// }

// Function to remove unnecessary zeros
const removeTrailingZeros = (numberStr) => {
  // Garantir que o valor seja uma string antes de chamar replace
  if (numberStr === null || numberStr === undefined) {
    return '';
  }

  // Converter para string se não for
  const strValue = String(numberStr);

  // Remover zeros desnecessários
  return strValue.replace(/\.?0+$/, '');
};

// Função para formatar valores em notação científica no formato Y x 10^X
const formatScientific = (value) => {
  if (value === null || value === undefined || isNaN(value)) {
    return '-';
  }

  // Converter para notação científica com mais precisão
  const exp = Number(value).toExponential(15);

  // Separar a base e o expoente
  const [base, exponent] = exp.split('e');

  // Limpar zeros desnecessários da base
  const cleanBase = removeTrailingZeros(base);

  // Retornar no formato Y x 10^X
  return `${cleanBase} x 10^${parseInt(exponent)}`;
};

function Chemistry() {
  const parametersRef = useRef(null);
  // eslint-disable-next-line no-unused-vars
  const [parametersHeight, setParametersHeight] = useState(0);

  const [composto, setComposto] = useState('');
  const [massaMolar, setMassaMolar] = useState(null);
  const [inputValue, setInputValue] = useState('');
  const [fromUnit, setFromUnit] = useState('mass');
  const [toUnit, setToUnit] = useState('moles');
  const [result, setResult] = useState(null);
  const [pressure, setPressure] = useState(1); // Use número ao invés de string
  const [temperature, setTemperature] = useState(273.15); // Use number instead of string
  const [selectedPreset, setSelectedPreset] = useState(null);
  const [activeTab, setActiveTab] = useState('conversions');

  // Add this useEffect to calculate the height of the parameters panel
  useEffect(() => {
    if (parametersRef.current) {
      const height = parametersRef.current.offsetHeight;
      document.documentElement.style.setProperty('--parameters-height', `${height}px`);
      setParametersHeight(height);
    }
  }, []);
  const [activeGasLaw, setActiveGasLaw] = useState('ideal');

  // Estados para rastrear o último campo alterado
  const [lastIdealGasField, setLastIdealGasField] = useState(null); // 'pressure', 'temperature', 'volume', 'quantity'
  const [lastBoyleField, setLastBoyleField] = useState(null); // 'p1', 'v1', 'p2', 'v2'
  const [lastCharlesField, setLastCharlesField] = useState(null); // 'v1', 't1', 'v2', 't2'

  // Estados para rastrear se o usuário está digitando em cada lei de gás
  const [isTypingIdealGas, setIsTypingIdealGas] = useState(false);
  const [isTypingBoyle, setIsTypingBoyle] = useState(false);
  const [isTypingCharles, setIsTypingCharles] = useState(false);

  // Estados para a Lei de Boyle (P₁V₁ = P₂V₂)
  const [pressure1, setPressure1] = useState(1);
  const [volume1, setVolume1] = useState(1);
  const [pressure2, setPressure2] = useState(2);
  const [volume2, setVolume2] = useState(0.5);

  // Estados para a Lei de Charles (V₁/T₁ = V₂/T₂)
  const [volume1Charles, setVolume1Charles] = useState(1);
  const [temperature1, setTemperature1] = useState(273.15);
  const [volume2Charles, setVolume2Charles] = useState(2);
  const [temperature2, setTemperature2] = useState(546.3);
  // Estado para controlar o carregamento (não utilizado atualmente)
  const [isLoading] = useState(false);
  const [error, setError] = useState(null);
  const [pressureUnit, setPressureUnit] = useState('atm');
  const [temperatureUnit, setTemperatureUnit] = useState('K');


  // Estados para unidades e valores
  const [massUnit, setMassUnit] = useState('g');
  const [massValue, setMassValue] = useState('');
  const [volumeUnit, setVolumeUnit] = useState('L');
  const [volumeValue, setVolumeValue] = useState('');
  const [quantityUnit, setQuantityUnit] = useState('mol');
  const [quantityValue, setQuantityValue] = useState('');
  const [concentrationUnit, setConcentrationUnit] = useState('M');
  // Estado para forçar atualização da interface quando as unidades mudarem
  const [unitUpdateTrigger, setUnitUpdateTrigger] = useState(0);
  // Estado para armazenar o valor original em gramas (para conversões consistentes)
  // eslint-disable-next-line no-unused-vars
  const [originalValueInGrams, setOriginalValueInGrams] = useState(null);

  // Helper functions
  const formatValue = (value) => {
    // Verificar se o valor é nulo, indefinido ou não é um número válido
    if (value === null || value === undefined || value === '' || isNaN(Number(value))) {
      return '-';
    }

    // Converter para número para garantir que temos um valor numérico
    const numValue = Number(value);

    // Verificar novamente se o valor é válido após a conversão
    if (isNaN(numValue)) {
      return '-';
    }

    // Arredondar o valor para evitar problemas de precisão de ponto flutuante
    // Arredonda para 15 casas decimais para cálculos internos (aumentado para maior precisão)
    let roundedValue;
    try {
      roundedValue = numValue.toPrecision(15);
    } catch (error) {
      console.error('Erro ao formatar valor:', error);
      return '-';
    }

    // Converter de volta para número para remover zeros à direita
    const finalValue = Number(roundedValue);

    // Special case for zero
    if (finalValue === 0) {
      return '0';
    }

    // Verificar se o valor é muito pequeno (pode estar sendo arredondado para zero)
    if (Math.abs(finalValue) < 1e-12) {
      // Forçar notação científica para valores extremamente pequenos
      return formatScientific(finalValue);
    }

    // For very small or very large values, use scientific notation
    // Ajustado para capturar valores muito pequenos (< 0.001) ou muito grandes (> 999)
    if (Math.abs(finalValue) < 0.001 || Math.abs(finalValue) > 999) {
      // Usar a função formatScientific para formatar no formato Y x 10^X
      return formatScientific(finalValue);
    }

    // Para valores muito pequenos mas não o suficiente para notação científica
    if (Math.abs(finalValue) < 0.01) {
      const fixed = finalValue.toFixed(8); // Usar mais casas decimais
      return removeTrailingZeros(fixed);
    }

    // For normal values, use up to 4 decimal places and remove unnecessary zeros
    const fixed = finalValue.toFixed(4);
    return removeTrailingZeros(fixed);
  };

  // Função para atualizar apenas o valor exibido nos parâmetros, sem recalcular
  // eslint-disable-next-line no-unused-vars
  const updateParameters = (value, type) => {
    if (value === null || isNaN(value)) return;

    switch (type) {
      case 'mass':
        setMassValue(formatValue(value));
        break;
      case 'volume':
        setVolumeValue(formatValue(value));
        break;
      case 'quantity':
        setQuantityValue(formatValue(value));
        break;
      default:
        break;
    }
  };

  const items = [
    { icon: <VscHome size={18} />, label: 'Home', onClick: () => alert('Home!') },
    { icon: <VscArchive size={18} />, label: 'Archive', onClick: () => alert('Archive!') },
    { icon: <VscAccount size={18} />, label: 'Profile', onClick: () => alert('Profile!') },
    { icon: <VscSettingsGear size={18} />, label: 'Settings', onClick: () => alert('Settings!') },
  ];

  // Estado para armazenar as informações dos elementos
  const [elementosInfo, setElementosInfo] = useState([]);

  useEffect(() => {
    if (composto) {
      try {
        const resultado = calcularMassaMolar(composto);
        setMassaMolar(resultado.massaMolar);
        setElementosInfo(resultado.elementos);
      } catch (error) {
        console.error("Erro ao calcular massa molar:", error);
        setMassaMolar(null);
        setElementosInfo([]);
      }
    } else {
      setMassaMolar(null);
      setElementosInfo([]);
    }
  }, [composto]);

  // eslint-disable-next-line no-unused-vars
  const formatResult = (value, unit) => {
    if (value === null || value === undefined || isNaN(value)) {
      return '-';
    }

    if (unit === 'atoms') {
      // Para átomos, usar notação científica no formato Y x 10^X
      return formatScientific(value);
    } else {
      // Para outras unidades, remover zeros desnecessários
      return removeTrailingZeros(value.toFixed(4));
    }
  };

  // Função para converter temperatura para Kelvin (para cálculos)
  const getTemperatureInKelvin = (value, unit) => {
    switch (unit) {
      case 'C':
        return value + 273.15;
      case 'F':
        return (value + 459.67) * (5/9);
      case 'K':
      default:
        return value;
    }
  };

  // Funções de conversão para cada tipo de unidade
  const convertMassCallback = useCallback(convertMass, []);

  // Atualizar o handleConvert para usar a temperatura em Kelvin
  const handleConvert = useCallback(() => {
    if (!inputValue || !massaMolar) {
      setResult(null);
      return;
    }

    const value = parseFloat(inputValue);
    if (isNaN(value)) {
      setError('Valor inválido');
      return;
    }

    // Converter temperatura para Kelvin para os cálculos
    const tempInKelvin = getTemperatureInKelvin(temperature, temperatureUnit);

    let convertedValue;
    let massaValue, volumeValue, molesValue;

    // Primeiro, calcular mols como valor intermediário
    switch (fromUnit) {
      case 'mass':
        // Usar o valor original em gramas se disponível, ou converter o valor atual para gramas
        let valueInGrams;
        if (originalValueInGrams !== null && massUnit !== 'g') {
          // Se temos o valor original em gramas e a unidade atual não é gramas, use o valor original
          valueInGrams = originalValueInGrams;
          // Usando valor original em gramas
        } else {
          // Caso contrário, converta o valor atual para gramas
          valueInGrams = convertMassCallback(value, massUnit, 'g');
          // Convertendo valor atual para gramas
        }
        molesValue = gramsToMoles(valueInGrams, massaMolar);
        // Mols calculados
        break;
      case 'volume':
        // Converter o valor para litros primeiro (unidade base para cálculos)
        const valueInLiters = convertVolume(value, volumeUnit, 'L');
        molesValue = (pressure * valueInLiters) / (R_IDEAL_GAS_CONSTANT * tempInKelvin);
        break;
      case 'moles':
        // Converter o valor para mols primeiro (unidade base para cálculos)
        molesValue = convertQuantity(value, quantityUnit, 'mol');
        break;
      case 'atoms':
        molesValue = atomsToMoles(value);
        break;
      default:
        break;
    }

    // Depois, calcular todos os outros valores a partir dos mols
    if (molesValue !== undefined) {
      // Calcular massa em gramas (unidade base)
      const massaValueInGrams = molesToGrams(molesValue, massaMolar);
      // Converter para a unidade de massa selecionada
      massaValue = convertMassCallback(massaValueInGrams, 'g', massUnit);

      // Calcular volume em litros (unidade base)
      const volumeValueInLiters = (molesValue * R_IDEAL_GAS_CONSTANT * tempInKelvin) / pressure;
      // Converter para a unidade de volume selecionada
      volumeValue = convertVolume(volumeValueInLiters, 'L', volumeUnit);

      // Atualizar todos os parâmetros
      setMassValue(formatValue(massaValue));
      setVolumeValue(formatValue(volumeValue));
      // Converter para a unidade de quantidade selecionada
      const quantityValueInSelectedUnit = convertQuantity(molesValue, 'mol', quantityUnit);
      setQuantityValue(formatValue(quantityValueInSelectedUnit));

      // Calcular o valor final baseado na unidade de destino
      switch (toUnit) {
        case 'mass':
          // Já está na unidade correta (massUnit)
          convertedValue = massaValue;
          break;
        case 'volume':
          // Já está na unidade correta (volumeUnit)
          convertedValue = volumeValue;
          break;
        case 'moles':
          // Converter para a unidade de quantidade selecionada
          convertedValue = convertQuantity(molesValue, 'mol', quantityUnit);
          break;
        case 'atoms':
          convertedValue = molesToAtoms(molesValue);
          break;
        default:
          break;
      }
    }

    // Garantir que o resultado não seja null quando houver um valor válido
    if (convertedValue !== undefined && convertedValue !== null) {
      setResult(formatValue(convertedValue));
      setError(null);
    } else {
      setResult(null);
      setError('Erro na conversão');
    }
  }, [inputValue, fromUnit, toUnit, massaMolar, pressure, temperature, temperatureUnit, massUnit, volumeUnit, quantityUnit, convertMass, originalValueInGrams]);

  // Estado para controlar a animação do botão de inverter
  const [isRotating, setIsRotating] = useState(false);

  // Adicionar função para inverter unidades
  const handleSwapUnits = () => {
    // Ativar a animação
    setIsRotating(true);

    // Inverter as unidades
    setFromUnit(toUnit);
    setToUnit(fromUnit);

    // Formatar o resultado para o campo de entrada
    if (result !== null) {
      // Verifica se o valor é muito grande ou muito pequeno para usar notação científica
      if (Math.abs(result) < 0.001 || Math.abs(result) > 999 ||
          (Math.abs(result) > 0 && Math.abs(result) < 0.000000001)) {
        // Usa a função formatScientific para formatar no formato Y x 10^X
        setInputValue(formatScientific(result));
      } else {
        // Para valores normais, usa toString()
        setInputValue(result.toString());
      }

      // Atualiza o valor original em gramas se a nova unidade for 'mass'
      if (toUnit === 'mass') {
        const numValue = parseFloat(result);
        if (!isNaN(numValue)) {
          // Converte o resultado para gramas
          const valueInGrams = convertMass(numValue, massUnit, 'g');
          setOriginalValueInGrams(valueInGrams);
        }
      } else {
        // Limpa o valor original em gramas se a nova unidade não for 'mass'
        setOriginalValueInGrams(null);
      }
    } else {
      setInputValue('');
      setOriginalValueInGrams(null);
    }

    // Garantir que o resultado seja definido corretamente
    if (inputValue) {
      const value = parseFloat(inputValue);
      if (!isNaN(value)) {
        setResult(formatValue(value));
      } else {
        setResult(null);
      }
    } else {
      setResult(null);
    }

    // Força a atualização da interface
    setUnitUpdateTrigger(prev => prev + 1);

    // Desativar a animação após 500ms (duração da animação)
    setTimeout(() => {
      setIsRotating(false);
    }, 500);
  };

  // Modificar useEffect para fazer conversão automática
  // Função para obter a unidade específica com base no tipo de unidade
  // Função para obter a unidade específica com base no tipo de unidade
  const getSpecificUnit = (unitType) => {
    switch (unitType) {
      case 'mass':
        return massUnit; // 'g', 'kg', etc.
      case 'volume':
        return volumeUnit; // 'L', 'mL', etc.
      case 'moles':
        return quantityUnit; // 'mol', 'mmol', etc.
      case 'atoms':
        return ''; // Retorna string vazia em vez de null
      default:
        return unitType;
    }
  };

  // Funções para calcular valores com base nas leis de gases
  const calculateIdealGasLaw = () => {
    console.log("Valores atuais:", { pressure, temperature, quantityValue, volumeValue, lastIdealGasField });

    // Verificar diretamente quais valores estão presentes e quais estão ausentes
    // Um valor é considerado ausente se for null, undefined, string vazia ou não for um número válido
    // Permitir zero como valor válido
    const isPressurePresent = pressure !== null && pressure !== undefined && pressure !== '' && !isNaN(parseFloat(pressure));
    const isTemperaturePresent = temperature !== null && temperature !== undefined && temperature !== '' && !isNaN(parseFloat(temperature));
    const isQuantityPresent = quantityValue !== null && quantityValue !== undefined && quantityValue !== '' && !isNaN(parseFloat(quantityValue));
    const isVolumePresent = volumeValue !== null && volumeValue !== undefined && volumeValue !== '' && !isNaN(parseFloat(volumeValue));

    console.log("Presença de valores:", {
      isPressurePresent,
      isTemperaturePresent,
      isQuantityPresent,
      isVolumePresent
    });

    // Converter valores de texto para números apenas se estiverem presentes
    const pressureNum = isPressurePresent ? parseFloat(pressure) : null;
    const temperatureNum = isTemperaturePresent ? parseFloat(temperature) : null;
    const quantityValueNum = isQuantityPresent ? parseFloat(quantityValue) : null;
    const volumeValueNum = isVolumePresent ? parseFloat(volumeValue) : null;

    // Contar quantos valores estão presentes
    const presentValuesCount = [isPressurePresent, isTemperaturePresent, isQuantityPresent, isVolumePresent].filter(Boolean).length;

    // Só calcular se pelo menos 3 valores estiverem presentes (para calcular o quarto)
    if (presentValuesCount < 3) {
      console.log(`Não é possível calcular: ${presentValuesCount} valores presentes, precisamos de pelo menos 3.`);
      return;
    }

    // Se todos os 4 valores estiverem presentes, respeitar o último campo alterado
    if (presentValuesCount === 4 && lastIdealGasField) {
      console.log(`Todos os 4 valores estão presentes. Respeitando o último campo alterado: ${lastIdealGasField}`);

      // Garantir que os valores numéricos sejam válidos (incluindo zero)
      // Verificar se o último campo alterado é zero
      const lastFieldIsZero =
        (lastIdealGasField === 'pressure' && pressureNum === 0) ||
        (lastIdealGasField === 'temperature' && temperatureNum === 0) ||
        (lastIdealGasField === 'volume' && volumeValueNum === 0) ||
        (lastIdealGasField === 'quantity' && quantityValueNum === 0);

      // Se o último campo alterado for zero, não recalcular
      if (lastFieldIsZero) {
        console.log(`O último campo alterado (${lastIdealGasField}) é zero. Mantendo o valor.`);
        return;
      }

      // Não recalcular o último campo alterado
      if (lastIdealGasField === 'pressure') {
        // Recalcular os outros valores com base na pressão
        const tempInKelvin = getTemperatureInKelvin(temperatureNum, temperatureUnit);
        // Evitar divisão por zero
        if (pressureNum === 0) {
          setVolumeValue('0');
        } else {
          const volumeInLiters = (quantityValueNum * R_IDEAL_GAS_CONSTANT * tempInKelvin) / pressureNum;
          setVolumeValue(formatValue(volumeInLiters));
        }
        return;
      } else if (lastIdealGasField === 'temperature') {
        // Recalcular os outros valores com base na temperatura
        const tempInKelvin = getTemperatureInKelvin(temperatureNum, temperatureUnit);
        // Evitar divisão por zero
        if (tempInKelvin === 0 || pressureNum === 0) {
          setVolumeValue('0');
        } else {
          const volumeInLiters = (quantityValueNum * R_IDEAL_GAS_CONSTANT * tempInKelvin) / pressureNum;
          setVolumeValue(formatValue(volumeInLiters));
        }
        return;
      } else if (lastIdealGasField === 'volume') {
        // Recalcular os outros valores com base no volume
        const tempInKelvin = getTemperatureInKelvin(temperatureNum, temperatureUnit);
        // Evitar divisão por zero
        if (tempInKelvin === 0 || volumeValueNum === 0) {
          setQuantityValue('0');
        } else {
          const molesValue = (pressureNum * volumeValueNum) / (R_IDEAL_GAS_CONSTANT * tempInKelvin);
          setQuantityValue(formatValue(molesValue));
        }
        return;
      } else if (lastIdealGasField === 'quantity') {
        // Recalcular os outros valores com base nos moles
        const tempInKelvin = getTemperatureInKelvin(temperatureNum, temperatureUnit);
        // Evitar divisão por zero
        if (pressureNum === 0) {
          setVolumeValue('0');
        } else {
          const volumeInLiters = (quantityValueNum * R_IDEAL_GAS_CONSTANT * tempInKelvin) / pressureNum;
          setVolumeValue(formatValue(volumeInLiters));
        }
        return;
      }
    }

    // PV = nRT
    // Calcular volume se pressão, temperatura e moles forem fornecidos, mas volume não
    if (isPressurePresent && isTemperaturePresent && isQuantityPresent && !isVolumePresent) {
      console.log("Calculando volume...");
      const tempInKelvin = getTemperatureInKelvin(temperatureNum, temperatureUnit);
      const volumeInLiters = (quantityValueNum * R_IDEAL_GAS_CONSTANT * tempInKelvin) / pressureNum;
      console.log(`Volume calculado: ${volumeInLiters} L`);
      setVolumeValue(formatValue(volumeInLiters));
    }
    // Calcular moles se pressão, temperatura e volume forem fornecidos, mas moles não
    else if (isPressurePresent && isTemperaturePresent && isVolumePresent && !isQuantityPresent) {
      console.log("Calculando moles...");
      const tempInKelvin = getTemperatureInKelvin(temperatureNum, temperatureUnit);
      const molesValue = (pressureNum * volumeValueNum) / (R_IDEAL_GAS_CONSTANT * tempInKelvin);
      console.log(`Moles calculados: ${molesValue} mol`);
      setQuantityValue(formatValue(molesValue));
    }
    // Calcular pressão se temperatura, volume e moles forem fornecidos, mas pressão não
    else if (isTemperaturePresent && isVolumePresent && isQuantityPresent && !isPressurePresent) {
      console.log("Calculando pressão...");
      const tempInKelvin = getTemperatureInKelvin(temperatureNum, temperatureUnit);
      const pressureValue = (quantityValueNum * R_IDEAL_GAS_CONSTANT * tempInKelvin) / volumeValueNum;
      console.log(`Pressão calculada: ${pressureValue} atm`);
      setPressure(formatValue(pressureValue));
    }
    // Calcular temperatura se pressão, volume e moles forem fornecidos, mas temperatura não
    else if (isPressurePresent && isVolumePresent && isQuantityPresent && !isTemperaturePresent) {
      console.log("Calculando temperatura...");
      const tempInKelvin = (pressureNum * volumeValueNum) / (quantityValueNum * R_IDEAL_GAS_CONSTANT);
      const tempInCurrentUnit = convertTemperature(tempInKelvin, 'K', temperatureUnit);
      console.log(`Temperatura calculada: ${tempInKelvin} K = ${tempInCurrentUnit} ${temperatureUnit}`);
      setTemperature(formatValue(tempInCurrentUnit));
    }
  };

  const calculateBoyleLaw = () => {
    console.log("Valores atuais (Boyle):", { pressure1, volume1, pressure2, volume2, lastBoyleField });

    // Verificar diretamente quais valores estão presentes e quais estão ausentes
    const isP1Present = pressure1 !== null && pressure1 !== undefined && pressure1 !== '' && !isNaN(parseFloat(pressure1));
    const isV1Present = volume1 !== null && volume1 !== undefined && volume1 !== '' && !isNaN(parseFloat(volume1));
    const isP2Present = pressure2 !== null && pressure2 !== undefined && pressure2 !== '' && !isNaN(parseFloat(pressure2));
    const isV2Present = volume2 !== null && volume2 !== undefined && volume2 !== '' && !isNaN(parseFloat(volume2));

    console.log("Presença de valores (Boyle):", {
      isP1Present,
      isV1Present,
      isP2Present,
      isV2Present
    });

    // Converter valores de texto para números apenas se estiverem presentes
    const pressure1Num = isP1Present ? parseFloat(pressure1) : null;
    const volume1Num = isV1Present ? parseFloat(volume1) : null;
    const pressure2Num = isP2Present ? parseFloat(pressure2) : null;
    const volume2Num = isV2Present ? parseFloat(volume2) : null;

    // Contar quantos valores estão presentes
    const presentValuesCount = [isP1Present, isV1Present, isP2Present, isV2Present].filter(Boolean).length;

    // Só calcular se pelo menos 3 valores estiverem presentes (para calcular o quarto)
    if (presentValuesCount < 3) {
      console.log(`Não é possível calcular Boyle: ${presentValuesCount} valores presentes, precisamos de pelo menos 3.`);
      return;
    }

    // Se todos os 4 valores estiverem presentes, respeitar o último campo alterado
    if (presentValuesCount === 4 && lastBoyleField) {
      console.log(`Todos os 4 valores estão presentes. Respeitando o último campo alterado: ${lastBoyleField}`);

      // Garantir que os valores numéricos sejam válidos (incluindo zero)
      // Verificar se o último campo alterado é zero
      const lastFieldIsZero =
        (lastBoyleField === 'p1' && pressure1Num === 0) ||
        (lastBoyleField === 'v1' && volume1Num === 0) ||
        (lastBoyleField === 'p2' && pressure2Num === 0) ||
        (lastBoyleField === 'v2' && volume2Num === 0);

      // Se o último campo alterado for zero, não recalcular
      if (lastFieldIsZero) {
        console.log(`O último campo alterado (${lastBoyleField}) é zero. Mantendo o valor.`);
        return;
      }

      // Não recalcular o último campo alterado
      if (lastBoyleField === 'p1') {
        // Recalcular P2 com base em P1, V1 e V2
        if (volume2Num === 0) {
          setPressure2('0');
        } else {
          const p2 = (pressure1Num * volume1Num) / volume2Num;
          setPressure2(formatValue(p2));
        }
        return;
      } else if (lastBoyleField === 'v1') {
        // Recalcular V2 com base em P1, V1 e P2
        if (pressure2Num === 0) {
          setVolume2('0');
        } else {
          const v2 = (pressure1Num * volume1Num) / pressure2Num;
          setVolume2(formatValue(v2));
        }
        return;
      } else if (lastBoyleField === 'p2') {
        // Recalcular P1 com base em V1, P2 e V2
        if (volume1Num === 0) {
          setPressure1('0');
        } else {
          const p1 = (pressure2Num * volume2Num) / volume1Num;
          setPressure1(formatValue(p1));
        }
        return;
      } else if (lastBoyleField === 'v2') {
        // Recalcular V1 com base em P1, P2 e V2
        if (pressure1Num === 0) {
          setVolume1('0');
        } else {
          const v1 = (pressure2Num * volume2Num) / pressure1Num;
          setVolume1(formatValue(v1));
        }
        return;
      }
    }

    // P₁V₁ = P₂V₂
    // Calcular P₂ se P₁, V₁ e V₂ forem fornecidos, mas P₂ não
    if (isP1Present && isV1Present && isV2Present && !isP2Present) {
      console.log("Calculando P₂...");
      const p2 = (pressure1Num * volume1Num) / volume2Num;
      console.log(`P₂ calculado: ${p2} ${pressureUnit}`);
      setPressure2(formatValue(p2));
    }
    // Calcular V₂ se P₁, V₁ e P₂ forem fornecidos, mas V₂ não
    else if (isP1Present && isV1Present && isP2Present && !isV2Present) {
      console.log("Calculando V₂...");
      const v2 = (pressure1Num * volume1Num) / pressure2Num;
      console.log(`V₂ calculado: ${v2} ${volumeUnit}`);
      setVolume2(formatValue(v2));
    }
    // Calcular P₁ se V₁, P₂ e V₂ forem fornecidos, mas P₁ não
    else if (isV1Present && isP2Present && isV2Present && !isP1Present) {
      console.log("Calculando P₁...");
      const p1 = (pressure2Num * volume2Num) / volume1Num;
      console.log(`P₁ calculado: ${p1} ${pressureUnit}`);
      setPressure1(formatValue(p1));
    }
    // Calcular V₁ se P₁, P₂ e V₂ forem fornecidos, mas V₁ não
    else if (isP1Present && isP2Present && isV2Present && !isV1Present) {
      console.log("Calculando V₁...");
      const v1 = (pressure2Num * volume2Num) / pressure1Num;
      console.log(`V₁ calculado: ${v1} ${volumeUnit}`);
      setVolume1(formatValue(v1));
    }
  };

  const calculateCharlesLaw = () => {
    console.log("Valores atuais (Charles):", { volume1Charles, temperature1, volume2Charles, temperature2, lastCharlesField });

    // Verificar diretamente quais valores estão presentes e quais estão ausentes
    const isV1Present = volume1Charles !== null && volume1Charles !== undefined && volume1Charles !== '' && !isNaN(parseFloat(volume1Charles));
    const isT1Present = temperature1 !== null && temperature1 !== undefined && temperature1 !== '' && !isNaN(parseFloat(temperature1));
    const isV2Present = volume2Charles !== null && volume2Charles !== undefined && volume2Charles !== '' && !isNaN(parseFloat(volume2Charles));
    const isT2Present = temperature2 !== null && temperature2 !== undefined && temperature2 !== '' && !isNaN(parseFloat(temperature2));

    console.log("Presença de valores (Charles):", {
      isV1Present,
      isT1Present,
      isV2Present,
      isT2Present
    });

    // Converter valores de texto para números apenas se estiverem presentes
    const volume1CharlesNum = isV1Present ? parseFloat(volume1Charles) : null;
    const temperature1Num = isT1Present ? parseFloat(temperature1) : null;
    const volume2CharlesNum = isV2Present ? parseFloat(volume2Charles) : null;
    const temperature2Num = isT2Present ? parseFloat(temperature2) : null;

    // Contar quantos valores estão presentes
    const presentValuesCount = [isV1Present, isT1Present, isV2Present, isT2Present].filter(Boolean).length;

    // Só calcular se pelo menos 3 valores estiverem presentes (para calcular o quarto)
    if (presentValuesCount < 3) {
      console.log(`Não é possível calcular Charles: ${presentValuesCount} valores presentes, precisamos de pelo menos 3.`);
      return;
    }

    // Se todos os 4 valores estiverem presentes, respeitar o último campo alterado
    if (presentValuesCount === 4 && lastCharlesField) {
      console.log(`Todos os 4 valores estão presentes. Respeitando o último campo alterado: ${lastCharlesField}`);

      // Garantir que os valores numéricos sejam válidos (incluindo zero)
      // Verificar se o último campo alterado é zero
      const lastFieldIsZero =
        (lastCharlesField === 'v1' && volume1CharlesNum === 0) ||
        (lastCharlesField === 't1' && temperature1Num === 0) ||
        (lastCharlesField === 'v2' && volume2CharlesNum === 0) ||
        (lastCharlesField === 't2' && temperature2Num === 0);

      // Se o último campo alterado for zero, não recalcular
      if (lastFieldIsZero) {
        console.log(`O último campo alterado (${lastCharlesField}) é zero. Mantendo o valor.`);
        return;
      }

      // Não recalcular o último campo alterado
      if (lastCharlesField === 'v1') {
        // Recalcular V2 com base em V1, T1 e T2
        if (temperature1Num === 0) {
          setVolume2Charles('0');
        } else {
          const v2 = (volume1CharlesNum * temperature2Num) / temperature1Num;
          setVolume2Charles(formatValue(v2));
        }
        return;
      } else if (lastCharlesField === 't1') {
        // Recalcular T2 com base em V1, T1 e V2
        if (volume1CharlesNum === 0) {
          setTemperature2('0');
        } else {
          const t2 = (volume2CharlesNum * temperature1Num) / volume1CharlesNum;
          setTemperature2(formatValue(t2));
        }
        return;
      } else if (lastCharlesField === 'v2') {
        // Recalcular V1 com base em T1, V2 e T2
        if (temperature2Num === 0) {
          setVolume1Charles('0');
        } else {
          const v1 = (volume2CharlesNum * temperature1Num) / temperature2Num;
          setVolume1Charles(formatValue(v1));
        }
        return;
      } else if (lastCharlesField === 't2') {
        // Recalcular T1 com base em V1, V2 e T2
        if (volume2CharlesNum === 0) {
          setTemperature1('0');
        } else {
          const t1 = (volume1CharlesNum * temperature2Num) / volume2CharlesNum;
          setTemperature1(formatValue(t1));
        }
        return;
      }
    }

    // V₁/T₁ = V₂/T₂
    // Calcular V₂ se V₁, T₁ e T₂ forem fornecidos, mas V₂ não
    if (isV1Present && isT1Present && isT2Present && !isV2Present) {
      console.log("Calculando V₂...");
      const v2 = (volume1CharlesNum * temperature2Num) / temperature1Num;
      console.log(`V₂ calculado: ${v2} ${volumeUnit}`);
      setVolume2Charles(formatValue(v2));
    }
    // Calcular T₂ se V₁, T₁ e V₂ forem fornecidos, mas T₂ não
    else if (isV1Present && isT1Present && isV2Present && !isT2Present) {
      console.log("Calculando T₂...");
      const t2 = (volume2CharlesNum * temperature1Num) / volume1CharlesNum;
      console.log(`T₂ calculado: ${t2} ${temperatureUnit}`);
      setTemperature2(formatValue(t2));
    }
    // Calcular V₁ se T₁, V₂ e T₂ forem fornecidos, mas V₁ não
    else if (isT1Present && isV2Present && isT2Present && !isV1Present) {
      console.log("Calculando V₁...");
      const v1 = (volume2CharlesNum * temperature1Num) / temperature2Num;
      console.log(`V₁ calculado: ${v1} ${volumeUnit}`);
      setVolume1Charles(formatValue(v1));
    }
    // Calcular T₁ se V₁, V₂ e T₂ forem fornecidos, mas T₁ não
    else if (isV1Present && isV2Present && isT2Present && !isT1Present) {
      console.log("Calculando T₁...");
      const t1 = (volume1CharlesNum * temperature2Num) / volume2CharlesNum;
      console.log(`T₁ calculado: ${t1} ${temperatureUnit}`);
      setTemperature1(formatValue(t1));
    }
  };

  useEffect(() => {
    if (inputValue && composto && massaMolar !== null) {
      handleConvert();
    }

    // Força a atualização da interface quando as unidades mudarem
    // Isso garante que a unidade exibida na seção de conversões seja atualizada
  }, [inputValue, fromUnit, toUnit, composto, massaMolar, handleConvert, massUnit, volumeUnit, quantityUnit, unitUpdateTrigger]);

  // Função para verificar se os valores atuais correspondem a algum preset
  const checkForPresetMatch = () => {
    // Converter os valores atuais para unidades base (K e atm) para comparação
    const currentTempInK = getTemperatureInKelvin(parseFloat(temperature), temperatureUnit);
    const currentPressInAtm = convertPressure(parseFloat(pressure), pressureUnit, 'atm');

    // Verificar se os valores correspondem ao preset STP (0°C, 1 atm)
    if (Math.abs(currentTempInK - 273.15) < 0.01 && Math.abs(currentPressInAtm - 1) < 0.01) {
      setSelectedPreset('STP');
      return;
    }

    // Verificar se os valores correspondem ao preset SATP (25°C, 1 atm)
    if (Math.abs(currentTempInK - 298.15) < 0.01 && Math.abs(currentPressInAtm - 1) < 0.01) {
      setSelectedPreset('SATP');
      return;
    }

    // Se não corresponder a nenhum preset, definir como custom
    setSelectedPreset('custom');
  };

  // Chamar as funções de cálculo quando os valores mudarem
  useEffect(() => {
    if (activeGasLaw === 'ideal' && !isTypingIdealGas) {
      calculateIdealGasLaw();
      // Verificar se os valores correspondem a algum preset
      checkForPresetMatch();
    }
  // eslint-disable-next-line react-hooks/exhaustive-deps
  }, [activeGasLaw, pressure, temperature, volumeValue, quantityValue, pressureUnit, temperatureUnit, volumeUnit, quantityUnit, isTypingIdealGas]);

  useEffect(() => {
    if (activeGasLaw === 'boyle' && !isTypingBoyle) {
      calculateBoyleLaw();
    }
  // eslint-disable-next-line react-hooks/exhaustive-deps
  }, [activeGasLaw, pressure1, volume1, pressure2, volume2, pressureUnit, volumeUnit, isTypingBoyle]);

  useEffect(() => {
    if (activeGasLaw === 'charles' && !isTypingCharles) {
      calculateCharlesLaw();
    }
  // eslint-disable-next-line react-hooks/exhaustive-deps
  }, [activeGasLaw, volume1Charles, temperature1, volume2Charles, temperature2, volumeUnit, temperatureUnit, isTypingCharles]);


  // Atualizar o input quando for número em notação científica
  const handleInputChange = (e) => {
    let value = e.target.value;

    // Remove a unidade se estiver presente no valor
    const unitStr = getSpecificUnit(fromUnit);
    if (value.endsWith(` ${unitStr}`)) {
      value = value.substring(0, value.length - unitStr.length - 1);
    }

    // Aceita números normais e notação científica (tanto e+/- quanto x 10^)
    if (value === '' ||
        /^[0-9]*\.?[0-9]*([eE][-+]?[0-9]*)?$/.test(value) ||
        /^[0-9]*\.?[0-9]* x 10\^[-+]?[0-9]*$/.test(value)) {

      // Converter de "Y x 10^X" para notação científica padrão se necessário
      if (/^[0-9]*\.?[0-9]* x 10\^[-+]?[0-9]*$/.test(value)) {
        const parts = value.split(' x 10^');
        const base = parts[0];
        const exponent = parts[1];
        value = `${base}e${exponent}`;
      }

      setInputValue(value); // Armazena o valor sem a unidade para cálculos

      // Se o valor for vazio, limpar os parâmetros
      if (value === '') {
        setMassValue('');
        setVolumeValue('');
        setQuantityValue('');
        setResult(null);
        setOriginalValueInGrams(null); // Limpa o valor original em gramas
      } else if (fromUnit === 'mass') {
        // Atualiza o valor original em gramas quando o input é alterado
        const numValue = parseFloat(value);
        if (!isNaN(numValue)) {
          const valueInGrams = convertMass(numValue, massUnit, 'g');
          setOriginalValueInGrams(valueInGrams);

          // Força a atualização do valor no painel de parâmetros
          setMassValue(formatValue(numValue));
        }
      }
    }
  };

  const handlePresetChange = (preset) => {
    if (preset === 'STP') {
      const tempInK = 273.15;
      const pressInAtm = 1;

      const convertedTemp = Number(convertTemperature(tempInK, 'K', temperatureUnit));
      const convertedPress = Number(convertPressure(pressInAtm, 'atm', pressureUnit));

      setTemperature(convertedTemp);
      setPressure(convertedPress);
      setSelectedPreset('STP');
    } else if (preset === 'SATP') {
      const tempInK = 298.15;
      const pressInAtm = 1;

      const convertedTemp = Number(convertTemperature(tempInK, 'K', temperatureUnit));
      const convertedPress = Number(convertPressure(pressInAtm, 'atm', pressureUnit));

      setTemperature(convertedTemp);
      setPressure(convertedPress);
      setSelectedPreset('SATP');
    }
  };

  // Atualize o texto dos botões para mostrar os valores nas unidades atuais
  const getPresetButtonText = (preset) => {
    if (preset === 'STP') {
      const temp = convertTemperature(273.15, 'K', temperatureUnit);
      const press = convertPressure(1, 'atm', pressureUnit);
      return `STP (${formatValue(temp)}${temperatureUnit === 'C' ? '°' : ''}${temperatureUnit}, ${formatValue(press)} ${pressureUnit})`;
    } else if (preset === 'SATP') {
      const temp = convertTemperature(298.15, 'K', temperatureUnit);
      const press = convertPressure(1, 'atm', pressureUnit);
      return `SATP (${formatValue(temp)}${temperatureUnit === 'C' ? '°' : ''}${temperatureUnit}, ${formatValue(press)} ${pressureUnit})`;
    }
    return '';
  };

  // Function to restore a conversion from history (não utilizada atualmente)
  // eslint-disable-next-line no-unused-vars
  const restoreConversion = (item) => {
    setFromUnit(item.fromUnit);
    setToUnit(item.toUnit);
    setInputValue(item.inputValue);
  };

  // Funções de conversão de unidades
  const convertPressure = (value, fromUnit, toUnit) => {
    if (fromUnit === toUnit) return value;

    // Primeiro converte para atm (unidade base)
    let atm;
    switch (fromUnit) {
      case 'atm':
        atm = value;
        break;
      case 'mmHg':
        atm = value / 760;
        break;
      case 'Pa':
        atm = value / 101325; // Pascal para atm
        break;
      case 'bar':
        atm = value / 1.01325;
        break;
      default:
        return value;
    }

    // Depois converte de atm para a unidade desejada
    switch (toUnit) {
      case 'atm':
        return atm;
      case 'mmHg':
        return atm * 760;
      case 'Pa':
        return atm * 101325; // atm para Pascal
      case 'bar':
        return atm * 1.01325;
      default:
        return atm;
    }
  };

  const convertTemperature = (value, fromUnit, toUnit) => {
    if (fromUnit === toUnit) return value;

    // Primeiro converte para Kelvin
    let kelvin;
    switch (fromUnit) {
      case 'K':
        kelvin = value;
        break;
      case 'C':
        kelvin = value + 273.15;
        break;
      case 'F':
        kelvin = (value + 459.67) * (5/9);
        break;
      default:
        return value;
    }

    // Depois converte de Kelvin para a unidade desejada
    switch (toUnit) {
      case 'K':
        return kelvin;
      case 'C':
        return kelvin - 273.15;
      case 'F':
        return kelvin * (9/5) - 459.67;
      default:
        return kelvin;
    }
  };









  // Função convertVolume removida - agora importada de chemistryUtils.js

  // Função convertQuantity removida - agora importada de chemistryUtils.js

  // Function to format temperature with the correct symbol
  const formatTemperature = (value, unit) => {
    const formattedValue = formatValue(value);
    const symbol = unit === 'C' ? '°C' : unit;
    return `${formattedValue} ${symbol}`;
  };

  return (
    <div className="App">
      <div className="panel parameters-panel" ref={parametersRef}>
        <div className="panel-block">
          <div className="panel-block-title">Parameters</div>
          <div className="parameter-item">
            <span className="parameter-label">Compound:</span>
            <span className="parameter-value">{composto || ' '}</span>
          </div>
          <div className="parameter-item">
            <span className="parameter-label">Molar Mass:</span>
            <span className="parameter-value">
              {massaMolar ? `${formatValue(massaMolar)} g/mol` : ' '}
            </span>
          </div>
          <div className="parameter-item">
            <span className="parameter-label">Mass:</span>
            <span className="parameter-value">
              {massValue ? `${massValue} ${massUnit}` : ' '}
            </span>
          </div>
          <div className="parameter-item">
            <span className="parameter-label">Volume:</span>
            <span className="parameter-value">
              {volumeValue ? `${volumeValue} ${volumeUnit}` : ' '}
            </span>
          </div>
          <div className="parameter-item">
            <span className="parameter-label">Quantity:</span>
            <span className="parameter-value">
              {quantityValue ? `${quantityValue} ${quantityUnit}` : ' '}
            </span>
          </div>
          <div className="parameter-item">
            <span className="parameter-label">Temperature:</span>
            <span className="parameter-value">
              {formatTemperature(temperature, temperatureUnit)}
            </span>
          </div>
          <div className="parameter-item">
            <span className="parameter-label">Pressure:</span>
            <span className="parameter-value">
              {`${formatValue(pressure)} ${pressureUnit}`}
            </span>
          </div>
        </div>
      </div>

      <div className="panel units-panel">
        <div className="panel-block">
          <div className="panel-block-title">Units</div>
          <div className="parameter-item">
            <span className="parameter-label">Mass:</span>
            <span className="parameter-value">
            <Select
              value={unitOptions.mass.find(option => option.value === massUnit)}
              onChange={(option) => {
                const newUnit = option.value;
                // Atualiza a unidade
                setMassUnit(newUnit);

                // Limpar o valor no painel de parâmetros quando mudar a unidade
                // sem ter um valor de entrada do usuário
                setMassValue('');

                // Força a atualização da interface
                setUnitUpdateTrigger(prev => prev + 1);

                // Atualiza o valor de entrada se a unidade atual for 'mass'
                if (fromUnit === 'mass') {
                  // Converte o valor de entrada para a nova unidade
                  if (inputValue) {
                    const value = parseFloat(inputValue);
                    if (!isNaN(value)) {
                      // Se não temos o valor original em gramas, calcule-o agora
                      if (originalValueInGrams === null) {
                        // Converte o valor atual para gramas
                        const valueInGrams = convertMass(value, massUnit, 'g');
                        setOriginalValueInGrams(valueInGrams);
                      }

                      // Usa o valor original em gramas para converter para a nova unidade
                      const valueToConvert = originalValueInGrams !== null ? originalValueInGrams : convertMass(value, massUnit, 'g');
                      const convertedValue = convertMass(valueToConvert, 'g', newUnit);

                      // Verifica se o valor é muito grande ou muito pequeno para usar notação científica
                      if (Math.abs(convertedValue) < 0.001 || Math.abs(convertedValue) > 999 ||
                          (Math.abs(convertedValue) > 0 && Math.abs(convertedValue) < 0.000000001)) {
                        // Usa a função formatScientific para formatar no formato Y x 10^X
                        setInputValue(formatScientific(convertedValue));
                      } else {
                        // Para valores normais, usa toString()
                        setInputValue(convertedValue.toString());
                      }
                    }
                  }
                }

                // Força uma atualização do resultado para refletir a nova unidade
                handleConvert();
              }}
              options={unitOptions.mass}
              styles={unitSelectStyles}
              isSearchable={false}
              menuPortalTarget={document.body}
              menuPosition="absolute"
              components={{
                IndicatorSeparator: null,
                // PERSONALIZAÇÃO: Componente personalizado para a seta do dropdown
                // Você pode modificar:
                // - position: 'absolute' - Posicionamento absoluto
                // - left: '5px' - Distância da esquerda
                // - opacity: 0.7 - Transparência (0 = invisível, 1 = opaco)
                // - O caractere ▼ pode ser substituído por outro símbolo
                DropdownIndicator: ({ selectProps }) => (
                  <span style={{
                    position: 'absolute',
                    left: '5px',
                    opacity: 0.7,
                    transform: selectProps.menuIsOpen ? 'rotate(180deg)' : 'rotate(0deg)',
                    transition: 'transform 0.2s ease',
                    display: 'inline-block'
                  }}>▼</span>
                )
              }}
            />
          </span>
        </div>
        <div className="parameter-item">
          <span className="parameter-label">Volume:</span>
          <span className="parameter-value">
            <Select
              value={unitOptions.volume.find(option => option.value === volumeUnit)}
              onChange={(option) => {
                const newUnit = option.value;
                // Atualiza a unidade
                setVolumeUnit(newUnit);

                // Limpar o valor no painel de parâmetros quando mudar a unidade
                // sem ter um valor de entrada do usuário
                setVolumeValue('');

                // Força a atualização da interface
                setUnitUpdateTrigger(prev => prev + 1);

                // Atualiza o valor de entrada se a unidade atual for 'volume'
                if (fromUnit === 'volume') {
                  // Converte o valor de entrada para a nova unidade
                  if (inputValue) {
                    const value = parseFloat(inputValue);
                    if (!isNaN(value)) {
                      // Converte o valor para a nova unidade
                      const convertedValue = convertVolume(value, volumeUnit, newUnit);
                      // Verifica se o valor é muito grande ou muito pequeno para usar notação científica
                      if (Math.abs(convertedValue) < 0.001 || Math.abs(convertedValue) > 999 ||
                          (Math.abs(convertedValue) > 0 && Math.abs(convertedValue) < 0.000000001)) {
                        // Usa a função formatScientific para formatar no formato Y x 10^X
                        setInputValue(formatScientific(convertedValue));
                      } else {
                        // Para valores normais, usa toString()
                        setInputValue(convertedValue.toString());
                      }
                    }
                  }
                }

                // Força uma atualização do resultado para refletir a nova unidade
                handleConvert();
              }}
              options={unitOptions.volume}
              styles={unitSelectStyles}
              isSearchable={false}
              menuPortalTarget={document.body}
              menuPosition="absolute"
              components={{
                IndicatorSeparator: null,
                DropdownIndicator: ({ selectProps }) => (
                  <span style={{
                    position: 'absolute',
                    left: '5px',
                    opacity: 0.7,
                    transform: selectProps.menuIsOpen ? 'rotate(180deg)' : 'rotate(0deg)',
                    transition: 'transform 0.2s ease',
                    display: 'inline-block'
                  }}>▼</span>
                )
              }}
            />
          </span>
        </div>
        <div className="parameter-item">
          <span className="parameter-label">Quantity:</span>
          <span className="parameter-value">
            <Select
              value={[
                { value: 'mol', label: 'Mols (mol)' },
                { value: 'mmol', label: 'Milimols (mmol)' }
              ].find(option => option.value === quantityUnit)}
              onChange={(option) => {
                const newUnit = option.value;
                // Atualiza a unidade
                setQuantityUnit(newUnit);

                // Limpar o valor no painel de parâmetros quando mudar a unidade
                // sem ter um valor de entrada do usuário
                setQuantityValue('');

                // Força a atualização da interface
                setUnitUpdateTrigger(prev => prev + 1);

                // Atualiza o valor de entrada se a unidade atual for 'moles'
                if (fromUnit === 'moles') {
                  // Converte o valor de entrada para a nova unidade
                  if (inputValue) {
                    const value = parseFloat(inputValue);
                    if (!isNaN(value)) {
                      // Converte o valor para a nova unidade
                      const convertedValue = convertQuantity(value, quantityUnit, newUnit);
                      // Verifica se o valor é muito grande ou muito pequeno para usar notação científica
                      if (Math.abs(convertedValue) < 0.001 || Math.abs(convertedValue) > 999 ||
                          (Math.abs(convertedValue) > 0 && Math.abs(convertedValue) < 0.000000001)) {
                        // Usa a função formatScientific para formatar no formato Y x 10^X
                        setInputValue(formatScientific(convertedValue));
                      } else {
                        // Para valores normais, usa toString()
                        setInputValue(convertedValue.toString());
                      }
                    }
                  }
                }

                // Força uma atualização do resultado para refletir a nova unidade
                handleConvert();
              }}
              options={[
                { value: 'mol', label: 'Mols (mol)' },
                { value: 'mmol', label: 'Milimols (mmol)' }
              ]}
              styles={unitSelectStyles}
              isSearchable={false}
              menuPortalTarget={document.body}
              menuPosition="absolute"
              components={{
                IndicatorSeparator: null,
                DropdownIndicator: ({ selectProps }) => (
                  <span style={{
                    position: 'absolute',
                    left: '5px',
                    opacity: 0.7,
                    transform: selectProps.menuIsOpen ? 'rotate(180deg)' : 'rotate(0deg)',
                    transition: 'transform 0.2s ease',
                    display: 'inline-block'
                  }}>▼</span>
                )
              }}
            />
          </span>
        </div>
        <div className="parameter-item">
          <span className="parameter-label">Concentration:</span>
          <span className="parameter-value">
            <Select
              value={unitOptions.concentration.find(option => option.value === concentrationUnit)}
              onChange={(option) => setConcentrationUnit(option.value)}
              options={unitOptions.concentration}
              styles={unitSelectStyles}
              isSearchable={false}
              menuPortalTarget={document.body}
              menuPosition="absolute"
              components={{
                IndicatorSeparator: null,
                DropdownIndicator: ({ selectProps }) => (
                  <span style={{
                    position: 'absolute',
                    left: '5px',
                    opacity: 0.7,
                    transform: selectProps.menuIsOpen ? 'rotate(180deg)' : 'rotate(0deg)',
                    transition: 'transform 0.2s ease',
                    display: 'inline-block'
                  }}>▼</span>
                )
              }}
            />
          </span>
        </div>
        <div className="parameter-item">
          <span className="parameter-label">Temperature:</span>
          <span className="parameter-value">
            <Select
              value={unitOptions.temperature.find(option => option.value === temperatureUnit)}
              onChange={(option) => {
                const newUnit = option.value;
                const currentValue = parseFloat(temperature);
                if (!isNaN(currentValue)) {
                  const convertedValue = convertTemperature(currentValue, temperatureUnit, newUnit);
                  // Formata o valor para exibição com número adequado de casas decimais
                  const formattedValue = formatValue(convertedValue);
                  setTemperature(parseFloat(formattedValue));
                }
                setTemperatureUnit(newUnit);
              }}
              options={unitOptions.temperature}
              styles={unitSelectStyles}
              isSearchable={false}
              menuPortalTarget={document.body}
              menuPosition="absolute"
              components={{
                IndicatorSeparator: null,
                DropdownIndicator: ({ selectProps }) => (
                  <span style={{
                    position: 'absolute',
                    left: '5px',
                    opacity: 0.7,
                    transform: selectProps.menuIsOpen ? 'rotate(180deg)' : 'rotate(0deg)',
                    transition: 'transform 0.2s ease',
                    display: 'inline-block'
                  }}>▼</span>
                )
              }}
            />
          </span>
        </div>
        <div className="parameter-item">
          <span className="parameter-label">Pressure:</span>
          <span className="parameter-value">
            <Select
              value={unitOptions.pressure.find(option => option.value === pressureUnit)}
              onChange={(option) => {
                const newUnit = option.value;
                const currentValue = parseFloat(pressure);
                if (!isNaN(currentValue)) {
                  const convertedValue = convertPressure(currentValue, pressureUnit, newUnit);
                  // Formata o valor para exibição com número adequado de casas decimais
                  const formattedValue = formatValue(convertedValue);
                  setPressure(parseFloat(formattedValue));
                }
                setPressureUnit(newUnit);
              }}
              options={unitOptions.pressure}
              styles={unitSelectStyles}
              isSearchable={false}
              menuPortalTarget={document.body}
              menuPosition="absolute"
              components={{
                IndicatorSeparator: null,
                DropdownIndicator: ({ selectProps }) => (
                  <span style={{
                    position: 'absolute',
                    left: '5px',
                    opacity: 0.7,
                    transform: selectProps.menuIsOpen ? 'rotate(180deg)' : 'rotate(0deg)',
                    transition: 'transform 0.2s ease',
                    display: 'inline-block'
                  }}>▼</span>
                )
              }}
            />
          </span>
        </div>
      </div>

      <div className="main-content-wrapper">
        <Squares
          speed={0.3}
          squareSize={30}
          direction="diagonal"
          borderColor="#222"
          hoverFillColor="#222"
          className="background-squares"
        />
        <div className="content-container">
        {/* AQUI: Mude o título principal da aplicação */}
        <h1>Chemistry Calculator</h1>

        {/* Compound Block */}
        <div className="calculator-block calculo-container">
          {/* AQUI: Mude o título da seção de composto */}
          <h2>Compound</h2>
          <div className="input-group">
            <input
              type="text"
              value={composto}
              onChange={(e) => setComposto(e.target.value)}
              placeholder="Enter chemical formula"
              className="chemical-input"
            />
          </div>

          {/* Carousel para exibição das informações do composto */}
          {composto && (
            <CompoundCarousel
              composto={composto}
              massaMolar={massaMolar}
              elementosInfo={elementosInfo}
              formatValue={formatValue}
              removeTrailingZeros={removeTrailingZeros}
            />
          )}
        </div>

        {/* Carousel com menu fora do bloco e slides dentro */}
        <SimpleCarousel
            slides={[
              {
                id: 'conversions',
                title: 'Unit Conversions',
                component: (
                  <Conversions
                    unitOptions={unitOptions}
                    fromUnit={fromUnit}
                    setFromUnit={setFromUnit}
                    toUnit={toUnit}
                    setToUnit={setToUnit}
                    inputValue={inputValue}
                    handleInputChange={handleInputChange}
                    result={result}
                    error={error}
                    isLoading={isLoading}
                    handleConvert={handleConvert}
                    handleSwapUnits={handleSwapUnits}
                    isRotating={isRotating}
                    setInputValue={setInputValue}
                    setUnitUpdateTrigger={setUnitUpdateTrigger}
                    getSpecificUnit={getSpecificUnit}
                  />
                )
              },
              {
                id: 'gasLaws',
                title: 'Gas Laws',
                component: (
                  <GasLaws
                    activeGasLaw={activeGasLaw}
                    setActiveGasLaw={setActiveGasLaw}
                    R_IDEAL_GAS_CONSTANT={R_IDEAL_GAS_CONSTANT}
                    quantityValue={quantityValue}
                    setQuantityValue={setQuantityValue}
                    volumeValue={volumeValue}
                    setVolumeValue={setVolumeValue}
                    pressure={pressure}
                    setPressure={setPressure}
                    temperature={temperature}
                    setTemperature={setTemperature}
                    quantityUnit={quantityUnit}
                    volumeUnit={volumeUnit}
                    pressureUnit={pressureUnit}
                    temperatureUnit={temperatureUnit}
                    setLastIdealGasField={setLastIdealGasField}
                    setIsTypingIdealGas={setIsTypingIdealGas}
                    calculateIdealGasLaw={calculateIdealGasLaw}
                    selectedPreset={selectedPreset}
                    handlePresetChange={handlePresetChange}
                    getPresetButtonText={getPresetButtonText}
                    setSelectedPreset={setSelectedPreset}
                    checkForPresetMatch={checkForPresetMatch}
                    pressure1={pressure1}
                    setPressure1={setPressure1}
                    volume1={volume1}
                    setVolume1={setVolume1}
                    pressure2={pressure2}
                    setPressure2={setPressure2}
                    volume2={volume2}
                    setVolume2={setVolume2}
                    setLastBoyleField={setLastBoyleField}
                    setIsTypingBoyle={setIsTypingBoyle}
                    calculateBoyleLaw={calculateBoyleLaw}
                    volume1Charles={volume1Charles}
                    setVolume1Charles={setVolume1Charles}
                    temperature1={temperature1}
                    setTemperature1={setTemperature1}
                    volume2Charles={volume2Charles}
                    setVolume2Charles={setVolume2Charles}
                    temperature2={temperature2}
                    setTemperature2={setTemperature2}
                    setLastCharlesField={setLastCharlesField}
                    setIsTypingCharles={setIsTypingCharles}
                    calculateCharlesLaw={calculateCharlesLaw}
                  />
                )
              },
              {
                id: 'conditions',
                title: 'Gas Conditions',
                component: (
                  <Conditions
                    pressure={pressure}
                    setPressure={setPressure}
                    temperature={temperature}
                    setTemperature={setTemperature}
                    pressureUnit={pressureUnit}
                    temperatureUnit={temperatureUnit}
                    selectedPreset={selectedPreset}
                    setSelectedPreset={setSelectedPreset}
                    handlePresetChange={handlePresetChange}
                    getPresetButtonText={getPresetButtonText}
                  />
                )
              },
              {
                id: 'chemistryTools',
                title: 'Chemistry Tools',
                component: <ChemistryToolsOp />
              },
              {
                id: 'chemicalEquations',
                title: 'Chemical Equations',
                component: <ChemicalEquationsOp />
              }
            ]}
          />

          <Dock
            items={items}
            panelHeight={70}
            baseItemSize={50}
            magnification={70}
          />

          {/* Componente de modo debug */}
          <DebugMode />
        </div>
      </div>
    </div>
  );
}

export default Chemistry;
