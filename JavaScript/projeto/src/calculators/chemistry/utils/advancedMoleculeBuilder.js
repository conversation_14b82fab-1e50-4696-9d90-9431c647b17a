/**
 * Sistema Avançado de Construção de Moléculas 3D
 * Suporta nomenclatura IUPAC complexa e geometria molecular precisa
 */

// Constantes para geometria molecular
const BOND_LENGTHS = {
  'C-C': 1.54,
  'C-H': 1.09,
  'C-O': 1.43,
  'O-H': 0.96,
  'C=C': 1.34,
  'C≡C': 1.20
};

const BOND_ANGLES = {
  tetrahedral: 109.47,
  trigonal: 120,
  linear: 180
};

// Parser de nomenclatura IUPAC avançado
export function parseIUPACNomenclature(name) {
  const normalized = name.toLowerCase().trim();

  // Casos especiais para nomes comuns
  const directMappings = {
    'methane': { mainChainLength: 1, functionalGroup: { type: 'alkane' }, substituents: [], positions: [] },
    'ethane': { mainChainLength: 2, functionalGroup: { type: 'alkane' }, substituents: [], positions: [] },
    'propane': { mainChainLength: 3, functionalGroup: { type: 'alkane' }, substituents: [], positions: [] },
    'butane': { mainChainLength: 4, functionalGroup: { type: 'alkane' }, substituents: [], positions: [] },
    'pentane': { mainChainLength: 5, functionalGroup: { type: 'alkane' }, substituents: [], positions: [] },
    'hexane': { mainChainLength: 6, functionalGroup: { type: 'alkane' }, substituents: [], positions: [] },
    'heptane': { mainChainLength: 7, functionalGroup: { type: 'alkane' }, substituents: [], positions: [] },
    'octane': { mainChainLength: 8, functionalGroup: { type: 'alkane' }, substituents: [], positions: [] },
    'nonane': { mainChainLength: 9, functionalGroup: { type: 'alkane' }, substituents: [], positions: [] },
    'decane': { mainChainLength: 10, functionalGroup: { type: 'alkane' }, substituents: [], positions: [] }
  };

  if (directMappings[normalized]) {
    return { ...directMappings[normalized], originalName: name };
  }

  // Parser para nomenclaturas complexas
  let workingName = normalized;

  // Detectar e extrair posições e substituintes
  const positions = [];
  const substituents = [];

  // Padrões para substituintes com posições (ex: 2-methyl, 2,3-dimethyl)
  const substituentPatterns = {
    'methyl': { formula: 'CH3', type: 'alkyl' },
    'ethyl': { formula: 'C2H5', type: 'alkyl' },
    'propyl': { formula: 'C3H7', type: 'alkyl' },
    'butyl': { formula: 'C4H9', type: 'alkyl' },
    'hydroxy': { formula: 'OH', type: 'alcohol' },
    'amino': { formula: 'NH2', type: 'amine' }
  };

  // Detectar padrões como "2-methyl", "2,3-dimethyl", etc.
  for (const [subName, subInfo] of Object.entries(substituentPatterns)) {
    // Padrão para detectar posições e multiplicadores
    const patterns = [
      new RegExp(`(\\d+(?:,\\d+)*)-(?:(di|tri|tetra)?${subName})`, 'g'),
      new RegExp(`(?:(di|tri|tetra)?${subName})`, 'g')
    ];

    // Processar cada padrão separadamente para evitar closure issues
    for (let patternIndex = 0; patternIndex < patterns.length; patternIndex++) {
      const pattern = patterns[patternIndex];
      let match;
      let currentWorkingName = workingName; // Cópia local

      while ((match = pattern.exec(currentWorkingName)) !== null) {
        const positionStr = match[1];
        const multiplier = match[2];

        if (positionStr) {
          const positionList = positionStr.split(',').map(Number);
          positions.push(positionList);

          // Adicionar substituinte para cada posição
          positionList.forEach(() => {
            substituents.push({ name: subName, ...subInfo });
          });
        } else if (multiplier) {
          // Lidar com multiplicadores (di, tri, tetra)
          const count = { 'di': 2, 'tri': 3, 'tetra': 4 }[multiplier] || 1;
          for (let i = 0; i < count; i++) {
            substituents.push({ name: subName, ...subInfo });
          }
        } else {
          substituents.push({ name: subName, ...subInfo });
        }

        // Remover do nome para análise posterior
        currentWorkingName = currentWorkingName.replace(match[0], '');
      }

      workingName = currentWorkingName; // Atualizar workingName
    }
  }

  // Detectar cadeia principal
  const chainPrefixes = {
    'meth': 1, 'eth': 2, 'prop': 3, 'but': 4, 'pent': 5,
    'hex': 6, 'hept': 7, 'oct': 8, 'non': 9, 'dec': 10
  };

  let mainChainLength = 0;
  let functionalGroup = null;

  for (const [prefix, length] of Object.entries(chainPrefixes)) {
    if (workingName.includes(prefix)) {
      mainChainLength = length;
      break;
    }
  }

  // Detectar grupo funcional
  const functionalGroups = {
    'ane': { type: 'alkane', suffix: 'ane' },
    'ene': { type: 'alkene', suffix: 'ene' },
    'yne': { type: 'alkyne', suffix: 'yne' },
    'ol': { type: 'alcohol', suffix: 'ol' },
    'al': { type: 'aldehyde', suffix: 'al' },
    'one': { type: 'ketone', suffix: 'one' }
  };

  for (const [suffix, info] of Object.entries(functionalGroups)) {
    if (workingName.endsWith(suffix)) {
      functionalGroup = info;
      break;
    }
  }

  // Se não encontrou grupo funcional, assumir alcano
  if (!functionalGroup && mainChainLength > 0) {
    functionalGroup = { type: 'alkane', suffix: 'ane' };
  }

  return {
    mainChainLength,
    functionalGroup,
    substituents,
    positions,
    originalName: name
  };
}

// Construtor de esqueleto de carbono com geometria tetraédrica correta
export function buildCarbonSkeleton(chainLength, branchInfo = []) {
  const atoms = [];
  const bonds = [];
  
  if (chainLength === 0) return { atoms, bonds };
  
  // Construir cadeia principal com ângulos tetraédricos
  for (let i = 0; i < chainLength; i++) {
    const position = calculateCarbonPosition(i, chainLength);
    atoms.push({
      element: 'C',
      x: position.x,
      y: position.y,
      z: position.z,
      index: i,
      type: 'main_chain'
    });
    
    // Adicionar ligação com carbono anterior
    if (i > 0) {
      bonds.push({
        from: i - 1,
        to: i,
        order: 1,
        type: 'C-C'
      });
    }
  }
  
  return { atoms, bonds };
}

// Calcular posição de carbono com geometria tetraédrica real
function calculateCarbonPosition(index, totalCarbons) {
  const ccBondLength = BOND_LENGTHS['C-C'];
  const tetraAngle = BOND_ANGLES.tetrahedral * Math.PI / 180;

  // Cache para evitar recálculos
  if (!calculateCarbonPosition.cache) {
    calculateCarbonPosition.cache = {};
  }

  const cacheKey = `${index}-${totalCarbons}`;
  if (calculateCarbonPosition.cache[cacheKey]) {
    return calculateCarbonPosition.cache[cacheKey];
  }

  let result;

  if (index === 0) {
    result = { x: 0, y: 0, z: 0 };
  } else if (index === 1) {
    result = { x: ccBondLength, y: 0, z: 0 };
  } else {
    // Construir posições iterativamente para evitar recursão
    const positions = [
      { x: 0, y: 0, z: 0 },
      { x: ccBondLength, y: 0, z: 0 }
    ];

    for (let i = 2; i <= index; i++) {
      const prevPos = positions[i - 1];
      const prevPrevPos = positions[i - 2];

      // Vetor da ligação anterior
      const prevVector = {
        x: prevPos.x - prevPrevPos.x,
        y: prevPos.y - prevPrevPos.y,
        z: prevPos.z - prevPrevPos.z
      };

      // Normalizar
      const length = Math.sqrt(prevVector.x ** 2 + prevVector.y ** 2 + prevVector.z ** 2);
      const unitVector = {
        x: prevVector.x / length,
        y: prevVector.y / length,
        z: prevVector.z / length
      };

      // Calcular nova direção com ângulo tetraédrico
      // Criar estrutura zigzag alternando direções
      const zigzagAngle = (i % 2 === 0) ? tetraAngle : -tetraAngle;

      // Vetor perpendicular para rotação
      const perpVector = {
        x: -unitVector.y,
        y: unitVector.x,
        z: 0
      };

      // Normalizar vetor perpendicular
      const perpLength = Math.sqrt(perpVector.x ** 2 + perpVector.y ** 2 + perpVector.z ** 2);
      if (perpLength > 0) {
        perpVector.x /= perpLength;
        perpVector.y /= perpLength;
        perpVector.z /= perpLength;
      }

      // Aplicar rotação tetraédrica
      const cos = Math.cos(zigzagAngle);
      const sin = Math.sin(zigzagAngle);

      const newDirection = {
        x: unitVector.x * cos + perpVector.x * sin,
        y: unitVector.y * cos + perpVector.y * sin,
        z: unitVector.z * cos + perpVector.z * sin + 0.3 * (i % 2 ? 1 : -1)
      };

      // Normalizar nova direção
      const newLength = Math.sqrt(newDirection.x ** 2 + newDirection.y ** 2 + newDirection.z ** 2);
      newDirection.x /= newLength;
      newDirection.y /= newLength;
      newDirection.z /= newLength;

      positions.push({
        x: prevPos.x + ccBondLength * newDirection.x,
        y: prevPos.y + ccBondLength * newDirection.y,
        z: prevPos.z + ccBondLength * newDirection.z
      });
    }

    result = positions[index];
  }

  calculateCarbonPosition.cache[cacheKey] = result;
  return result;
}

// Função para rotacionar vetor (para uso futuro)
// eslint-disable-next-line no-unused-vars
function rotateVector(vector, axis, angle) {
  const cos = Math.cos(angle);
  const sin = Math.sin(angle);
  const oneMinusCos = 1 - cos;

  // Fórmula de rotação de Rodrigues
  return {
    x: vector.x * (cos + axis.x * axis.x * oneMinusCos) +
       vector.y * (axis.x * axis.y * oneMinusCos - axis.z * sin) +
       vector.z * (axis.x * axis.z * oneMinusCos + axis.y * sin),
    y: vector.x * (axis.y * axis.x * oneMinusCos + axis.z * sin) +
       vector.y * (cos + axis.y * axis.y * oneMinusCos) +
       vector.z * (axis.y * axis.z * oneMinusCos - axis.x * sin),
    z: vector.x * (axis.z * axis.x * oneMinusCos - axis.y * sin) +
       vector.y * (axis.z * axis.y * oneMinusCos + axis.x * sin) +
       vector.z * (cos + axis.z * axis.z * oneMinusCos)
  };
}

// Adicionar hidrogênios com posicionamento tetraédrico
export function addHydrogens(carbonSkeleton) {
  const { atoms, bonds } = carbonSkeleton;
  const newAtoms = [...atoms];
  const newBonds = [...bonds];
  
  atoms.forEach((carbon, carbonIndex) => {
    if (carbon.element !== 'C') return;
    
    // Calcular quantos hidrogênios este carbono precisa
    const carbonBonds = bonds.filter(bond => 
      bond.from === carbonIndex || bond.to === carbonIndex
    );
    
    const numCarbonBonds = carbonBonds.length;
    const numHydrogens = 4 - numCarbonBonds; // Carbono tetravalente
    
    if (numHydrogens <= 0) return;
    
    // Calcular posições tetraédricas para hidrogênios
    const hydrogenPositions = calculateHydrogenPositions(
      carbon, carbonIndex, atoms, bonds, numHydrogens
    );
    
    hydrogenPositions.forEach(pos => {
      const hydrogenIndex = newAtoms.length;
      newAtoms.push({
        element: 'H',
        x: pos.x,
        y: pos.y,
        z: pos.z,
        type: 'hydrogen'
      });
      
      newBonds.push({
        from: carbonIndex,
        to: hydrogenIndex,
        order: 1,
        type: 'C-H'
      });
    });
  });
  
  return { atoms: newAtoms, bonds: newBonds };
}

// Calcular posições tetraédricas para hidrogênios
function calculateHydrogenPositions(carbon, carbonIndex, allAtoms, allBonds, numHydrogens) {
  const chBondLength = BOND_LENGTHS['C-H'];
  const tetraAngle = BOND_ANGLES.tetrahedral * Math.PI / 180;
  
  // Encontrar direções das ligações C-C existentes
  const existingDirections = [];
  
  allBonds.forEach(bond => {
    let otherAtomIndex = -1;
    if (bond.from === carbonIndex) {
      otherAtomIndex = bond.to;
    } else if (bond.to === carbonIndex) {
      otherAtomIndex = bond.from;
    }
    
    if (otherAtomIndex >= 0 && allAtoms[otherAtomIndex].element === 'C') {
      const otherAtom = allAtoms[otherAtomIndex];
      const direction = {
        x: otherAtom.x - carbon.x,
        y: otherAtom.y - carbon.y,
        z: otherAtom.z - carbon.z
      };
      
      // Normalizar
      const length = Math.sqrt(direction.x ** 2 + direction.y ** 2 + direction.z ** 2);
      existingDirections.push({
        x: direction.x / length,
        y: direction.y / length,
        z: direction.z / length
      });
    }
  });
  
  // Gerar direções tetraédricas para hidrogênios
  const hydrogenPositions = [];
  
  for (let h = 0; h < numHydrogens; h++) {
    // Calcular direção tetraédrica que evita as ligações C-C existentes
    const angle = (h * 2 * Math.PI / numHydrogens) + (carbonIndex * Math.PI / 6);
    
    let direction = {
      x: Math.cos(angle) * Math.sin(tetraAngle),
      y: Math.sin(angle) * Math.sin(tetraAngle),
      z: Math.cos(tetraAngle) * (h % 2 ? 1 : -1)
    };
    
    // Ajustar para evitar sobreposição com ligações C-C
    existingDirections.forEach(ccDir => {
      const dotProduct = direction.x * ccDir.x + direction.y * ccDir.y + direction.z * ccDir.z;
      if (Math.abs(dotProduct) > 0.8) { // Muito próximo, ajustar
        direction.z += 0.5 * (h % 2 ? 1 : -1);
        // Renormalizar
        const newLength = Math.sqrt(direction.x ** 2 + direction.y ** 2 + direction.z ** 2);
        direction.x /= newLength;
        direction.y /= newLength;
        direction.z /= newLength;
      }
    });
    
    hydrogenPositions.push({
      x: carbon.x + chBondLength * direction.x,
      y: carbon.y + chBondLength * direction.y,
      z: carbon.z + chBondLength * direction.z
    });
  }
  
  return hydrogenPositions;
}

// Adicionar grupos funcionais e substituintes
export function addFunctionalGroups(molecule, parsedInfo) {
  let { atoms, bonds } = molecule;

  // Adicionar álcoois (-OH)
  if (parsedInfo.functionalGroup?.type === 'alcohol') {
    const result = addAlcoholGroup(atoms, bonds, parsedInfo);
    atoms = result.atoms;
    bonds = result.bonds;
  }

  // Adicionar substituintes alquílicos
  parsedInfo.substituents.forEach((substituent, index) => {
    if (substituent.type === 'alkyl') {
      const position = parsedInfo.positions[index] || [2]; // Posição padrão
      const result = addAlkylSubstituent(atoms, bonds, substituent, position[0] - 1);
      atoms = result.atoms;
      bonds = result.bonds;
    }
  });

  return { atoms, bonds };
}

// Adicionar grupo álcool (-OH)
function addAlcoholGroup(atoms, bonds, parsedInfo) {
  const newAtoms = [...atoms];
  const newBonds = [...bonds];

  // Encontrar posição do álcool (padrão: carbono 1)
  const alcoholPosition = 0; // Simplificado por agora
  const carbon = atoms[alcoholPosition];

  if (carbon && carbon.element === 'C') {
    // Remover um hidrogênio deste carbono
    const hydrogenIndex = newAtoms.findIndex((atom, index) =>
      atom.element === 'H' &&
      newBonds.some(bond =>
        (bond.from === alcoholPosition && bond.to === index) ||
        (bond.to === alcoholPosition && bond.from === index)
      )
    );

    if (hydrogenIndex >= 0) {
      // Remover hidrogênio
      newAtoms.splice(hydrogenIndex, 1);
      newBonds.splice(newBonds.findIndex(bond =>
        (bond.from === alcoholPosition && bond.to === hydrogenIndex) ||
        (bond.to === alcoholPosition && bond.from === hydrogenIndex)
      ), 1);

      // Ajustar índices das ligações
      newBonds.forEach(bond => {
        if (bond.from > hydrogenIndex) bond.from--;
        if (bond.to > hydrogenIndex) bond.to--;
      });

      // Adicionar oxigênio
      const oxygenIndex = newAtoms.length;
      const chBondLength = BOND_LENGTHS['C-O'];

      newAtoms.push({
        element: 'O',
        x: carbon.x + chBondLength,
        y: carbon.y,
        z: carbon.z + chBondLength * 0.5,
        type: 'alcohol_oxygen'
      });

      newBonds.push({
        from: alcoholPosition,
        to: oxygenIndex,
        order: 1,
        type: 'C-O'
      });

      // Adicionar hidrogênio do OH
      const ohHydrogenIndex = newAtoms.length;
      const ohBondLength = BOND_LENGTHS['O-H'];

      newAtoms.push({
        element: 'H',
        x: newAtoms[oxygenIndex].x + ohBondLength,
        y: newAtoms[oxygenIndex].y,
        z: newAtoms[oxygenIndex].z,
        type: 'alcohol_hydrogen'
      });

      newBonds.push({
        from: oxygenIndex,
        to: ohHydrogenIndex,
        order: 1,
        type: 'O-H'
      });
    }
  }

  return { atoms: newAtoms, bonds: newBonds };
}

// Adicionar substituinte alquílico
function addAlkylSubstituent(atoms, bonds, substituent, position) {
  const newAtoms = [...atoms];
  const newBonds = [...bonds];

  const carbon = atoms[position];
  if (!carbon || carbon.element !== 'C') {
    return { atoms: newAtoms, bonds: newBonds };
  }

  // Remover um hidrogênio deste carbono
  const hydrogenIndex = newAtoms.findIndex((atom, index) =>
    atom.element === 'H' &&
    newBonds.some(bond =>
      (bond.from === position && bond.to === index) ||
      (bond.to === position && bond.from === index)
    )
  );

  if (hydrogenIndex >= 0) {
    // Remover hidrogênio
    newAtoms.splice(hydrogenIndex, 1);
    newBonds.splice(newBonds.findIndex(bond =>
      (bond.from === position && bond.to === hydrogenIndex) ||
      (bond.to === position && bond.from === hydrogenIndex)
    ), 1);

    // Ajustar índices
    newBonds.forEach(bond => {
      if (bond.from > hydrogenIndex) bond.from--;
      if (bond.to > hydrogenIndex) bond.to--;
    });

    // Adicionar grupo metil (CH3) como exemplo
    if (substituent.name === 'methyl') {
      const methylCarbonIndex = newAtoms.length;
      const ccBondLength = BOND_LENGTHS['C-C'];

      // Adicionar carbono do metil
      newAtoms.push({
        element: 'C',
        x: carbon.x + ccBondLength * Math.cos(Math.PI / 3),
        y: carbon.y + ccBondLength * Math.sin(Math.PI / 3),
        z: carbon.z,
        type: 'methyl_carbon'
      });

      newBonds.push({
        from: position,
        to: methylCarbonIndex,
        order: 1,
        type: 'C-C'
      });

      // Adicionar 3 hidrogênios ao metil
      const chBondLength = BOND_LENGTHS['C-H'];
      for (let i = 0; i < 3; i++) {
        const angle = (i * 2 * Math.PI / 3) + Math.PI / 6;
        const hIndex = newAtoms.length;

        newAtoms.push({
          element: 'H',
          x: newAtoms[methylCarbonIndex].x + chBondLength * Math.cos(angle),
          y: newAtoms[methylCarbonIndex].y + chBondLength * Math.sin(angle),
          z: newAtoms[methylCarbonIndex].z + chBondLength * (i % 2 ? 0.5 : -0.5),
          type: 'methyl_hydrogen'
        });

        newBonds.push({
          from: methylCarbonIndex,
          to: hIndex,
          order: 1,
          type: 'C-H'
        });
      }
    }
  }

  return { atoms: newAtoms, bonds: newBonds };
}

// Função principal para gerar molécula completa
export function generateAdvancedMolecule(input) {
  // Tentar parser IUPAC primeiro
  const parsed = parseIUPACNomenclature(input);

  if (parsed.mainChainLength > 0) {
    // Construir esqueleto de carbono
    const skeleton = buildCarbonSkeleton(parsed.mainChainLength);

    // Adicionar hidrogênios
    const withHydrogens = addHydrogens(skeleton);

    // Adicionar grupos funcionais
    const withFunctionalGroups = addFunctionalGroups(withHydrogens, parsed);

    return {
      atoms: withFunctionalGroups.atoms,
      bonds: withFunctionalGroups.bonds,
      geometry: 'tetrahedral',
      bondAngle: BOND_ANGLES.tetrahedral,
      bondLength: BOND_LENGTHS['C-C'],
      moleculeInfo: parsed
    };
  }

  return null;
}

// Função para reconhecer nomes comuns e fórmulas
export function parseCommonNamesAndFormulas(input) {
  const normalized = input.toLowerCase().trim();

  // Nomes comuns diretos
  const commonMolecules = {
    'methane': 'CH4',
    'ethane': 'C2H6',
    'propane': 'C3H8',
    'butane': 'C4H10',
    'pentane': 'C5H12',
    'hexane': 'C6H14',
    'heptane': 'C7H16',
    'octane': 'C8H18',
    'nonane': 'C9H20',
    'decane': 'C10H22'
  };

  if (commonMolecules[normalized]) {
    return generateAdvancedMolecule(normalized);
  }

  // Se é uma fórmula molecular, converter para nome
  const formulaToName = {
    'ch4': 'methane',
    'c2h6': 'ethane',
    'c3h8': 'propane',
    'c4h10': 'butane',
    'c5h12': 'pentane',
    'c6h14': 'hexane',
    'c7h16': 'heptane',
    'c8h18': 'octane',
    'c9h20': 'nonane',
    'c10h22': 'decane'
  };

  if (formulaToName[normalized]) {
    return generateAdvancedMolecule(formulaToName[normalized]);
  }

  return null;
}
