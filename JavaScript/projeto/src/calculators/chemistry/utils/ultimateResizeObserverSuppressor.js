/**
 * Supressor ultra-agressivo para erros do ResizeObserver
 * Esta é a solução definitiva para eliminar completamente os erros do ResizeObserver
 */

// Função para detectar erros do ResizeObserver de forma mais abrangente
const isResizeObserverError = (error, stack, source) => {
  const errorStr = String(error || '').toLowerCase();
  const stackStr = String(stack || '').toLowerCase();
  const sourceStr = String(source || '').toLowerCase();
  
  const patterns = [
    'resizeobserver',
    'undelivered notifications',
    'loop completed',
    'loop limit exceeded',
    'resize observer loop',
    'handleerror',
    'observer loop',
    '110072', // Linha específica do bundle
    '110091', // Linha específica do bundle
    'bundle.js:110072',
    'bundle.js:110091'
  ];
  
  return patterns.some(pattern => 
    errorStr.includes(pattern) || 
    stackStr.includes(pattern) || 
    sourceStr.includes(pattern)
  );
};

// Supressão definitiva
export const ultimateResizeObserverSuppression = () => {
  console.log('🚀 Iniciando supressão definitiva do ResizeObserver...');

  // 1. Interceptar console.error de forma mais agressiva
  const originalConsoleError = console.error;
  console.error = function(...args) {
    const message = args.join(' ');
    if (isResizeObserverError(message)) {
      return; // Silenciar completamente
    }
    return originalConsoleError.apply(console, args);
  };

  // 2. Interceptar window.onerror de forma mais específica
  const originalWindowError = window.onerror;
  window.onerror = function(message, source, lineno, colno, error) {
    if (isResizeObserverError(message, error?.stack, source) || 
        lineno === 110072 || lineno === 110091) {
      return true; // Prevenir propagação
    }
    if (originalWindowError) {
      return originalWindowError.call(this, message, source, lineno, colno, error);
    }
    return false;
  };

  // 3. Interceptar addEventListener para eventos de erro
  const originalAddEventListener = EventTarget.prototype.addEventListener;
  EventTarget.prototype.addEventListener = function(type, listener, options) {
    if (type === 'error') {
      const wrappedListener = function(event) {
        if (isResizeObserverError(event.message, event.error?.stack, event.filename) ||
            event.lineno === 110072 || event.lineno === 110091) {
          event.preventDefault();
          event.stopPropagation();
          event.stopImmediatePropagation();
          return false;
        }
        if (typeof listener === 'function') {
          return listener.call(this, event);
        } else if (listener && typeof listener.handleEvent === 'function') {
          return listener.handleEvent(event);
        }
      };
      return originalAddEventListener.call(this, type, wrappedListener, options);
    }
    return originalAddEventListener.call(this, type, listener, options);
  };

  // 4. Interceptar unhandledrejection
  const originalUnhandledRejection = window.onunhandledrejection;
  window.onunhandledrejection = function(event) {
    const reason = event.reason;
    if (isResizeObserverError(reason?.message || reason, reason?.stack)) {
      event.preventDefault();
      return;
    }
    if (originalUnhandledRejection) {
      return originalUnhandledRejection.call(this, event);
    }
  };

  // 5. Interceptar o ResizeObserver diretamente
  if (window.ResizeObserver) {
    const OriginalResizeObserver = window.ResizeObserver;
    
    window.ResizeObserver = class SuppressedResizeObserver extends OriginalResizeObserver {
      constructor(callback) {
        const suppressedCallback = (entries, observer) => {
          try {
            // Usar requestAnimationFrame para evitar loops
            requestAnimationFrame(() => {
              try {
                callback(entries, observer);
              } catch (error) {
                if (!isResizeObserverError(error.message, error.stack)) {
                  throw error;
                }
                // Silenciar erros do ResizeObserver
              }
            });
          } catch (error) {
            if (!isResizeObserverError(error.message, error.stack)) {
              throw error;
            }
            // Silenciar erros do ResizeObserver
          }
        };
        
        super(suppressedCallback);
      }
    };
  }

  // 6. Interceptar erros específicos do React/bundle
  const originalErrorHandler = window.__REACT_ERROR_OVERLAY_GLOBAL_HOOK__?.onError;
  if (window.__REACT_ERROR_OVERLAY_GLOBAL_HOOK__) {
    window.__REACT_ERROR_OVERLAY_GLOBAL_HOOK__.onError = function(error) {
      if (isResizeObserverError(error.message, error.stack)) {
        return; // Silenciar
      }
      if (originalErrorHandler) {
        return originalErrorHandler.call(this, error);
      }
    };
  }

  // 7. Interceptar setTimeout/setInterval para capturar erros assíncronos
  const originalSetTimeout = window.setTimeout;
  window.setTimeout = function(callback, delay, ...args) {
    const wrappedCallback = function() {
      try {
        return callback.apply(this, arguments);
      } catch (error) {
        if (!isResizeObserverError(error.message, error.stack)) {
          throw error;
        }
        // Silenciar erros do ResizeObserver
      }
    };
    return originalSetTimeout.call(this, wrappedCallback, delay, ...args);
  };

  // 8. Interceptar requestAnimationFrame
  const originalRAF = window.requestAnimationFrame;
  window.requestAnimationFrame = function(callback) {
    const wrappedCallback = function(timestamp) {
      try {
        return callback(timestamp);
      } catch (error) {
        if (!isResizeObserverError(error.message, error.stack)) {
          throw error;
        }
        // Silenciar erros do ResizeObserver
      }
    };
    return originalRAF.call(this, wrappedCallback);
  };

  // 9. Interceptar MutationObserver (pode estar relacionado)
  if (window.MutationObserver) {
    const OriginalMutationObserver = window.MutationObserver;
    window.MutationObserver = class SuppressedMutationObserver extends OriginalMutationObserver {
      constructor(callback) {
        const suppressedCallback = (mutations, observer) => {
          try {
            callback(mutations, observer);
          } catch (error) {
            if (!isResizeObserverError(error.message, error.stack)) {
              throw error;
            }
            // Silenciar erros relacionados ao ResizeObserver
          }
        };
        super(suppressedCallback);
      }
    };
  }

  // 10. Interceptar erros do bundle específico
  const originalEval = window.eval;
  // eslint-disable-next-line no-eval
  window.eval = function(code) {
    try {
      // eslint-disable-next-line no-eval
      return originalEval.call(this, code);
    } catch (error) {
      if (isResizeObserverError(error.message, error.stack)) {
        return; // Silenciar
      }
      throw error;
    }
  };

  console.log('✅ Supressão definitiva do ResizeObserver ativada!');
  console.log('🔇 Todos os erros do ResizeObserver serão silenciados');
};

// Auto-aplicar a supressão
ultimateResizeObserverSuppression();

// Aplicar novamente após um delay para garantir
setTimeout(() => {
  ultimateResizeObserverSuppression();
}, 100);

// Aplicar quando o DOM estiver pronto
if (document.readyState === 'loading') {
  document.addEventListener('DOMContentLoaded', ultimateResizeObserverSuppression);
} else {
  ultimateResizeObserverSuppression();
}

export default ultimateResizeObserverSuppression;
