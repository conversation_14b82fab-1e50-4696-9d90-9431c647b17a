/**
 * Teste do Sistema Limpo de Construção de Moléculas
 */

import { generateCleanMolecule } from './cleanMoleculeBuilder.js';

// Função para testar o sistema limpo
export function testCleanMoleculeBuilder() {
  console.log('🧪 Testando Sistema Limpo de Construção de Moléculas');
  
  const testCases = [
    'methane',
    'ethane',
    'propane',
    'butane', 
    'pentane',
    'hexane',
    'ethyne',
    'C2H2',
    'C3H8',
    'C4H10'
  ];
  
  testCases.forEach(testCase => {
    console.log(`\n🔬 Testando: ${testCase}`);
    
    const molecule = generateCleanMolecule(testCase);
    if (molecule) {
      console.log(`✅ Molécula gerada com ${molecule.atoms.length} átomos e ${molecule.bonds.length} ligações`);
      
      const carbons = molecule.atoms.filter(a => a.element === 'C');
      const hydrogens = molecule.atoms.filter(a => a.element === 'H');
      
      console.log(`   Carbonos: ${carbons.length}, Hidrogênios: ${hydrogens.length}`);
      
      // Verificar valência dos carbonos
      let carbonValencyOK = true;
      carbons.forEach((carbon, index) => {
        const carbonBonds = molecule.bonds.filter(bond => 
          bond.from === index || bond.to === index
        );
        const totalBondOrder = carbonBonds.reduce((sum, bond) => sum + bond.order, 0);
        
        if (totalBondOrder !== 4) {
          console.log(`❌ Carbono ${index} tem valência ${totalBondOrder} (esperado: 4)`);
          carbonValencyOK = false;
        }
      });
      
      if (carbonValencyOK) {
        console.log('✅ Valência dos carbonos correta');
      }
      
      // Verificar valência dos hidrogênios
      let hydrogenValencyOK = true;
      hydrogens.forEach((hydrogen, hIndex) => {
        const realIndex = carbons.length + hIndex;
        const hydrogenBonds = molecule.bonds.filter(bond => 
          bond.from === realIndex || bond.to === realIndex
        );
        
        if (hydrogenBonds.length !== 1) {
          console.log(`❌ Hidrogênio ${realIndex} tem ${hydrogenBonds.length} ligações (esperado: 1)`);
          hydrogenValencyOK = false;
        }
      });
      
      if (hydrogenValencyOK) {
        console.log('✅ Valência dos hidrogênios correta');
      }
      
      // Verificar geometria do propano
      if (testCase === 'propane' && carbons.length >= 3) {
        const c1 = carbons[0];
        const c2 = carbons[1]; 
        const c3 = carbons[2];
        
        const v1 = { x: c1.x - c2.x, y: c1.y - c2.y, z: c1.z - c2.z };
        const v2 = { x: c3.x - c2.x, y: c3.y - c2.y, z: c3.z - c2.z };
        
        const dot = v1.x * v2.x + v1.y * v2.y + v1.z * v2.z;
        const mag1 = Math.sqrt(v1.x ** 2 + v1.y ** 2 + v1.z ** 2);
        const mag2 = Math.sqrt(v2.x ** 2 + v2.y ** 2 + v2.z ** 2);
        
        const angle = Math.acos(dot / (mag1 * mag2)) * 180 / Math.PI;
        console.log(`🔍 Ângulo C-C-C no propano: ${angle.toFixed(2)}° (esperado: ~109.47°)`);
        
        if (Math.abs(angle - 109.47) < 10) {
          console.log('✅ Ângulo tetraédrico correto');
        } else {
          console.log('❌ Ângulo tetraédrico incorreto');
        }
      }
      
      // Verificar etino
      if (testCase === 'ethyne' || testCase === 'c2h2') {
        if (hydrogens.length === 2) {
          console.log('✅ Etino tem 2 hidrogênios (correto)');
        } else {
          console.log(`❌ Etino tem ${hydrogens.length} hidrogênios (esperado: 2)`);
        }
        
        // Verificar ligação tripla
        const tripleBond = molecule.bonds.find(bond => bond.order === 3);
        if (tripleBond) {
          console.log('✅ Ligação tripla C≡C encontrada');
        } else {
          console.log('❌ Ligação tripla C≡C não encontrada');
        }
      }
      
    } else {
      console.log('❌ Falha na geração da molécula');
    }
  });
}

// Executar teste se este arquivo for importado
if (typeof window !== 'undefined') {
  window.testCleanMoleculeBuilder = testCleanMoleculeBuilder;
}
