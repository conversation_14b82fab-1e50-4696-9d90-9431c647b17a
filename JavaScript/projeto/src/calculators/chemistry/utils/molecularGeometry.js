import { PeriodicTableData } from '../constants/chemistryConstants.js';

/**
 * Biblioteca para cálculos precisos de geometria molecular
 * Baseada na teoria VSEPR e dados experimentais
 */

// Função para determinar a geometria molecular baseada na teoria VSEPR
export function determineVSEPRGeometry(centralAtom, bondingPairs, lonePairs) {
  const totalPairs = bondingPairs + lonePairs;
  const stericNumber = totalPairs;

  const geometries = {
    2: {
      0: { name: 'linear', angle: 180, description: 'Linear' },
    },
    3: {
      0: { name: 'trigonal_planar', angle: 120, description: 'Trigonal Planar' },
      1: { name: 'bent_trigonal', angle: 117, description: 'Bent (Trigonal)' },
    },
    4: {
      0: { name: 'tetrahedral', angle: 109.47, description: 'Tetrahedral' },
      1: { name: 'trigonal_pyramidal', angle: 107, description: 'Trigonal Pyramidal' },
      2: { name: 'bent_tetrahedral', angle: 104.5, description: 'Bent (Tetrahedral)' },
    },
    5: {
      0: { name: 'trigonal_bipyramidal', angle: 120, description: 'Trigonal Bipyramidal' },
      1: { name: 'see_saw', angle: 119, description: 'See-Saw' },
      2: { name: 'T_shaped', angle: 90, description: 'T-Shaped' },
      3: { name: 'linear', angle: 180, description: 'Linear' },
    },
    6: {
      0: { name: 'octahedral', angle: 90, description: 'Octahedral' },
      1: { name: 'square_pyramidal', angle: 90, description: 'Square Pyramidal' },
      2: { name: 'square_planar', angle: 90, description: 'Square Planar' },
    }
  };

  return geometries[stericNumber]?.[lonePairs] || { 
    name: 'unknown', 
    angle: 109.47, 
    description: 'Unknown Geometry' 
  };
}

// Função para calcular pares de elétrons solitários
export function calculateLonePairs(element, bondingElectrons) {
  const valenceElectrons = PeriodicTableData.valenceElectrons[element] || 0;
  const lonePairElectrons = valenceElectrons - bondingElectrons;
  return Math.max(0, Math.floor(lonePairElectrons / 2));
}

// Função para obter comprimento de ligação
export function getBondLength(atom1, atom2, bondOrder = 1) {
  const bondKey = `${atom1}-${atom2}`;
  const reverseBondKey = `${atom2}-${atom1}`;
  
  let baseLength = PeriodicTableData.bondLengths[bondKey] || 
                   PeriodicTableData.bondLengths[reverseBondKey];
  
  if (!baseLength) {
    // Calcular baseado nos raios covalentes
    const radius1 = PeriodicTableData.covalentRadius[atom1] || 1.0;
    const radius2 = PeriodicTableData.covalentRadius[atom2] || 1.0;
    baseLength = radius1 + radius2;
  }

  // Ajustar para ordem de ligação
  const bondOrderFactors = { 1: 1.0, 2: 0.87, 3: 0.78 };
  return baseLength * (bondOrderFactors[bondOrder] || 1.0);
}

// Função para gerar coordenadas 3D precisas
export function generatePrecise3DCoordinates(formula, elements) {
  if (!elements || elements.length === 0) return null;

  // Estruturas conhecidas com coordenadas experimentais precisas
  const preciseStructures = {
    'H2O': generateWaterStructure(),
    'CO2': generateCO2Structure(),
    'NH3': generateAmmoniaStructure(),
    'CH4': generateMethaneStructure(),
    'HCl': generateHClStructure(),
    'C2H6': generateEthaneStructure(),
    'C2H4': generateEtheneStructure(),
    'C2H2': generateEthyneStructure(),
    'SO2': generateSO2Structure(),
    'H2S': generateH2SStructure(),
    'PH3': generatePH3Structure(),
    'BF3': generateBF3Structure(),
    'SF6': generateSF6Structure(),
    'PCl5': generatePCl5Structure()
  };

  if (preciseStructures[formula]) {
    return preciseStructures[formula];
  }

  // Para moléculas desconhecidas, usar algoritmo VSEPR
  return generateVSEPRStructure(elements);
}

// Estrutura precisa da água (H2O)
function generateWaterStructure() {
  const bondLength = getBondLength('O', 'H');
  const bondAngle = PeriodicTableData.bondAngles.bent_tetrahedral * Math.PI / 180;
  
  return {
    atoms: [
      { element: 'O', x: 0, y: 0, z: 0 },
      { 
        element: 'H', 
        x: bondLength * Math.cos(bondAngle / 2), 
        y: bondLength * Math.sin(bondAngle / 2), 
        z: 0 
      },
      { 
        element: 'H', 
        x: bondLength * Math.cos(bondAngle / 2), 
        y: -bondLength * Math.sin(bondAngle / 2), 
        z: 0 
      }
    ],
    bonds: [
      { from: 0, to: 1, order: 1 },
      { from: 0, to: 2, order: 1 }
    ],
    geometry: 'bent_tetrahedral',
    bondAngle: 104.5,
    bondLength: bondLength
  };
}

// Estrutura precisa do CO2
function generateCO2Structure() {
  const bondLength = getBondLength('C', 'O', 2); // Ligação dupla
  
  return {
    atoms: [
      { element: 'C', x: 0, y: 0, z: 0 },
      { element: 'O', x: -bondLength, y: 0, z: 0 },
      { element: 'O', x: bondLength, y: 0, z: 0 }
    ],
    bonds: [
      { from: 0, to: 1, order: 2 },
      { from: 0, to: 2, order: 2 }
    ],
    geometry: 'linear',
    bondAngle: 180,
    bondLength: bondLength
  };
}

// Estrutura precisa da amônia (NH3)
function generateAmmoniaStructure() {
  const bondLength = getBondLength('N', 'H');
  const bondAngle = PeriodicTableData.bondAngles.trigonal_pyramidal * Math.PI / 180;
  const height = bondLength * Math.cos(bondAngle / 2);
  const radius = bondLength * Math.sin(bondAngle / 2);
  
  return {
    atoms: [
      { element: 'N', x: 0, y: 0, z: height * 0.3 },
      { element: 'H', x: radius, y: 0, z: -height * 0.7 },
      { element: 'H', x: -radius * 0.5, y: radius * Math.sqrt(3) / 2, z: -height * 0.7 },
      { element: 'H', x: -radius * 0.5, y: -radius * Math.sqrt(3) / 2, z: -height * 0.7 }
    ],
    bonds: [
      { from: 0, to: 1, order: 1 },
      { from: 0, to: 2, order: 1 },
      { from: 0, to: 3, order: 1 }
    ],
    geometry: 'trigonal_pyramidal',
    bondAngle: 107,
    bondLength: bondLength
  };
}

// Estrutura precisa do metano (CH4)
function generateMethaneStructure() {
  const bondLength = getBondLength('C', 'H');
  const tetrahedralAngle = Math.acos(-1/3); // ~109.47°
  
  return {
    atoms: [
      { element: 'C', x: 0, y: 0, z: 0 },
      { element: 'H', x: bondLength, y: 0, z: 0 },
      { 
        element: 'H', 
        x: bondLength * Math.cos(2 * Math.PI / 3), 
        y: bondLength * Math.sin(2 * Math.PI / 3), 
        z: 0 
      },
      { 
        element: 'H', 
        x: bondLength * Math.cos(4 * Math.PI / 3), 
        y: bondLength * Math.sin(4 * Math.PI / 3), 
        z: 0 
      },
      { 
        element: 'H', 
        x: 0, 
        y: 0, 
        z: bondLength * Math.sqrt(2/3) 
      }
    ],
    bonds: [
      { from: 0, to: 1, order: 1 },
      { from: 0, to: 2, order: 1 },
      { from: 0, to: 3, order: 1 },
      { from: 0, to: 4, order: 1 }
    ],
    geometry: 'tetrahedral',
    bondAngle: 109.47,
    bondLength: bondLength
  };
}

// Estrutura do HCl
function generateHClStructure() {
  const bondLength = getBondLength('H', 'Cl');
  
  return {
    atoms: [
      { element: 'H', x: 0, y: 0, z: 0 },
      { element: 'Cl', x: bondLength, y: 0, z: 0 }
    ],
    bonds: [
      { from: 0, to: 1, order: 1 }
    ],
    geometry: 'linear',
    bondAngle: 180,
    bondLength: bondLength
  };
}

// Estrutura do etano (C2H6)
function generateEthaneStructure() {
  const ccBondLength = getBondLength('C', 'C');
  const chBondLength = getBondLength('C', 'H');
  const bondAngle = PeriodicTableData.bondAngles.tetrahedral * Math.PI / 180;
  
  return {
    atoms: [
      { element: 'C', x: -ccBondLength/2, y: 0, z: 0 },
      { element: 'C', x: ccBondLength/2, y: 0, z: 0 },
      { element: 'H', x: -ccBondLength/2 - chBondLength * Math.cos(bondAngle), y: chBondLength * Math.sin(bondAngle), z: 0 },
      { element: 'H', x: -ccBondLength/2 - chBondLength * Math.cos(bondAngle), y: -chBondLength * Math.sin(bondAngle), z: 0 },
      { element: 'H', x: -ccBondLength/2, y: 0, z: chBondLength },
      { element: 'H', x: ccBondLength/2 + chBondLength * Math.cos(bondAngle), y: chBondLength * Math.sin(bondAngle), z: 0 },
      { element: 'H', x: ccBondLength/2 + chBondLength * Math.cos(bondAngle), y: -chBondLength * Math.sin(bondAngle), z: 0 },
      { element: 'H', x: ccBondLength/2, y: 0, z: chBondLength }
    ],
    bonds: [
      { from: 0, to: 1, order: 1 },
      { from: 0, to: 2, order: 1 }, { from: 0, to: 3, order: 1 }, { from: 0, to: 4, order: 1 },
      { from: 1, to: 5, order: 1 }, { from: 1, to: 6, order: 1 }, { from: 1, to: 7, order: 1 }
    ],
    geometry: 'tetrahedral',
    bondAngle: 109.47,
    bondLength: chBondLength
  };
}

// Estrutura do eteno (C2H4)
function generateEtheneStructure() {
  const ccBondLength = getBondLength('C', 'C', 2); // Ligação dupla
  const chBondLength = getBondLength('C', 'H');
  const bondAngle = PeriodicTableData.bondAngles.trigonal_planar * Math.PI / 180;

  return {
    atoms: [
      { element: 'C', x: -ccBondLength/2, y: 0, z: 0 },
      { element: 'C', x: ccBondLength/2, y: 0, z: 0 },
      { element: 'H', x: -ccBondLength/2 - chBondLength * Math.cos(bondAngle), y: chBondLength * Math.sin(bondAngle), z: 0 },
      { element: 'H', x: -ccBondLength/2 - chBondLength * Math.cos(bondAngle), y: -chBondLength * Math.sin(bondAngle), z: 0 },
      { element: 'H', x: ccBondLength/2 + chBondLength * Math.cos(bondAngle), y: chBondLength * Math.sin(bondAngle), z: 0 },
      { element: 'H', x: ccBondLength/2 + chBondLength * Math.cos(bondAngle), y: -chBondLength * Math.sin(bondAngle), z: 0 }
    ],
    bonds: [
      { from: 0, to: 1, order: 2 },
      { from: 0, to: 2, order: 1 }, { from: 0, to: 3, order: 1 },
      { from: 1, to: 4, order: 1 }, { from: 1, to: 5, order: 1 }
    ],
    geometry: 'trigonal_planar',
    bondAngle: 120,
    bondLength: chBondLength
  };
}

// Estrutura do etino (C2H2)
function generateEthyneStructure() {
  const ccBondLength = getBondLength('C', 'C', 3); // Ligação tripla
  const chBondLength = getBondLength('C', 'H');

  return {
    atoms: [
      { element: 'C', x: -ccBondLength/2, y: 0, z: 0 },
      { element: 'C', x: ccBondLength/2, y: 0, z: 0 },
      { element: 'H', x: -ccBondLength/2 - chBondLength, y: 0, z: 0 },
      { element: 'H', x: ccBondLength/2 + chBondLength, y: 0, z: 0 }
    ],
    bonds: [
      { from: 0, to: 1, order: 3 },
      { from: 0, to: 2, order: 1 },
      { from: 1, to: 3, order: 1 }
    ],
    geometry: 'linear',
    bondAngle: 180,
    bondLength: chBondLength
  };
}

// Estrutura do SO2
function generateSO2Structure() {
  const bondLength = getBondLength('S', 'O', 2);
  const bondAngle = PeriodicTableData.bondAngles.bent_trigonal * Math.PI / 180;

  return {
    atoms: [
      { element: 'S', x: 0, y: 0, z: 0 },
      {
        element: 'O',
        x: bondLength * Math.cos(bondAngle / 2),
        y: bondLength * Math.sin(bondAngle / 2),
        z: 0
      },
      {
        element: 'O',
        x: bondLength * Math.cos(bondAngle / 2),
        y: -bondLength * Math.sin(bondAngle / 2),
        z: 0
      }
    ],
    bonds: [
      { from: 0, to: 1, order: 2 },
      { from: 0, to: 2, order: 2 }
    ],
    geometry: 'bent_trigonal',
    bondAngle: 117,
    bondLength: bondLength
  };
}

// Estrutura do H2S
function generateH2SStructure() {
  const bondLength = getBondLength('S', 'H');
  const bondAngle = 92.1 * Math.PI / 180; // Ângulo específico do H2S

  return {
    atoms: [
      { element: 'S', x: 0, y: 0, z: 0 },
      {
        element: 'H',
        x: bondLength * Math.cos(bondAngle / 2),
        y: bondLength * Math.sin(bondAngle / 2),
        z: 0
      },
      {
        element: 'H',
        x: bondLength * Math.cos(bondAngle / 2),
        y: -bondLength * Math.sin(bondAngle / 2),
        z: 0
      }
    ],
    bonds: [
      { from: 0, to: 1, order: 1 },
      { from: 0, to: 2, order: 1 }
    ],
    geometry: 'bent_tetrahedral',
    bondAngle: 92.1,
    bondLength: bondLength
  };
}

// Estrutura do PH3
function generatePH3Structure() {
  const bondLength = getBondLength('P', 'H');
  const bondAngle = 93.5 * Math.PI / 180; // Ângulo específico do PH3
  const height = bondLength * Math.cos(bondAngle / 2);
  const radius = bondLength * Math.sin(bondAngle / 2);

  return {
    atoms: [
      { element: 'P', x: 0, y: 0, z: height * 0.3 },
      { element: 'H', x: radius, y: 0, z: -height * 0.7 },
      { element: 'H', x: -radius * 0.5, y: radius * Math.sqrt(3) / 2, z: -height * 0.7 },
      { element: 'H', x: -radius * 0.5, y: -radius * Math.sqrt(3) / 2, z: -height * 0.7 }
    ],
    bonds: [
      { from: 0, to: 1, order: 1 },
      { from: 0, to: 2, order: 1 },
      { from: 0, to: 3, order: 1 }
    ],
    geometry: 'trigonal_pyramidal',
    bondAngle: 93.5,
    bondLength: bondLength
  };
}

// Estrutura do BF3
function generateBF3Structure() {
  const bondLength = getBondLength('B', 'F');
  const bondAngle = PeriodicTableData.bondAngles.trigonal_planar * Math.PI / 180;

  return {
    atoms: [
      { element: 'B', x: 0, y: 0, z: 0 },
      { element: 'F', x: bondLength, y: 0, z: 0 },
      {
        element: 'F',
        x: bondLength * Math.cos(bondAngle),
        y: bondLength * Math.sin(bondAngle),
        z: 0
      },
      {
        element: 'F',
        x: bondLength * Math.cos(2 * bondAngle),
        y: bondLength * Math.sin(2 * bondAngle),
        z: 0
      }
    ],
    bonds: [
      { from: 0, to: 1, order: 1 },
      { from: 0, to: 2, order: 1 },
      { from: 0, to: 3, order: 1 }
    ],
    geometry: 'trigonal_planar',
    bondAngle: 120,
    bondLength: bondLength
  };
}

// Estrutura do SF6
function generateSF6Structure() {
  const bondLength = getBondLength('S', 'F');

  return {
    atoms: [
      { element: 'S', x: 0, y: 0, z: 0 },
      { element: 'F', x: bondLength, y: 0, z: 0 },
      { element: 'F', x: -bondLength, y: 0, z: 0 },
      { element: 'F', x: 0, y: bondLength, z: 0 },
      { element: 'F', x: 0, y: -bondLength, z: 0 },
      { element: 'F', x: 0, y: 0, z: bondLength },
      { element: 'F', x: 0, y: 0, z: -bondLength }
    ],
    bonds: [
      { from: 0, to: 1, order: 1 }, { from: 0, to: 2, order: 1 },
      { from: 0, to: 3, order: 1 }, { from: 0, to: 4, order: 1 },
      { from: 0, to: 5, order: 1 }, { from: 0, to: 6, order: 1 }
    ],
    geometry: 'octahedral',
    bondAngle: 90,
    bondLength: bondLength
  };
}

// Estrutura do PCl5
function generatePCl5Structure() {
  const bondLength = getBondLength('P', 'Cl');
  const equatorialRadius = bondLength;
  const axialDistance = bondLength * 1.1; // Ligações axiais são ligeiramente mais longas

  return {
    atoms: [
      { element: 'P', x: 0, y: 0, z: 0 },
      // Ligações equatoriais
      { element: 'Cl', x: equatorialRadius, y: 0, z: 0 },
      { element: 'Cl', x: -equatorialRadius * 0.5, y: equatorialRadius * Math.sqrt(3) / 2, z: 0 },
      { element: 'Cl', x: -equatorialRadius * 0.5, y: -equatorialRadius * Math.sqrt(3) / 2, z: 0 },
      // Ligações axiais
      { element: 'Cl', x: 0, y: 0, z: axialDistance },
      { element: 'Cl', x: 0, y: 0, z: -axialDistance }
    ],
    bonds: [
      { from: 0, to: 1, order: 1 }, { from: 0, to: 2, order: 1 },
      { from: 0, to: 3, order: 1 }, { from: 0, to: 4, order: 1 },
      { from: 0, to: 5, order: 1 }
    ],
    geometry: 'trigonal_bipyramidal',
    bondAngle: 120, // Equatorial
    bondLength: bondLength
  };
}

// Algoritmo VSEPR genérico para moléculas desconhecidas
function generateVSEPRStructure(elements) {
  if (!elements || elements.length === 0) return null;

  // Identificar átomo central (geralmente o menos eletronegativo ou com mais ligações)
  const centralAtom = findCentralAtom(elements);
  if (!centralAtom) return null;

  const centralElement = centralAtom.simbolo;
  const otherAtoms = elements.filter(el => el.simbolo !== centralElement || el.quantidade > 1);

  // Calcular número de ligações e pares solitários
  const totalBondingElectrons = otherAtoms.reduce((sum, atom) => {
    return sum + (atom.quantidade * PeriodicTableData.valenceElectrons[atom.simbolo] || 0);
  }, 0);

  const bondingPairs = otherAtoms.reduce((sum, atom) => sum + atom.quantidade, 0);
  const lonePairs = calculateLonePairs(centralElement, totalBondingElectrons);

  // Determinar geometria VSEPR
  const geometry = determineVSEPRGeometry(centralElement, bondingPairs, lonePairs);

  // Gerar coordenadas baseadas na geometria
  return generateCoordinatesFromGeometry(centralElement, otherAtoms, geometry);
}

// Função para encontrar o átomo central
function findCentralAtom(elements) {
  // Regras para determinar átomo central:
  // 1. Menor eletronegatividade (exceto H)
  // 2. Maior número de ligações possíveis
  // 3. Não é hidrogênio (exceto em casos especiais)

  const nonHydrogenAtoms = elements.filter(el => el.simbolo !== 'H');
  if (nonHydrogenAtoms.length === 0) return elements[0];
  if (nonHydrogenAtoms.length === 1) return nonHydrogenAtoms[0];

  // Encontrar o menos eletronegativo
  return nonHydrogenAtoms.reduce((central, current) => {
    const centralEN = PeriodicTableData.electronegativity[central.simbolo] || 4.0;
    const currentEN = PeriodicTableData.electronegativity[current.simbolo] || 4.0;
    return currentEN < centralEN ? current : central;
  });
}

// Função para gerar coordenadas baseadas na geometria VSEPR
function generateCoordinatesFromGeometry(centralElement, otherAtoms, geometry) {
  const atoms = [{ element: centralElement, x: 0, y: 0, z: 0 }];
  const bonds = [];
  let atomIndex = 1;

  otherAtoms.forEach(atomInfo => {
    const bondLength = getBondLength(centralElement, atomInfo.simbolo);

    for (let i = 0; i < atomInfo.quantidade; i++) {
      const position = calculateAtomPosition(atomIndex - 1, atomInfo.quantidade, geometry, bondLength);
      atoms.push({
        element: atomInfo.simbolo,
        x: position.x,
        y: position.y,
        z: position.z
      });

      bonds.push({ from: 0, to: atomIndex, order: 1 });
      atomIndex++;
    }
  });

  return {
    atoms,
    bonds,
    geometry: geometry.name,
    bondAngle: geometry.angle,
    bondLength: getBondLength(centralElement, otherAtoms[0]?.simbolo || 'H')
  };
}

// Função para calcular posição do átomo baseada na geometria
function calculateAtomPosition(index, totalAtoms, geometry, bondLength) {
  const angle = geometry.angle * Math.PI / 180;

  switch (geometry.name) {
    case 'linear':
      return {
        x: index === 0 ? bondLength : -bondLength,
        y: 0,
        z: 0
      };

    case 'trigonal_planar':
    case 'bent_trigonal':
      const planarAngle = (2 * Math.PI * index) / totalAtoms;
      return {
        x: bondLength * Math.cos(planarAngle),
        y: bondLength * Math.sin(planarAngle),
        z: 0
      };

    case 'tetrahedral':
    case 'trigonal_pyramidal':
    case 'bent_tetrahedral':
      // Coordenadas tetraédricas
      const tetrahedralPositions = [
        { x: 1, y: 1, z: 1 },
        { x: -1, y: -1, z: 1 },
        { x: -1, y: 1, z: -1 },
        { x: 1, y: -1, z: -1 }
      ];
      const pos = tetrahedralPositions[index % 4];
      const factor = bondLength / Math.sqrt(3);
      return {
        x: pos.x * factor,
        y: pos.y * factor,
        z: pos.z * factor
      };

    default:
      // Posição padrão circular
      const defaultAngle = (2 * Math.PI * index) / totalAtoms;
      return {
        x: bondLength * Math.cos(defaultAngle),
        y: bondLength * Math.sin(defaultAngle),
        z: 0
      };
  }
}
