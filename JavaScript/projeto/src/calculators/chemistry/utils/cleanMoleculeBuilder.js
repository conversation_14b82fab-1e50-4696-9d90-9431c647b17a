/**
 * Sistema Limpo de Construção de Moléculas 3D
 * Foco em geometria correta e valência adequada
 */

// Constantes físicas
const BOND_LENGTHS = {
  'C-C': 1.54,
  'C-H': 1.09,
  'C=C': 1.34,
  'C≡C': 1.20
};

const TETRAHEDRAL_ANGLE = 109.47 * Math.PI / 180;

// Função principal para gerar moléculas
export function generateCleanMolecule(input) {
  console.log(`🔬 Sistema limpo tentando gerar: "${input}"`);

  const normalized = input.toLowerCase().trim();

  // Mapear nomes para estruturas
  const moleculeMap = {
    'methane': () => generateMethane(),
    'ethane': () => generateEthane(),
    'propane': () => generatePropane(),
    'butane': () => generateButane(),
    'pentane': () => generatePentane(),
    'hexane': () => generateHexane(),
    'ethyne': () => generateEthyne(),
    'acetylene': () => generateEthyne()
  };

  // Mapear fórmulas para nomes
  const formulaMap = {
    'ch4': 'methane',
    'c2h6': 'ethane',
    'c3h8': 'propane',
    'c4h10': 'butane',
    'c5h12': 'pentane',
    'c6h14': 'hexane',
    'c2h2': 'ethyne'
  };

  const name = formulaMap[normalized] || normalized;
  const generator = moleculeMap[name];

  if (generator) {
    console.log(`✅ Sistema limpo gerando: ${name}`);
    const result = generator();
    console.log(`✅ Estrutura gerada com ${result.atoms.length} átomos`);
    return result;
  }

  console.log(`❌ Sistema limpo não reconheceu: "${input}" (normalizado: "${normalized}")`);
  return null;
}

// Metano (CH4) - tetraédrico perfeito
function generateMethane() {
  const chBond = BOND_LENGTHS['C-H'];
  
  return {
    atoms: [
      { element: 'C', x: 0, y: 0, z: 0 },
      { element: 'H', x: chBond, y: 0, z: 0 },
      { element: 'H', x: -chBond/3, y: chBond * Math.sqrt(8/9), z: 0 },
      { element: 'H', x: -chBond/3, y: -chBond * Math.sqrt(2/9), z: chBond * Math.sqrt(2/3) },
      { element: 'H', x: -chBond/3, y: -chBond * Math.sqrt(2/9), z: -chBond * Math.sqrt(2/3) }
    ],
    bonds: [
      { from: 0, to: 1, order: 1 },
      { from: 0, to: 2, order: 1 },
      { from: 0, to: 3, order: 1 },
      { from: 0, to: 4, order: 1 }
    ],
    geometry: 'tetrahedral',
    bondAngle: 109.47
  };
}

// Etano (C2H6) - dois tetraedros conectados
function generateEthane() {
  const ccBond = BOND_LENGTHS['C-C'];
  const chBond = BOND_LENGTHS['C-H'];
  
  return {
    atoms: [
      // Carbonos
      { element: 'C', x: -ccBond/2, y: 0, z: 0 },
      { element: 'C', x: ccBond/2, y: 0, z: 0 },
      
      // Hidrogênios do primeiro carbono
      { element: 'H', x: -ccBond/2 - chBond * 0.8, y: chBond * 0.6, z: 0 },
      { element: 'H', x: -ccBond/2 - chBond * 0.8, y: -chBond * 0.3, z: chBond * 0.7 },
      { element: 'H', x: -ccBond/2 - chBond * 0.8, y: -chBond * 0.3, z: -chBond * 0.7 },
      
      // Hidrogênios do segundo carbono (rotacionados 60°)
      { element: 'H', x: ccBond/2 + chBond * 0.8, y: -chBond * 0.6, z: 0 },
      { element: 'H', x: ccBond/2 + chBond * 0.8, y: chBond * 0.3, z: chBond * 0.7 },
      { element: 'H', x: ccBond/2 + chBond * 0.8, y: chBond * 0.3, z: -chBond * 0.7 }
    ],
    bonds: [
      { from: 0, to: 1, order: 1 }, // C-C
      { from: 0, to: 2, order: 1 }, // C-H
      { from: 0, to: 3, order: 1 }, // C-H
      { from: 0, to: 4, order: 1 }, // C-H
      { from: 1, to: 5, order: 1 }, // C-H
      { from: 1, to: 6, order: 1 }, // C-H
      { from: 1, to: 7, order: 1 }  // C-H
    ],
    geometry: 'tetrahedral',
    bondAngle: 109.47
  };
}

// Propano (C3H8) - cadeia com ângulo tetraédrico correto
function generatePropane() {
  const ccBond = BOND_LENGTHS['C-C'];
  const chBond = BOND_LENGTHS['C-H'];
  
  // Posições dos carbonos com ângulo tetraédrico
  const c1 = { x: 0, y: 0, z: 0 };
  const c2 = { x: ccBond, y: 0, z: 0 };
  const c3 = { 
    x: ccBond + ccBond * Math.cos(Math.PI - TETRAHEDRAL_ANGLE),
    y: ccBond * Math.sin(Math.PI - TETRAHEDRAL_ANGLE),
    z: 0
  };
  
  return {
    atoms: [
      // Carbonos
      { element: 'C', ...c1 },
      { element: 'C', ...c2 },
      { element: 'C', ...c3 },
      
      // Hidrogênios do primeiro carbono (CH3)
      { element: 'H', x: c1.x - chBond, y: c1.y, z: c1.z },
      { element: 'H', x: c1.x, y: c1.y + chBond * 0.8, z: c1.z + chBond * 0.6 },
      { element: 'H', x: c1.x, y: c1.y - chBond * 0.8, z: c1.z + chBond * 0.6 },
      
      // Hidrogênios do carbono central (CH2)
      { element: 'H', x: c2.x, y: c2.y, z: c2.z + chBond },
      { element: 'H', x: c2.x, y: c2.y, z: c2.z - chBond },
      
      // Hidrogênios do terceiro carbono (CH3)
      { element: 'H', x: c3.x + chBond * 0.6, y: c3.y + chBond * 0.8, z: c3.z },
      { element: 'H', x: c3.x - chBond * 0.3, y: c3.y + chBond * 0.3, z: c3.z + chBond * 0.8 },
      { element: 'H', x: c3.x - chBond * 0.3, y: c3.y + chBond * 0.3, z: c3.z - chBond * 0.8 }
    ],
    bonds: [
      { from: 0, to: 1, order: 1 }, // C-C
      { from: 1, to: 2, order: 1 }, // C-C
      { from: 0, to: 3, order: 1 }, // C-H
      { from: 0, to: 4, order: 1 }, // C-H
      { from: 0, to: 5, order: 1 }, // C-H
      { from: 1, to: 6, order: 1 }, // C-H
      { from: 1, to: 7, order: 1 }, // C-H
      { from: 2, to: 8, order: 1 }, // C-H
      { from: 2, to: 9, order: 1 }, // C-H
      { from: 2, to: 10, order: 1 } // C-H
    ],
    geometry: 'tetrahedral',
    bondAngle: 109.47
  };
}

// Butano (C4H10) - cadeia zigzag
function generateButane() {
  const ccBond = BOND_LENGTHS['C-C'];
  const chBond = BOND_LENGTHS['C-H'];
  
  // Posições dos carbonos em conformação zigzag
  const c1 = { x: 0, y: 0, z: 0 };
  const c2 = { x: ccBond, y: 0, z: 0 };
  const c3 = { 
    x: ccBond + ccBond * Math.cos(Math.PI - TETRAHEDRAL_ANGLE),
    y: ccBond * Math.sin(Math.PI - TETRAHEDRAL_ANGLE),
    z: 0
  };
  const c4 = {
    x: c3.x + ccBond * Math.cos(-TETRAHEDRAL_ANGLE),
    y: c3.y + ccBond * Math.sin(-TETRAHEDRAL_ANGLE),
    z: 0
  };
  
  return {
    atoms: [
      // Carbonos
      { element: 'C', ...c1 },
      { element: 'C', ...c2 },
      { element: 'C', ...c3 },
      { element: 'C', ...c4 },
      
      // Hidrogênios do primeiro carbono (CH3)
      { element: 'H', x: c1.x - chBond, y: c1.y, z: c1.z },
      { element: 'H', x: c1.x, y: c1.y + chBond * 0.8, z: c1.z + chBond * 0.6 },
      { element: 'H', x: c1.x, y: c1.y - chBond * 0.8, z: c1.z + chBond * 0.6 },
      
      // Hidrogênios do segundo carbono (CH2)
      { element: 'H', x: c2.x, y: c2.y, z: c2.z + chBond },
      { element: 'H', x: c2.x, y: c2.y, z: c2.z - chBond },
      
      // Hidrogênios do terceiro carbono (CH2)
      { element: 'H', x: c3.x, y: c3.y, z: c3.z + chBond },
      { element: 'H', x: c3.x, y: c3.y, z: c3.z - chBond },
      
      // Hidrogênios do quarto carbono (CH3)
      { element: 'H', x: c4.x + chBond * 0.6, y: c4.y - chBond * 0.8, z: c4.z },
      { element: 'H', x: c4.x - chBond * 0.3, y: c4.y - chBond * 0.3, z: c4.z + chBond * 0.8 },
      { element: 'H', x: c4.x - chBond * 0.3, y: c4.y - chBond * 0.3, z: c4.z - chBond * 0.8 }
    ],
    bonds: [
      { from: 0, to: 1, order: 1 }, // C-C
      { from: 1, to: 2, order: 1 }, // C-C
      { from: 2, to: 3, order: 1 }, // C-C
      { from: 0, to: 4, order: 1 }, // C-H
      { from: 0, to: 5, order: 1 }, // C-H
      { from: 0, to: 6, order: 1 }, // C-H
      { from: 1, to: 7, order: 1 }, // C-H
      { from: 1, to: 8, order: 1 }, // C-H
      { from: 2, to: 9, order: 1 }, // C-H
      { from: 2, to: 10, order: 1 }, // C-H
      { from: 3, to: 11, order: 1 }, // C-H
      { from: 3, to: 12, order: 1 }, // C-H
      { from: 3, to: 13, order: 1 }  // C-H
    ],
    geometry: 'tetrahedral',
    bondAngle: 109.47
  };
}

// Pentano (C5H12) - cadeia zigzag estendida
function generatePentane() {
  return generateAlkaneChain(5);
}

// Hexano (C6H14) - cadeia zigzag estendida
function generateHexane() {
  return generateAlkaneChain(6);
}

// Etino/Acetileno (C2H2) - linear
function generateEthyne() {
  const ccBond = BOND_LENGTHS['C≡C'];
  const chBond = BOND_LENGTHS['C-H'];
  
  return {
    atoms: [
      { element: 'C', x: -ccBond/2, y: 0, z: 0 },
      { element: 'C', x: ccBond/2, y: 0, z: 0 },
      { element: 'H', x: -ccBond/2 - chBond, y: 0, z: 0 },
      { element: 'H', x: ccBond/2 + chBond, y: 0, z: 0 }
    ],
    bonds: [
      { from: 0, to: 1, order: 3 }, // C≡C
      { from: 0, to: 2, order: 1 }, // C-H
      { from: 1, to: 3, order: 1 }  // C-H
    ],
    geometry: 'linear',
    bondAngle: 180
  };
}

// Gerador genérico de alcanos lineares
function generateAlkaneChain(n) {
  if (n <= 4) {
    // Usar funções específicas para moléculas pequenas
    const generators = [null, generateMethane, generateEthane, generatePropane, generateButane];
    return generators[n]();
  }

  const ccBond = BOND_LENGTHS['C-C'];
  const chBond = BOND_LENGTHS['C-H'];
  const atoms = [];
  const bonds = [];

  // Gerar carbonos em conformação zigzag
  for (let i = 0; i < n; i++) {
    let x, y, z = 0;

    if (i === 0) {
      x = 0; y = 0;
    } else if (i === 1) {
      x = ccBond; y = 0;
    } else {
      // Alternar ângulos para criar zigzag
      const prevC = atoms[i - 1];
      const prevPrevC = atoms[i - 2];

      const angle = (i % 2 === 0) ? TETRAHEDRAL_ANGLE : -TETRAHEDRAL_ANGLE;
      const dx = prevC.x - prevPrevC.x;
      const dy = prevC.y - prevPrevC.y;

      x = prevC.x + ccBond * Math.cos(Math.atan2(dy, dx) + Math.PI - angle);
      y = prevC.y + ccBond * Math.sin(Math.atan2(dy, dx) + Math.PI - angle);
    }

    atoms.push({ element: 'C', x, y, z });

    // Adicionar ligação C-C
    if (i > 0) {
      bonds.push({ from: i - 1, to: i, order: 1 });
    }
  }

  // Adicionar hidrogênios
  atoms.forEach((carbon, i) => {
    const numH = (i === 0 || i === n - 1) ? 3 : 2; // CH3 nas pontas, CH2 no meio

    for (let h = 0; h < numH; h++) {
      const angle = (h * 2 * Math.PI / numH) + (i * Math.PI / 3);
      const hx = carbon.x + chBond * Math.cos(angle) * 0.8;
      const hy = carbon.y + chBond * Math.sin(angle) * 0.8;
      const hz = carbon.z + chBond * (h % 2 ? 0.6 : -0.6);

      const hIndex = atoms.length;
      atoms.push({ element: 'H', x: hx, y: hy, z: hz });
      bonds.push({ from: i, to: hIndex, order: 1 });
    }
  });

  return {
    atoms,
    bonds,
    geometry: 'tetrahedral',
    bondAngle: 109.47
  };
}

// Função de teste para o console
if (typeof window !== 'undefined') {
  window.testCleanSystem = function() {
    console.log('🧪 TESTE DO SISTEMA LIMPO');

    const testCases = ['propane', 'butane', 'ethyne'];

    testCases.forEach(molecule => {
      console.log(`\n🔬 Testando: ${molecule}`);
      const result = generateCleanMolecule(molecule);

      if (result) {
        console.log(`✅ Sucesso! ${result.atoms.length} átomos, ${result.bonds.length} ligações`);

        // Verificar propano especificamente
        if (molecule === 'propane') {
          const carbons = result.atoms.filter(a => a.element === 'C');
          if (carbons.length >= 3) {
            const c1 = carbons[0], c2 = carbons[1], c3 = carbons[2];
            const v1 = { x: c1.x - c2.x, y: c1.y - c2.y, z: c1.z - c2.z };
            const v2 = { x: c3.x - c2.x, y: c3.y - c2.y, z: c3.z - c2.z };

            const dot = v1.x * v2.x + v1.y * v2.y + v1.z * v2.z;
            const mag1 = Math.sqrt(v1.x ** 2 + v1.y ** 2 + v1.z ** 2);
            const mag2 = Math.sqrt(v2.x ** 2 + v2.y ** 2 + v2.z ** 2);

            const angle = Math.acos(dot / (mag1 * mag2)) * 180 / Math.PI;
            console.log(`🔍 Ângulo C-C-C: ${angle.toFixed(2)}° (esperado: ~109.47°)`);
          }
        }

        console.log('📋 Estrutura:', result);
      } else {
        console.log('❌ Falhou');
      }
    });
  };
}
