/**
 * Sistema Genérico de Geração de Estruturas Moleculares 3D
 * Funciona com nomenclatura IUPAC, fórmulas químicas e SMILES
 */

// import { PeriodicTableData } from '../constants/chemistryConstants.js';

// Parser de fórmulas químicas
export function parseChemicalFormula(formula) {
  const elements = [];
  const regex = /([A-Z][a-z]?)(\d*)/g;
  let match;
  
  while ((match = regex.exec(formula)) !== null) {
    const element = match[1];
    const count = match[2] ? parseInt(match[2]) : 1;
    
    const existing = elements.find(el => el.simbolo === element);
    if (existing) {
      existing.quantidade += count;
    } else {
      elements.push({ simbolo: element, quantidade: count });
    }
  }
  
  return elements;
}

// Gerador automático de SMILES para moléculas simples
export function generateSMILESFromFormula(formula) {
  const elements = parseChemicalFormula(formula);
  
  // Alcanos: CnH2n+2
  const carbon = elements.find(el => el.simbolo === 'C');
  const hydrogen = elements.find(el => el.simbolo === 'H');
  
  if (carbon && hydrogen && elements.length === 2) {
    const nC = carbon.quantidade;
    const nH = hydrogen.quantidade;
    
    // Verificar se é alcano
    if (nH === 2 * nC + 2) {
      return 'C'.repeat(nC);
    }
    
    // Verificar se é alceno
    if (nH === 2 * nC && nC >= 2) {
      return 'C=C' + 'C'.repeat(nC - 2);
    }
    
    // Verificar se é alcino
    if (nH === 2 * nC - 2 && nC >= 2) {
      return 'C#C' + 'C'.repeat(nC - 2);
    }
  }
  
  // Álcoois simples
  const oxygen = elements.find(el => el.simbolo === 'O');
  if (carbon && hydrogen && oxygen && oxygen.quantidade === 1 && elements.length === 3) {
    const nC = carbon.quantidade;
    const nH = hydrogen.quantidade;
    
    if (nH === 2 * nC + 2) {
      return 'C'.repeat(nC - 1) + 'CO';
    }
  }
  
  return null;
}

// Reconhecimento de nomenclatura IUPAC e nomes comuns
export function parseIUPACName(name) {
  const normalized = name.toLowerCase().trim();

  // Nomes comuns primeiro
  const commonNames = {
    'water': { formula: 'H2O', type: 'inorganic' },
    'ammonia': { formula: 'NH3', type: 'inorganic' },
    'methane': { carbonCount: 1, functionalGroup: { type: 'alkane' } },
    'ethane': { carbonCount: 2, functionalGroup: { type: 'alkane' } },
    'propane': { carbonCount: 3, functionalGroup: { type: 'alkane' } },
    'butane': { carbonCount: 4, functionalGroup: { type: 'alkane' } },
    'pentane': { carbonCount: 5, functionalGroup: { type: 'alkane' } },
    'hexane': { carbonCount: 6, functionalGroup: { type: 'alkane' } },
    'heptane': { carbonCount: 7, functionalGroup: { type: 'alkane' } },
    'octane': { carbonCount: 8, functionalGroup: { type: 'alkane' } },
    'nonane': { carbonCount: 9, functionalGroup: { type: 'alkane' } },
    'decane': { carbonCount: 10, functionalGroup: { type: 'alkane' } },
    'ethene': { carbonCount: 2, functionalGroup: { type: 'alkene' } },
    'ethylene': { carbonCount: 2, functionalGroup: { type: 'alkene' } },
    'propene': { carbonCount: 3, functionalGroup: { type: 'alkene' } },
    'propylene': { carbonCount: 3, functionalGroup: { type: 'alkene' } },
    'ethyne': { carbonCount: 2, functionalGroup: { type: 'alkyne' } },
    'acetylene': { carbonCount: 2, functionalGroup: { type: 'alkyne' } },
    'methanol': { carbonCount: 1, functionalGroup: { type: 'alcohol' } },
    'ethanol': { carbonCount: 2, functionalGroup: { type: 'alcohol' } },
    'propanol': { carbonCount: 3, functionalGroup: { type: 'alcohol' } },
    'benzene': { formula: 'C6H6', type: 'aromatic' },
    'toluene': { formula: 'C7H8', type: 'aromatic' }
  };

  if (commonNames[normalized]) {
    return commonNames[normalized];
  }

  // Prefixos para número de carbonos
  const prefixes = {
    'meth': 1, 'eth': 2, 'prop': 3, 'but': 4, 'pent': 5,
    'hex': 6, 'hept': 7, 'oct': 8, 'non': 9, 'dec': 10
  };

  // Sufixos funcionais
  const suffixes = {
    'ane': { type: 'alkane', bonds: 'single' },
    'ene': { type: 'alkene', bonds: 'double' },
    'yne': { type: 'alkyne', bonds: 'triple' },
    'ol': { type: 'alcohol', functional: 'OH' },
    'al': { type: 'aldehyde', functional: 'CHO' },
    'one': { type: 'ketone', functional: 'CO' },
    'oic acid': { type: 'carboxylic_acid', functional: 'COOH' }
  };

  // Encontrar prefixo
  let carbonCount = 0;
  let functionalGroup = null;

  for (const [prefix, count] of Object.entries(prefixes)) {
    if (normalized.startsWith(prefix)) {
      carbonCount = count;
      break;
    }
  }

  // Encontrar sufixo
  for (const [suffix, info] of Object.entries(suffixes)) {
    if (normalized.endsWith(suffix)) {
      functionalGroup = info;
      break;
    }
  }

  if (carbonCount > 0 && functionalGroup) {
    return { carbonCount, functionalGroup };
  }

  return null;
}

// Gerador de fórmula a partir de nome IUPAC
export function generateFormulaFromIUPAC(name) {
  const parsed = parseIUPACName(name);
  if (!parsed) return null;

  // Se já tem fórmula direta (moléculas inorgânicas ou aromáticas)
  if (parsed.formula) {
    return parsed.formula;
  }

  const { carbonCount, functionalGroup } = parsed;

  if (!functionalGroup || !carbonCount) return null;

  switch (functionalGroup.type) {
    case 'alkane':
      return carbonCount === 1 ? 'CH4' : `C${carbonCount}H${2 * carbonCount + 2}`;
    case 'alkene':
      return carbonCount === 2 ? 'C2H4' : `C${carbonCount}H${2 * carbonCount}`;
    case 'alkyne':
      return carbonCount === 2 ? 'C2H2' : `C${carbonCount}H${2 * carbonCount - 2}`;
    case 'alcohol':
      return carbonCount === 1 ? 'CH4O' : `C${carbonCount}H${2 * carbonCount + 2}O`;
    case 'aldehyde':
      return carbonCount === 1 ? 'CH2O' : `C${carbonCount}H${2 * carbonCount}O`;
    case 'ketone':
      return carbonCount >= 3 ? `C${carbonCount}H${2 * carbonCount}O` : null;
    case 'carboxylic_acid':
      return carbonCount === 1 ? 'CH2O2' : `C${carbonCount}H${2 * carbonCount}O2`;
    default:
      return null;
  }
}

// Sistema genérico de geração de estruturas 3D
export function generateGeneric3DStructure(input) {
  let formula = null;
  let elements = null;
  let smiles = null;
  
  // Tentar interpretar o input
  if (input.match(/^[A-Z][a-z]?(\d*[A-Z][a-z]?\d*)*$/)) {
    // É uma fórmula química
    formula = input;
    elements = parseChemicalFormula(formula);
    smiles = generateSMILESFromFormula(formula);
  } else if (input.match(/^[A-Za-z\s]+$/)) {
    // É um nome (IUPAC ou comum)
    formula = generateFormulaFromIUPAC(input);
    if (formula) {
      elements = parseChemicalFormula(formula);
      smiles = generateSMILESFromFormula(formula);
    }
  } else {
    // Pode ser SMILES
    smiles = input;
    formula = generateFormulaFromSMILES(smiles);
    if (formula) {
      elements = parseChemicalFormula(formula);
    }
  }
  
  if (!elements || elements.length === 0) {
    return null;
  }
  
  // Gerar estrutura 3D baseada nos elementos
  return generateStructureFromElements(elements, smiles);
}

// Gerador de fórmula a partir de SMILES
function generateFormulaFromSMILES(smiles) {
  const elementCount = {};
  
  // Contar carbonos
  const carbons = (smiles.match(/C/g) || []).length;
  if (carbons > 0) elementCount.C = carbons;
  
  // Contar outros elementos explícitos
  const others = smiles.match(/[NOPS]/g) || [];
  others.forEach(el => {
    elementCount[el] = (elementCount[el] || 0) + 1;
  });
  
  // Calcular hidrogênios implícitos
  let hydrogens = 0;
  for (let i = 0; i < smiles.length; i++) {
    const char = smiles[i];
    if (char === 'C') {
      let bonds = 0;
      
      // Contar ligações
      if (i > 0 && /[C=]/.test(smiles[i-1])) bonds++;
      if (i < smiles.length - 1 && /[C=]/.test(smiles[i+1])) bonds++;
      
      // Ligações duplas
      if (i > 0 && smiles[i-1] === '=') bonds++;
      if (i < smiles.length - 1 && smiles[i+1] === '=') bonds++;
      
      // Ligações triplas
      if (i > 0 && smiles[i-1] === '#') bonds += 2;
      if (i < smiles.length - 1 && smiles[i+1] === '#') bonds += 2;
      
      // Hidrogênios implícitos (4 - bonds)
      hydrogens += Math.max(0, 4 - bonds);
    }
  }
  
  if (hydrogens > 0) elementCount.H = hydrogens;
  
  // Construir fórmula
  let formula = '';
  const order = ['C', 'H', 'N', 'O', 'P', 'S'];
  
  order.forEach(el => {
    if (elementCount[el]) {
      formula += el + (elementCount[el] > 1 ? elementCount[el] : '');
    }
  });
  
  return formula || null;
}

// Gerador de estrutura 3D a partir de elementos
function generateStructureFromElements(elements, smiles = null) {
  // Identificar tipo de molécula
  const carbon = elements.find(el => el.simbolo === 'C');

  // Moléculas orgânicas (contém carbono)
  if (carbon) {
    return generateOrganicStructure(elements, smiles);
  }

  // Moléculas inorgânicas simples
  return generateInorganicStructure(elements);
}

// Gerador de estruturas orgânicas
function generateOrganicStructure(elements, smiles) {
  const carbon = elements.find(el => el.simbolo === 'C');
  const hydrogen = elements.find(el => el.simbolo === 'H');
  const nC = carbon.quantidade;
  const nH = hydrogen ? hydrogen.quantidade : 0;
  
  // Alcanos lineares
  if (nH === 2 * nC + 2) {
    return generateLinearAlkane(nC);
  }
  
  // Alcenos
  if (nH === 2 * nC) {
    return generateAlkene(nC);
  }
  
  // Alcinos
  if (nH === 2 * nC - 2) {
    return generateAlkyne(nC);
  }
  
  // Estrutura genérica baseada em VSEPR
  return generateVSEPRStructure(elements);
}

// Gerador de alcanos lineares com geometria tetraédrica correta
function generateLinearAlkane(nC) {
  const atoms = [];
  const bonds = [];
  const ccBondLength = 1.54; // Angstroms
  const chBondLength = 1.09;
  const tetraAngle = Math.acos(-1/3); // Ângulo tetraédrico real ~109.47°

  // Casos especiais para moléculas pequenas
  if (nC === 1) {
    return generateMethane();
  } else if (nC === 2) {
    return generateEthane();
  }

  // Para n >= 3, gerar cadeia com ângulos tetraédricos
  for (let i = 0; i < nC; i++) {
    let x, y, z;

    if (i === 0) {
      // Primeiro carbono na origem
      x = 0; y = 0; z = 0;
    } else if (i === 1) {
      // Segundo carbono ao longo do eixo x
      x = ccBondLength; y = 0; z = 0;
    } else {
      // Carbonos subsequentes com ângulo tetraédrico
      const prevC = atoms[i - 1];
      const prevPrevC = atoms[i - 2];

      // Vetor da ligação anterior
      const dx = prevC.x - prevPrevC.x;
      const dy = prevC.y - prevPrevC.y;
      const dz = prevC.z - prevPrevC.z;
      const length = Math.sqrt(dx*dx + dy*dy + dz*dz);

      // Normalizar
      const ux = dx / length;

      // Calcular nova direção com ângulo tetraédrico
      // Rotacionar em torno do eixo perpendicular
      const rotAngle = Math.PI - tetraAngle; // Ângulo de rotação
      const alternateY = (i % 2) * 2 - 1; // Alternar direção para zigzag

      x = prevC.x + ccBondLength * Math.cos(rotAngle) * ux;
      y = prevC.y + ccBondLength * Math.sin(rotAngle) * alternateY;
      z = prevC.z + ccBondLength * Math.sin(rotAngle) * 0.5;
    }

    atoms.push({ element: 'C', x, y, z });

    if (i > 0) {
      bonds.push({ from: i - 1, to: i, order: 1 });
    }
  }

  // Adicionar hidrogênios com posicionamento tetraédrico
  atoms.forEach((carbon, i) => {
    const numH = (i === 0 || i === nC - 1) ? 3 : 2; // CH3 nas pontas, CH2 no meio

    // Calcular direções das ligações C-H
    const hPositions = calculateTetrahedralHydrogens(carbon, i, atoms, numH, chBondLength);

    hPositions.forEach(pos => {
      atoms.push({ element: 'H', x: pos.x, y: pos.y, z: pos.z });
      bonds.push({ from: i, to: atoms.length - 1, order: 1 });
    });
  });

  return {
    atoms,
    bonds,
    geometry: 'linear_alkane',
    bondAngle: 109.47,
    bondLength: ccBondLength
  };
}

// Função auxiliar para calcular posições tetraédricas dos hidrogênios
function calculateTetrahedralHydrogens(carbon, carbonIndex, allCarbons, numH, chBondLength) {
  const positions = [];
  const tetraAngle = Math.acos(-1/3);

  // Gerar direções tetraédricas para hidrogênios
  for (let h = 0; h < numH; h++) {
    const angle = (h * 2 * Math.PI / numH) + carbonIndex * Math.PI / 6;
    const perpAngle = tetraAngle;

    const hx = carbon.x + chBondLength * Math.cos(angle) * Math.sin(perpAngle);
    const hy = carbon.y + chBondLength * Math.sin(angle) * Math.sin(perpAngle);
    const hz = carbon.z + chBondLength * Math.cos(perpAngle) * (h % 2 ? 1 : -1);

    positions.push({ x: hx, y: hy, z: hz });
  }

  return positions;
}

// Funções específicas para moléculas pequenas
function generateMethane() {
  const chBondLength = 1.09;

  return {
    atoms: [
      { element: 'C', x: 0, y: 0, z: 0 },
      { element: 'H', x: chBondLength, y: 0, z: 0 },
      {
        element: 'H',
        x: chBondLength * Math.cos(2 * Math.PI / 3),
        y: chBondLength * Math.sin(2 * Math.PI / 3),
        z: 0
      },
      {
        element: 'H',
        x: chBondLength * Math.cos(4 * Math.PI / 3),
        y: chBondLength * Math.sin(4 * Math.PI / 3),
        z: 0
      },
      {
        element: 'H',
        x: 0,
        y: 0,
        z: chBondLength * Math.sqrt(2/3)
      }
    ],
    bonds: [
      { from: 0, to: 1, order: 1 },
      { from: 0, to: 2, order: 1 },
      { from: 0, to: 3, order: 1 },
      { from: 0, to: 4, order: 1 }
    ],
    geometry: 'tetrahedral',
    bondAngle: 109.47,
    bondLength: chBondLength
  };
}

function generateEthane() {
  const ccBondLength = 1.54;
  const chBondLength = 1.09;

  return {
    atoms: [
      { element: 'C', x: -ccBondLength/2, y: 0, z: 0 },
      { element: 'C', x: ccBondLength/2, y: 0, z: 0 },
      // Hidrogênios do primeiro carbono
      { element: 'H', x: -ccBondLength/2 - chBondLength * 0.8, y: chBondLength * 0.6, z: 0 },
      { element: 'H', x: -ccBondLength/2 - chBondLength * 0.8, y: -chBondLength * 0.3, z: chBondLength * 0.7 },
      { element: 'H', x: -ccBondLength/2 - chBondLength * 0.8, y: -chBondLength * 0.3, z: -chBondLength * 0.7 },
      // Hidrogênios do segundo carbono
      { element: 'H', x: ccBondLength/2 + chBondLength * 0.8, y: chBondLength * 0.6, z: 0 },
      { element: 'H', x: ccBondLength/2 + chBondLength * 0.8, y: -chBondLength * 0.3, z: chBondLength * 0.7 },
      { element: 'H', x: ccBondLength/2 + chBondLength * 0.8, y: -chBondLength * 0.3, z: -chBondLength * 0.7 }
    ],
    bonds: [
      { from: 0, to: 1, order: 1 },
      { from: 0, to: 2, order: 1 },
      { from: 0, to: 3, order: 1 },
      { from: 0, to: 4, order: 1 },
      { from: 1, to: 5, order: 1 },
      { from: 1, to: 6, order: 1 },
      { from: 1, to: 7, order: 1 }
    ],
    geometry: 'tetrahedral',
    bondAngle: 109.47,
    bondLength: ccBondLength
  };
}

// Placeholder para outras funções
function generateAlkene(nC) {
  // Implementação simplificada - usar alcano como base
  return generateLinearAlkane(nC);
}

function generateAlkyne(nC) {
  // Implementação simplificada - usar alcano como base
  return generateLinearAlkane(nC);
}

function generateInorganicStructure(elements) {
  // Implementação básica para moléculas inorgânicas
  return generateVSEPRStructure(elements);
}

function generateVSEPRStructure(elements) {
  // Implementação básica VSEPR
  const atoms = [];
  const bonds = [];
  
  elements.forEach((element, index) => {
    for (let i = 0; i < element.quantidade; i++) {
      const angle = (atoms.length * 2 * Math.PI) / elements.reduce((sum, el) => sum + el.quantidade, 0);
      const radius = index === 0 ? 0 : 1.5;
      
      atoms.push({
        element: element.simbolo,
        x: radius * Math.cos(angle),
        y: radius * Math.sin(angle),
        z: 0
      });
      
      if (atoms.length > 1) {
        bonds.push({ from: 0, to: atoms.length - 1, order: 1 });
      }
    }
  });
  
  return {
    atoms,
    bonds,
    geometry: 'generic',
    bondAngle: 109.47,
    bondLength: 1.5
  };
}
