import { generatePrecise3DCoordinates, determineVSEPRGeometry, getBondLength } from './molecularGeometry.js';

/**
 * Arquivo de teste para verificar os cálculos de geometria molecular
 */

// Função para testar estruturas específicas
export function testMolecularStructures() {
  console.log('🧪 Testando Biblioteca de Geometria Molecular');
  console.log('='.repeat(50));

  // Teste 1: Água (H2O)
  console.log('\n💧 Testando H2O (Água):');
  const h2oElements = [
    { simbolo: 'O', quantidade: 1 },
    { simbolo: 'H', quantidade: 2 }
  ];
  const h2oStructure = generatePrecise3DCoordinates('H2O', h2oElements);
  console.log('Geometria:', h2oStructure?.geometry);
  console.log('Ângulo de ligação:', h2oStructure?.bondAngle, '°');
  console.log('Comprimento de ligação O-H:', h2oStructure?.bondLength, 'Å');
  console.log('Número de átomos:', h2oStructure?.atoms?.length);
  console.log('Coordenadas do oxigênio:', h2oStructure?.atoms?.[0]);
  console.log('Coordenadas H1:', h2oStructure?.atoms?.[1]);
  console.log('Coordenadas H2:', h2oStructure?.atoms?.[2]);

  // Teste 2: Dióxido de carbono (CO2)
  console.log('\n🌫️ Testando CO2 (Dióxido de Carbono):');
  const co2Elements = [
    { simbolo: 'C', quantidade: 1 },
    { simbolo: 'O', quantidade: 2 }
  ];
  const co2Structure = generatePrecise3DCoordinates('CO2', co2Elements);
  console.log('Geometria:', co2Structure?.geometry);
  console.log('Ângulo de ligação:', co2Structure?.bondAngle, '°');
  console.log('Comprimento de ligação C=O:', co2Structure?.bondLength, 'Å');
  console.log('Número de átomos:', co2Structure?.atoms?.length);
  console.log('Coordenadas do carbono:', co2Structure?.atoms?.[0]);
  console.log('Coordenadas O1:', co2Structure?.atoms?.[1]);
  console.log('Coordenadas O2:', co2Structure?.atoms?.[2]);

  // Teste 3: Amônia (NH3)
  console.log('\n🔵 Testando NH3 (Amônia):');
  const nh3Elements = [
    { simbolo: 'N', quantidade: 1 },
    { simbolo: 'H', quantidade: 3 }
  ];
  const nh3Structure = generatePrecise3DCoordinates('NH3', nh3Elements);
  console.log('Geometria:', nh3Structure?.geometry);
  console.log('Ângulo de ligação:', nh3Structure?.bondAngle, '°');
  console.log('Comprimento de ligação N-H:', nh3Structure?.bondLength, 'Å');
  console.log('Número de átomos:', nh3Structure?.atoms?.length);

  // Teste 4: Metano (CH4)
  console.log('\n⚡ Testando CH4 (Metano):');
  const ch4Elements = [
    { simbolo: 'C', quantidade: 1 },
    { simbolo: 'H', quantidade: 4 }
  ];
  const ch4Structure = generatePrecise3DCoordinates('CH4', ch4Elements);
  console.log('Geometria:', ch4Structure?.geometry);
  console.log('Ângulo de ligação:', ch4Structure?.bondAngle, '°');
  console.log('Comprimento de ligação C-H:', ch4Structure?.bondLength, 'Å');
  console.log('Número de átomos:', ch4Structure?.atoms?.length);

  // Teste 5: Teste de geometria VSEPR
  console.log('\n🔬 Testando Teoria VSEPR:');
  
  // Teste linear (2 pares ligantes, 0 solitários)
  const linearGeometry = determineVSEPRGeometry('C', 2, 0);
  console.log('Linear (CO2):', linearGeometry);
  
  // Teste trigonal planar (3 pares ligantes, 0 solitários)
  const trigonalGeometry = determineVSEPRGeometry('B', 3, 0);
  console.log('Trigonal Planar (BF3):', trigonalGeometry);
  
  // Teste tetraédrico (4 pares ligantes, 0 solitários)
  const tetrahedralGeometry = determineVSEPRGeometry('C', 4, 0);
  console.log('Tetraédrico (CH4):', tetrahedralGeometry);
  
  // Teste piramidal trigonal (3 pares ligantes, 1 solitário)
  const pyramidalGeometry = determineVSEPRGeometry('N', 3, 1);
  console.log('Piramidal Trigonal (NH3):', pyramidalGeometry);
  
  // Teste angular (2 pares ligantes, 2 solitários)
  const bentGeometry = determineVSEPRGeometry('O', 2, 2);
  console.log('Angular (H2O):', bentGeometry);

  // Teste 6: Comprimentos de ligação
  console.log('\n📏 Testando Comprimentos de Ligação:');
  console.log('C-H:', getBondLength('C', 'H'), 'Å');
  console.log('O-H:', getBondLength('O', 'H'), 'Å');
  console.log('N-H:', getBondLength('N', 'H'), 'Å');
  console.log('C=O (dupla):', getBondLength('C', 'O', 2), 'Å');
  console.log('C≡N (tripla):', getBondLength('C', 'N', 3), 'Å');

  console.log('\n✅ Testes concluídos!');
  console.log('='.repeat(50));
}

// Função para validar precisão dos ângulos
export function validateBondAngles() {
  console.log('\n🎯 Validando Precisão dos Ângulos de Ligação:');
  
  const testCases = [
    {
      name: 'H2O',
      elements: [{ simbolo: 'O', quantidade: 1 }, { simbolo: 'H', quantidade: 2 }],
      expectedAngle: 104.5,
      tolerance: 1.0
    },
    {
      name: 'CO2',
      elements: [{ simbolo: 'C', quantidade: 1 }, { simbolo: 'O', quantidade: 2 }],
      expectedAngle: 180,
      tolerance: 0.1
    },
    {
      name: 'NH3',
      elements: [{ simbolo: 'N', quantidade: 1 }, { simbolo: 'H', quantidade: 3 }],
      expectedAngle: 107,
      tolerance: 2.0
    },
    {
      name: 'CH4',
      elements: [{ simbolo: 'C', quantidade: 1 }, { simbolo: 'H', quantidade: 4 }],
      expectedAngle: 109.47,
      tolerance: 1.0
    }
  ];

  testCases.forEach(testCase => {
    const structure = generatePrecise3DCoordinates(testCase.name, testCase.elements);
    const actualAngle = structure?.bondAngle || 0;
    const difference = Math.abs(actualAngle - testCase.expectedAngle);
    const isValid = difference <= testCase.tolerance;
    
    console.log(`${testCase.name}: ${actualAngle}° (esperado: ${testCase.expectedAngle}°) - ${isValid ? '✅' : '❌'}`);
    if (!isValid) {
      console.log(`  Diferença: ${difference.toFixed(2)}° (tolerância: ${testCase.tolerance}°)`);
    }
  });
}

// Função para calcular distâncias entre átomos
export function calculateAtomDistance(atom1, atom2) {
  const dx = atom1.x - atom2.x;
  const dy = atom1.y - atom2.y;
  const dz = atom1.z - atom2.z;
  return Math.sqrt(dx * dx + dy * dy + dz * dz);
}

// Função para calcular ângulo entre três átomos
export function calculateBondAngle(atom1, atom2, atom3) {
  // atom2 é o átomo central
  const v1 = {
    x: atom1.x - atom2.x,
    y: atom1.y - atom2.y,
    z: atom1.z - atom2.z
  };
  
  const v2 = {
    x: atom3.x - atom2.x,
    y: atom3.y - atom2.y,
    z: atom3.z - atom2.z
  };
  
  const dot = v1.x * v2.x + v1.y * v2.y + v1.z * v2.z;
  const mag1 = Math.sqrt(v1.x * v1.x + v1.y * v1.y + v1.z * v1.z);
  const mag2 = Math.sqrt(v2.x * v2.x + v2.y * v2.y + v2.z * v2.z);
  
  const cosAngle = dot / (mag1 * mag2);
  const angleRad = Math.acos(Math.max(-1, Math.min(1, cosAngle)));
  return angleRad * 180 / Math.PI;
}

// Função para validar estruturas calculadas
export function validateCalculatedStructures() {
  console.log('\n🔍 Validando Estruturas Calculadas:');
  
  // Validar H2O
  const h2oStructure = generatePrecise3DCoordinates('H2O', [
    { simbolo: 'O', quantidade: 1 }, 
    { simbolo: 'H', quantidade: 2 }
  ]);
  
  if (h2oStructure && h2oStructure.atoms.length >= 3) {
    const calculatedAngle = calculateBondAngle(
      h2oStructure.atoms[1], // H1
      h2oStructure.atoms[0], // O (central)
      h2oStructure.atoms[2]  // H2
    );
    console.log(`H2O - Ângulo calculado: ${calculatedAngle.toFixed(2)}° (esperado: ~104.5°)`);
    
    const bondLength = calculateAtomDistance(h2oStructure.atoms[0], h2oStructure.atoms[1]);
    console.log(`H2O - Comprimento O-H: ${bondLength.toFixed(3)} Å (esperado: ~0.96 Å)`);
  }
  
  // Validar CO2
  const co2Structure = generatePrecise3DCoordinates('CO2', [
    { simbolo: 'C', quantidade: 1 }, 
    { simbolo: 'O', quantidade: 2 }
  ]);
  
  if (co2Structure && co2Structure.atoms.length >= 3) {
    const calculatedAngle = calculateBondAngle(
      co2Structure.atoms[1], // O1
      co2Structure.atoms[0], // C (central)
      co2Structure.atoms[2]  // O2
    );
    console.log(`CO2 - Ângulo calculado: ${calculatedAngle.toFixed(2)}° (esperado: 180°)`);
    
    const bondLength = calculateAtomDistance(co2Structure.atoms[0], co2Structure.atoms[1]);
    console.log(`CO2 - Comprimento C=O: ${bondLength.toFixed(3)} Å (esperado: ~1.16 Å)`);
  }
}

// Executar todos os testes
export function runAllTests() {
  testMolecularStructures();
  validateBondAngles();
  validateCalculatedStructures();
}
