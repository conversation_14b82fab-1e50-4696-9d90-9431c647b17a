/**
 * Supressor específico para erros do bundle React relacionados ao ResizeObserver
 * Intercepta erros nas linhas específicas 110072 e 110091 do bundle
 */

console.log('🎯 Carregando supressor específico do bundle...');

// Interceptar erros específicos do bundle
const suppressBundleErrors = () => {
  // Interceptar console.error com verificação específica do bundle
  const originalConsoleError = console.error;
  console.error = function(...args) {
    const message = args.join(' ');
    const stack = args.find(arg => arg && arg.stack)?.stack || '';
    
    // Verificar se é o erro específico do bundle
    if (message.includes('ResizeObserver loop completed') ||
        message.includes('undelivered notifications') ||
        stack.includes('110072') ||
        stack.includes('110091') ||
        stack.includes('handleError') ||
        args.some(arg => String(arg).includes('110072')) ||
        args.some(arg => String(arg).includes('110091'))) {
      
      console.log('🔇 Erro do ResizeObserver suprimido:', message.substring(0, 50) + '...');
      return; // Silenciar completamente
    }
    
    return originalConsoleError.apply(console, args);
  };

  // Interceptar window.onerror com foco nas linhas específicas
  const originalWindowError = window.onerror;
  window.onerror = function(message, source, lineno, colno, error) {
    // Verificar linhas específicas do bundle
    if (lineno === 110072 || lineno === 110091 ||
        String(message).includes('ResizeObserver') ||
        String(source).includes('bundle.js') && (
          String(message).includes('loop completed') ||
          String(message).includes('undelivered notifications') ||
          String(message).includes('handleError')
        )) {
      
      console.log(`🔇 Erro do bundle suprimido na linha ${lineno}:`, String(message).substring(0, 50) + '...');
      return true; // Prevenir propagação
    }
    
    if (originalWindowError) {
      return originalWindowError.call(this, message, source, lineno, colno, error);
    }
    return false;
  };

  // Interceptar erros não capturados
  const originalUnhandledRejection = window.onunhandledrejection;
  window.onunhandledrejection = function(event) {
    const reason = event.reason;
    const message = String(reason?.message || reason || '');
    
    if (message.includes('ResizeObserver') ||
        message.includes('loop completed') ||
        message.includes('undelivered notifications') ||
        message.includes('110072') ||
        message.includes('110091')) {
      
      console.log('🔇 Promise rejection do ResizeObserver suprimida:', message.substring(0, 50) + '...');
      event.preventDefault();
      return;
    }
    
    if (originalUnhandledRejection) {
      return originalUnhandledRejection.call(this, event);
    }
  };

  // Interceptar addEventListener para eventos de erro
  const originalAddEventListener = EventTarget.prototype.addEventListener;
  EventTarget.prototype.addEventListener = function(type, listener, options) {
    if (type === 'error') {
      const wrappedListener = function(event) {
        const message = String(event.message || '');
        const filename = String(event.filename || '');
        
        if ((event.lineno === 110072 || event.lineno === 110091) ||
            message.includes('ResizeObserver') ||
            (filename.includes('bundle.js') && (
              message.includes('loop completed') ||
              message.includes('undelivered notifications')
            ))) {
          
          console.log(`🔇 Event error do ResizeObserver suprimido:`, message.substring(0, 50) + '...');
          event.preventDefault();
          event.stopPropagation();
          event.stopImmediatePropagation();
          return false;
        }
        
        if (typeof listener === 'function') {
          return listener.call(this, event);
        } else if (listener && typeof listener.handleEvent === 'function') {
          return listener.handleEvent(event);
        }
      };
      return originalAddEventListener.call(this, type, wrappedListener, options);
    }
    return originalAddEventListener.call(this, type, listener, options);
  };

  console.log('✅ Supressor específico do bundle ativado');
};

// Aplicar imediatamente
suppressBundleErrors();

// Aplicar novamente após um delay
setTimeout(suppressBundleErrors, 50);
setTimeout(suppressBundleErrors, 100);
setTimeout(suppressBundleErrors, 500);

// Aplicar quando o DOM estiver pronto
if (document.readyState === 'loading') {
  document.addEventListener('DOMContentLoaded', suppressBundleErrors);
} else {
  suppressBundleErrors();
}

// Aplicar quando a janela carregar
window.addEventListener('load', suppressBundleErrors);

// Interceptar especificamente a função handleError do React
const interceptReactHandleError = () => {
  // Tentar encontrar e interceptar a função handleError do React
  const scripts = document.querySelectorAll('script');
  scripts.forEach(script => {
    if (script.src && script.src.includes('bundle.js')) {
      console.log('🎯 Bundle React encontrado, aplicando interceptação específica...');
    }
  });
  
  // Interceptar qualquer função global que possa ser handleError
  if (window.handleError) {
    const originalHandleError = window.handleError;
    window.handleError = function(...args) {
      const message = args.join(' ');
      if (message.includes('ResizeObserver') || message.includes('loop completed')) {
        console.log('🔇 handleError do ResizeObserver interceptado');
        return;
      }
      return originalHandleError.apply(this, args);
    };
  }
};

// Aplicar interceptação do React
setTimeout(interceptReactHandleError, 1000);

export default suppressBundleErrors;
