/**
 * Supressor ultra-robusto para erros do ResizeObserver
 * Este arquivo pode ser importado em qualquer lugar que precise suprimir esses erros
 */

let isSuppressionActive = false;
let originalHandlers = {};

// Função para verificar se é erro do ResizeObserver
export const isResizeObserverError = (message) => {
  const msg = String(message || '').toLowerCase();
  return msg.includes('resizeobserver') ||
         msg.includes('undelivered notifications') ||
         msg.includes('loop completed') ||
         msg.includes('loop limit exceeded') ||
         msg.includes('resize observer loop') ||
         msg.includes('handleerror') ||
         msg.includes('loop completed with undelivered notifications') ||
         msg.includes('resizeobserver loop completed') ||
         msg.includes('resizeobserver loop limit') ||
         msg.includes('observer loop') ||
         msg.includes('110072') || // Linha específica do erro
         msg.includes('110091');   // Linha específica do erro
};

// Função principal de supressão
export const suppressResizeObserverErrors = () => {
  if (isSuppressionActive) return;
  isSuppressionActive = true;

  // Salvar handlers originais
  originalHandlers = {
    consoleError: console.error,
    consoleWarn: console.warn,
    windowError: window.onerror,
    windowUnhandledRejection: window.onunhandledrejection,
    addEventListener: window.addEventListener
  };

  // Suprimir console.error
  console.error = (...args) => {
    if (isResizeObserverError(args[0])) {
      return;
    }
    originalHandlers.consoleError.apply(console, args);
  };

  // Suprimir console.warn
  console.warn = (...args) => {
    if (isResizeObserverError(args[0])) {
      return;
    }
    originalHandlers.consoleWarn.apply(console, args);
  };

  // Suprimir window.onerror
  window.onerror = (message, source, lineno, colno, error) => {
    if (isResizeObserverError(message)) {
      return true;
    }
    if (originalHandlers.windowError) {
      return originalHandlers.windowError(message, source, lineno, colno, error);
    }
    return false;
  };

  // Suprimir unhandledrejection
  window.onunhandledrejection = (event) => {
    if (isResizeObserverError(event.reason?.message || event.reason)) {
      event.preventDefault();
      return;
    }
    if (originalHandlers.windowUnhandledRejection) {
      return originalHandlers.windowUnhandledRejection(event);
    }
  };

  // Interceptar addEventListener para erros
  window.addEventListener = function(type, listener, options) {
    if (type === 'error') {
      const wrappedListener = function(event) {
        if (isResizeObserverError(event.message || event.error?.message)) {
          event.preventDefault();
          event.stopPropagation();
          event.stopImmediatePropagation();
          return false;
        }
        if (typeof listener === 'function') {
          return listener.call(this, event);
        }
      };
      return originalHandlers.addEventListener.call(this, type, wrappedListener, options);
    }
    return originalHandlers.addEventListener.call(this, type, listener, options);
  };

  // Interceptar ResizeObserver diretamente
  if (window.ResizeObserver) {
    const OriginalResizeObserver = window.ResizeObserver;
    window.ResizeObserver = class extends OriginalResizeObserver {
      constructor(callback) {
        const wrappedCallback = (entries, observer) => {
          try {
            callback(entries, observer);
          } catch (error) {
            if (!isResizeObserverError(error.message)) {
              throw error;
            }
            // Silenciar erros do ResizeObserver
          }
        };
        super(wrappedCallback);
      }
    };
  }

  // Interceptar requestAnimationFrame para capturar erros assíncronos
  const originalRAF = window.requestAnimationFrame;
  window.requestAnimationFrame = function(callback) {
    const wrappedCallback = function(timestamp) {
      try {
        return callback(timestamp);
      } catch (error) {
        if (!isResizeObserverError(error.message)) {
          throw error;
        }
        // Silenciar erros do ResizeObserver em RAF
      }
    };
    return originalRAF.call(this, wrappedCallback);
  };

  // Interceptação ultra-agressiva para capturar erros do bundle
  const originalThrow = Error.prototype.constructor;
  Error.prototype.constructor = function(message) {
    if (isResizeObserverError(message)) {
      // Criar erro silencioso
      const silentError = originalThrow.call(this, 'Suppressed ResizeObserver error');
      silentError.suppressed = true;
      return silentError;
    }
    return originalThrow.call(this, message);
  };

  // Interceptar setTimeout e setInterval para capturar erros assíncronos
  const originalSetTimeout = window.setTimeout;
  window.setTimeout = function(callback, delay, ...args) {
    const wrappedCallback = function() {
      try {
        return callback.apply(this, arguments);
      } catch (error) {
        if (!isResizeObserverError(error.message)) {
          throw error;
        }
        // Silenciar erros do ResizeObserver em setTimeout
      }
    };
    return originalSetTimeout.call(this, wrappedCallback, delay, ...args);
  };

  // Interceptar Promise.reject para capturar rejeições do ResizeObserver
  const originalPromiseReject = Promise.reject;
  Promise.reject = function(reason) {
    if (isResizeObserverError(String(reason))) {
      // Retornar promise resolvida em vez de rejeitada
      return Promise.resolve();
    }
    return originalPromiseReject.call(this, reason);
  };

  console.log('🛡️ Ultra-aggressive ResizeObserver error suppression activated');
};

// Função para restaurar handlers originais
export const restoreOriginalHandlers = () => {
  if (!isSuppressionActive || !originalHandlers.consoleError) return;

  console.error = originalHandlers.consoleError;
  console.warn = originalHandlers.consoleWarn;
  window.onerror = originalHandlers.windowError;
  window.onunhandledrejection = originalHandlers.windowUnhandledRejection;
  window.addEventListener = originalHandlers.addEventListener;

  isSuppressionActive = false;
  console.log('🔄 Original error handlers restored');
};

// Função para aplicar supressão temporária
export const withResizeObserverSuppression = async (fn) => {
  const wasActive = isSuppressionActive;
  
  if (!wasActive) {
    suppressResizeObserverErrors();
  }
  
  try {
    return await fn();
  } finally {
    if (!wasActive) {
      restoreOriginalHandlers();
    }
  }
};

// Função para criar um wrapper de componente React com supressão
export const withResizeObserverSuppressionHOC = (Component) => {
  return function WrappedComponent(props) {
    // Importar React dinamicamente se necessário
    if (typeof React !== 'undefined') {
      React.useEffect(() => {
        suppressResizeObserverErrors();
        return () => {
          // Não restaurar automaticamente para evitar conflitos
          // restoreOriginalHandlers();
        };
      }, []);

      return React.createElement(Component, props);
    }

    // Fallback se React não estiver disponível
    return Component(props);
  };
};

// Auto-aplicar supressão se estiver em ambiente de desenvolvimento
if (process.env.NODE_ENV === 'development') {
  // Aguardar um pouco para garantir que o DOM esteja pronto
  setTimeout(() => {
    suppressResizeObserverErrors();
  }, 100);
}

// Aplicar supressão imediatamente para casos críticos
suppressResizeObserverErrors();

export default {
  suppressResizeObserverErrors,
  restoreOriginalHandlers,
  withResizeObserverSuppression,
  withResizeObserverSuppressionHOC,
  isResizeObserverError
};
