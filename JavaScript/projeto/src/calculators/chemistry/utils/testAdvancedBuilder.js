/**
 * Teste do Sistema Avançado de Construção de Moléculas
 */

import { generateAdvancedMolecule, parseIUPACNomenclature } from './advancedMoleculeBuilder.js';

// Função para testar o sistema
export function testAdvancedMoleculeBuilder() {
  console.log('🧪 Testando Sistema Avançado de Construção de Moléculas');
  
  const testCases = [
    'propane',
    'butane', 
    'pentane',
    'hexane',
    '2-methylbutane',
    'hex-2-ol',
    '2,3-dimethylbutane'
  ];
  
  testCases.forEach(testCase => {
    console.log(`\n🔬 Testando: ${testCase}`);
    
    // Testar parser
    const parsed = parseIUPACNomenclature(testCase);
    console.log('Parser result:', parsed);
    
    // Testar geração de molécula
    const molecule = generateAdvancedMolecule(testCase);
    if (molecule) {
      console.log(`✅ Molécula gerada com ${molecule.atoms.length} átomos e ${molecule.bonds.length} ligações`);
      console.log('Átomos de carbono:', molecule.atoms.filter(a => a.element === 'C').length);
      console.log('Átomos de hidrogênio:', molecule.atoms.filter(a => a.element === 'H').length);
      
      // Verificar geometria do propano
      if (testCase === 'propane' && molecule.atoms.length >= 3) {
        const carbons = molecule.atoms.filter(a => a.element === 'C');
        if (carbons.length >= 3) {
          // Calcular ângulo entre os três primeiros carbonos
          const c1 = carbons[0];
          const c2 = carbons[1]; 
          const c3 = carbons[2];
          
          const v1 = { x: c1.x - c2.x, y: c1.y - c2.y, z: c1.z - c2.z };
          const v2 = { x: c3.x - c2.x, y: c3.y - c2.y, z: c3.z - c2.z };
          
          const dot = v1.x * v2.x + v1.y * v2.y + v1.z * v2.z;
          const mag1 = Math.sqrt(v1.x ** 2 + v1.y ** 2 + v1.z ** 2);
          const mag2 = Math.sqrt(v2.x ** 2 + v2.y ** 2 + v2.z ** 2);
          
          const angle = Math.acos(dot / (mag1 * mag2)) * 180 / Math.PI;
          console.log(`🔍 Ângulo C-C-C no propano: ${angle.toFixed(2)}° (esperado: ~109.47°)`);
        }
      }
    } else {
      console.log('❌ Falha na geração da molécula');
    }
  });
}

// Executar teste se este arquivo for importado
if (typeof window !== 'undefined') {
  window.testAdvancedMoleculeBuilder = testAdvancedMoleculeBuilder;
}
