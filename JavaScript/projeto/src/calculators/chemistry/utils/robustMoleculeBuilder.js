/**
 * Sistema Robusto de Construção de Moléculas 3D
 * Respeita regra do octeto e valida todas as ligações
 */

// Constantes físicas
const BOND_LENGTHS = {
  'C-C': 1.54,
  'C-H': 1.09,
  'C=C': 1.34,
  'C≡C': 1.20
};

const TETRAHEDRAL_ANGLE = 109.47 * Math.PI / 180;

// Regras de valência
const VALENCE_RULES = {
  'C': 4,  // Carbono precisa de 4 ligações
  'H': 1,  // Hidrogênio precisa de 1 ligação
  'O': 2,  // Oxigênio precisa de 2 ligações
  'N': 3   // Nitrogênio precisa de 3 ligações
};

// Classe para construir moléculas com validação
class MoleculeBuilder {
  constructor() {
    this.atoms = [];
    this.bonds = [];
    this.nextAtomId = 0;
  }

  // Adicionar átomo
  addAtom(element, x, y, z) {
    const atom = {
      id: this.nextAtomId++,
      element,
      x, y, z,
      bonds: [],
      valenceUsed: 0,
      valenceNeeded: VALENCE_RULES[element] || 4
    };
    this.atoms.push(atom);
    return atom.id;
  }

  // Adicionar ligação com validação
  addBond(atomId1, atomId2, order = 1) {
    const atom1 = this.atoms.find(a => a.id === atomId1);
    const atom2 = this.atoms.find(a => a.id === atomId2);

    if (!atom1 || !atom2) {
      console.error(`Átomos não encontrados: ${atomId1}, ${atomId2}`);
      return false;
    }

    // Verificar se a ligação não excede a valência
    if (atom1.valenceUsed + order > atom1.valenceNeeded) {
      console.error(`Ligação excederia valência do ${atom1.element} (${atom1.valenceUsed + order}/${atom1.valenceNeeded})`);
      return false;
    }

    if (atom2.valenceUsed + order > atom2.valenceNeeded) {
      console.error(`Ligação excederia valência do ${atom2.element} (${atom2.valenceUsed + order}/${atom2.valenceNeeded})`);
      return false;
    }

    // Adicionar ligação
    const bond = {
      from: atomId1,
      to: atomId2,
      order,
      type: `${atom1.element}-${atom2.element}`
    };

    this.bonds.push(bond);
    atom1.bonds.push(bond);
    atom2.bonds.push(bond);
    atom1.valenceUsed += order;
    atom2.valenceUsed += order;

    console.log(`✅ Ligação adicionada: ${atom1.element}(${atom1.valenceUsed}/${atom1.valenceNeeded}) - ${atom2.element}(${atom2.valenceUsed}/${atom2.valenceNeeded})`);
    return true;
  }

  // Adicionar hidrogênios automaticamente para completar valência
  addHydrogens() {
    const carbonsNeedingH = this.atoms.filter(atom => 
      atom.element === 'C' && atom.valenceUsed < atom.valenceNeeded
    );

    carbonsNeedingH.forEach(carbon => {
      const hNeeded = carbon.valenceNeeded - carbon.valenceUsed;
      console.log(`Carbono ${carbon.id} precisa de ${hNeeded} hidrogênios`);

      for (let i = 0; i < hNeeded; i++) {
        // Calcular posição tetraédrica para hidrogênio
        const hPos = this.calculateHydrogenPosition(carbon, i, hNeeded);
        const hId = this.addAtom('H', hPos.x, hPos.y, hPos.z);
        this.addBond(carbon.id, hId, 1);
      }
    });
  }

  // Calcular posição tetraédrica para hidrogênio
  calculateHydrogenPosition(carbon, hIndex, totalH) {
    const chBond = BOND_LENGTHS['C-H'];
    
    // Encontrar direções das ligações C-C existentes
    const ccDirections = [];
    carbon.bonds.forEach(bond => {
      const otherId = bond.from === carbon.id ? bond.to : bond.from;
      const otherAtom = this.atoms.find(a => a.id === otherId);
      
      if (otherAtom && otherAtom.element === 'C') {
        const dir = {
          x: otherAtom.x - carbon.x,
          y: otherAtom.y - carbon.y,
          z: otherAtom.z - carbon.z
        };
        const length = Math.sqrt(dir.x**2 + dir.y**2 + dir.z**2);
        ccDirections.push({
          x: dir.x / length,
          y: dir.y / length,
          z: dir.z / length
        });
      }
    });

    // Gerar direção tetraédrica para hidrogênio
    let angle = (hIndex * 2 * Math.PI / totalH) + (carbon.id * Math.PI / 6);
    
    // Se há ligações C-C, ajustar para evitar sobreposição
    if (ccDirections.length > 0) {
      angle += Math.PI / 4; // Offset para evitar sobreposição
    }

    const direction = {
      x: Math.cos(angle) * Math.sin(TETRAHEDRAL_ANGLE),
      y: Math.sin(angle) * Math.sin(TETRAHEDRAL_ANGLE),
      z: Math.cos(TETRAHEDRAL_ANGLE) * (hIndex % 2 ? 1 : -1)
    };

    return {
      x: carbon.x + chBond * direction.x,
      y: carbon.y + chBond * direction.y,
      z: carbon.z + chBond * direction.z
    };
  }

  // Validar estrutura final
  validateStructure() {
    let isValid = true;
    const errors = [];

    this.atoms.forEach(atom => {
      if (atom.valenceUsed !== atom.valenceNeeded) {
        isValid = false;
        errors.push(`${atom.element} ${atom.id}: ${atom.valenceUsed}/${atom.valenceNeeded} ligações`);
      }
    });

    if (!isValid) {
      console.error('❌ Estrutura inválida:', errors);
    } else {
      console.log('✅ Estrutura válida - todas as valências corretas');
    }

    return { isValid, errors };
  }

  // Converter para formato compatível
  toMoleculeFormat() {
    const validation = this.validateStructure();
    
    if (!validation.isValid) {
      console.error('Não é possível retornar estrutura inválida');
      return null;
    }

    return {
      atoms: this.atoms.map(atom => ({
        element: atom.element,
        x: atom.x,
        y: atom.y,
        z: atom.z
      })),
      bonds: this.bonds.map(bond => ({
        from: bond.from,
        to: bond.to,
        order: bond.order,
        type: bond.type
      })),
      geometry: 'tetrahedral',
      bondAngle: 109.47,
      validation
    };
  }
}

// Função principal para gerar moléculas
export function generateRobustMolecule(input) {
  console.log(`🔬 Sistema robusto gerando: "${input}"`);
  
  const normalized = input.toLowerCase().trim();
  
  // Mapear para funções específicas
  const generators = {
    'methane': () => generateMethaneRobust(),
    'ethane': () => generateEthaneRobust(),
    'propane': () => generatePropaneRobust(),
    'butane': () => generateButaneRobust(),
    'pentane': () => generatePentaneRobust(),
    'hexane': () => generateHexaneRobust(),
    'ethyne': () => generateEthyneRobust(),
    'acetylene': () => generateEthyneRobust(),
    'ch4': () => generateMethaneRobust(),
    'c2h6': () => generateEthaneRobust(),
    'c3h8': () => generatePropaneRobust(),
    'c4h10': () => generateButaneRobust(),
    'c5h12': () => generatePentaneRobust(),
    'c6h14': () => generateHexaneRobust(),
    'c2h2': () => generateEthyneRobust()
  };

  const generator = generators[normalized];
  if (generator) {
    const result = generator();
    if (result) {
      console.log(`✅ Gerado com sucesso: ${result.atoms.length} átomos, ${result.bonds.length} ligações`);
    }
    return result;
  }

  console.log(`❌ Molécula não reconhecida: "${input}"`);
  return null;
}

// Metano (CH4)
function generateMethaneRobust() {
  const builder = new MoleculeBuilder();
  
  // Adicionar carbono central
  const cId = builder.addAtom('C', 0, 0, 0);
  
  // Adicionar hidrogênios automaticamente
  builder.addHydrogens();
  
  return builder.toMoleculeFormat();
}

// Etano (C2H6)
function generateEthaneRobust() {
  const builder = new MoleculeBuilder();
  const ccBond = BOND_LENGTHS['C-C'];
  
  // Adicionar carbonos
  const c1Id = builder.addAtom('C', -ccBond/2, 0, 0);
  const c2Id = builder.addAtom('C', ccBond/2, 0, 0);
  
  // Ligação C-C
  builder.addBond(c1Id, c2Id, 1);
  
  // Adicionar hidrogênios automaticamente
  builder.addHydrogens();
  
  return builder.toMoleculeFormat();
}

// Propano (C3H8)
function generatePropaneRobust() {
  const builder = new MoleculeBuilder();
  const ccBond = BOND_LENGTHS['C-C'];
  
  // Posições dos carbonos com ângulo tetraédrico
  const c1Id = builder.addAtom('C', 0, 0, 0);
  const c2Id = builder.addAtom('C', ccBond, 0, 0);
  const c3Id = builder.addAtom('C', 
    ccBond + ccBond * Math.cos(Math.PI - TETRAHEDRAL_ANGLE),
    ccBond * Math.sin(Math.PI - TETRAHEDRAL_ANGLE),
    0
  );
  
  // Ligações C-C
  builder.addBond(c1Id, c2Id, 1);
  builder.addBond(c2Id, c3Id, 1);
  
  // Adicionar hidrogênios automaticamente
  builder.addHydrogens();
  
  return builder.toMoleculeFormat();
}

// Butano (C4H10)
function generateButaneRobust() {
  const builder = new MoleculeBuilder();
  const ccBond = BOND_LENGTHS['C-C'];
  
  // Cadeia zigzag com 4 carbonos
  const c1Id = builder.addAtom('C', 0, 0, 0);
  const c2Id = builder.addAtom('C', ccBond, 0, 0);
  const c3Id = builder.addAtom('C', 
    ccBond + ccBond * Math.cos(Math.PI - TETRAHEDRAL_ANGLE),
    ccBond * Math.sin(Math.PI - TETRAHEDRAL_ANGLE),
    0
  );
  const c4Id = builder.addAtom('C',
    ccBond + ccBond * Math.cos(Math.PI - TETRAHEDRAL_ANGLE) + ccBond * Math.cos(-TETRAHEDRAL_ANGLE),
    ccBond * Math.sin(Math.PI - TETRAHEDRAL_ANGLE) + ccBond * Math.sin(-TETRAHEDRAL_ANGLE),
    0
  );
  
  // Ligações C-C
  builder.addBond(c1Id, c2Id, 1);
  builder.addBond(c2Id, c3Id, 1);
  builder.addBond(c3Id, c4Id, 1);
  
  // Adicionar hidrogênios automaticamente
  builder.addHydrogens();
  
  return builder.toMoleculeFormat();
}

// Pentano (C5H12)
function generatePentaneRobust() {
  return generateAlkaneChainRobust(5);
}

// Hexano (C6H14)
function generateHexaneRobust() {
  return generateAlkaneChainRobust(6);
}

// Etino (C2H2)
function generateEthyneRobust() {
  const builder = new MoleculeBuilder();
  const ccBond = BOND_LENGTHS['C≡C'];
  
  // Carbonos
  const c1Id = builder.addAtom('C', -ccBond/2, 0, 0);
  const c2Id = builder.addAtom('C', ccBond/2, 0, 0);
  
  // Ligação tripla C≡C
  builder.addBond(c1Id, c2Id, 3);
  
  // Adicionar hidrogênios automaticamente
  builder.addHydrogens();
  
  return builder.toMoleculeFormat();
}

// Gerador genérico para alcanos
function generateAlkaneChainRobust(n) {
  const builder = new MoleculeBuilder();
  const ccBond = BOND_LENGTHS['C-C'];
  const carbonIds = [];
  
  // Gerar carbonos em cadeia zigzag
  for (let i = 0; i < n; i++) {
    let x, y, z = 0;
    
    if (i === 0) {
      x = 0; y = 0;
    } else if (i === 1) {
      x = ccBond; y = 0;
    } else {
      // Calcular posição com ângulo tetraédrico
      const prevAtom = builder.atoms[carbonIds[i-1]];
      const prevPrevAtom = builder.atoms[carbonIds[i-2]];
      
      const angle = (i % 2 === 0) ? TETRAHEDRAL_ANGLE : -TETRAHEDRAL_ANGLE;
      const dx = prevAtom.x - prevPrevAtom.x;
      const dy = prevAtom.y - prevPrevAtom.y;
      
      x = prevAtom.x + ccBond * Math.cos(Math.atan2(dy, dx) + Math.PI - angle);
      y = prevAtom.y + ccBond * Math.sin(Math.atan2(dy, dx) + Math.PI - angle);
    }
    
    const cId = builder.addAtom('C', x, y, z);
    carbonIds.push(cId);
    
    // Adicionar ligação C-C
    if (i > 0) {
      builder.addBond(carbonIds[i-1], carbonIds[i], 1);
    }
  }
  
  // Adicionar hidrogênios automaticamente
  builder.addHydrogens();
  
  return builder.toMoleculeFormat();
}

// Função de teste para o console
if (typeof window !== 'undefined') {
  window.testRobustSystem = function() {
    console.log('🧪 TESTE DO SISTEMA ROBUSTO');

    const testCases = ['methane', 'ethane', 'propane', 'butane', 'pentane', 'ethyne'];

    testCases.forEach(molecule => {
      console.log(`\n🔬 Testando: ${molecule}`);
      const result = generateRobustMolecule(molecule);

      if (result) {
        const carbons = result.atoms.filter(a => a.element === 'C');
        const hydrogens = result.atoms.filter(a => a.element === 'H');

        console.log(`✅ ${carbons.length}C + ${hydrogens.length}H = ${result.atoms.length} átomos`);
        console.log(`🔗 ${result.bonds.length} ligações`);

        // Verificar fórmulas esperadas
        const expectedFormulas = {
          'methane': { C: 1, H: 4 },
          'ethane': { C: 2, H: 6 },
          'propane': { C: 3, H: 8 },
          'butane': { C: 4, H: 10 },
          'pentane': { C: 5, H: 12 },
          'ethyne': { C: 2, H: 2 }
        };

        const expected = expectedFormulas[molecule];
        if (expected) {
          const correct = carbons.length === expected.C && hydrogens.length === expected.H;
          console.log(`${correct ? '✅' : '❌'} Fórmula: C${carbons.length}H${hydrogens.length} (esperado: C${expected.C}H${expected.H})`);
        }

        // Verificar valência
        if (result.validation?.isValid) {
          console.log('✅ Valência correta');
        } else {
          console.log('❌ Valência incorreta:', result.validation?.errors);
        }

      } else {
        console.log('❌ Falhou');
      }
    });
  };
}
