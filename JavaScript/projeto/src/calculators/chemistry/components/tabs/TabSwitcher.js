import React, { useState, useEffect, useRef } from 'react';
import './TabSwitcher.css';

/**
 * TabSwitcher - Um componente que simula o efeito visual de um carousel
 * mas usando um sistema de abas com animações de transição 3D
 *
 * @param {Object} props - Propriedades do componente
 * @param {Array} props.tabs - Array de objetos com {id, title, component}
 * @param {Object} props.style - Estilos adicionais para o componente
 * @param {Object} props.menuStyle - Estilos adicionais para o menu de navegação
 * @param {Object} props.contentStyle - Estilos adicionais para o conteúdo
 * @param {string} props.className - Classes adicionais para o componente
 * @param {string} props.menuClassName - Classes adicionais para o menu
 * @param {string} props.contentClassName - Classes adicionais para o conteúdo
 */
const TabSwitcher = ({
  tabs,
  style = {},
  menuStyle = {},
  contentStyle = {},
  className = '',
  menuClassName = '',
  contentClassName = ''
}) => {
  const [activeTab, setActiveTab] = useState(tabs[0].id);
  const [displayedTabs, setDisplayedTabs] = useState([]);
  const [isTransitioning, setIsTransitioning] = useState(false);
  const [direction, setDirection] = useState(null); // 'next' ou 'prev'
  const [newTabEntering, setNewTabEntering] = useState(null); // Tab que está entrando
  const [previousTab, setPreviousTab] = useState(null); // Tab anterior durante a transição
  const tabsContainerRef = useRef(null);

  // Inicializar os tabs exibidos
  useEffect(() => {
    initializeDisplayedTabs();
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, []);

  // Inicializar os tabs exibidos
  const initializeDisplayedTabs = () => {
    const activeIndex = tabs.findIndex(tab => tab.id === activeTab);
    let initialTabs = getVisibleTabsForIndex(activeIndex);
    setDisplayedTabs(initialTabs);
  };

  // Obter os tabs visíveis para um determinado índice
  const getVisibleTabsForIndex = (activeIndex) => {
    // Caso normal: mostrar o tab ativo e um de cada lado
    if (activeIndex > 0 && activeIndex < tabs.length - 1) {
      return [
        { ...tabs[activeIndex - 1], position: 'left' },
        { ...tabs[activeIndex], position: 'center' },
        { ...tabs[activeIndex + 1], position: 'right' }
      ];
    }
    // Caso especial: primeiro tab ativo - mostrar o último tab à esquerda
    else if (activeIndex === 0) {
      return [
        { ...tabs[tabs.length - 1], position: 'left' },
        { ...tabs[0], position: 'center' },
        { ...tabs[1], position: 'right' }
      ];
    }
    // Caso especial: último tab ativo - mostrar o primeiro tab à direita
    else if (activeIndex === tabs.length - 1) {
      return [
        { ...tabs[tabs.length - 2], position: 'left' },
        { ...tabs[tabs.length - 1], position: 'center' },
        { ...tabs[0], position: 'right' }
      ];
    }
    return [];
  };

  // Determinar a direção correta com base na posição dos tabs
  const determineDirection = (currentIndex, targetIndex) => {
    // Caso especial: navegando do último para o primeiro tab
    if (currentIndex === tabs.length - 1 && targetIndex === 0) {
      return 'next'; // Continuar para a direita (efeito de loop)
    }

    // Caso especial: navegando do primeiro para o último tab
    if (currentIndex === 0 && targetIndex === tabs.length - 1) {
      return 'prev'; // Continuar para a esquerda (efeito de loop)
    }

    // Casos normais
    // Se estamos indo para a direita (índice maior)
    if (targetIndex > currentIndex) {
      return 'next';
    }
    // Se estamos indo para a esquerda (índice menor)
    else if (targetIndex < currentIndex) {
      return 'prev';
    }

    // Se estamos no mesmo tab (não deveria acontecer)
    return null;
  };

  // Obter o próximo tab que entrará na visualização
  const getNextEnteringTab = (currentActiveIndex, navDirection) => {
    if (navDirection === 'next') {
      // Se estamos indo para a direita, o novo tab entrará pela direita
      const nextIndex = (currentActiveIndex + 2) % tabs.length;
      return { ...tabs[nextIndex], entering: 'right' };
    } else if (navDirection === 'prev') {
      // Se estamos indo para a esquerda, o novo tab entrará pela esquerda
      const prevIndex = (currentActiveIndex - 2 + tabs.length) % tabs.length;
      return { ...tabs[prevIndex], entering: 'left' };
    }
    return null;
  };

  // Função para lidar com a mudança de tab
  const switchTab = (tabId, navDirection) => {
    // Evitar múltiplas transições simultâneas ou mudança para o tab atual
    if (tabId === activeTab || isTransitioning) {
      return;
    }

    // Determinar a direção com base na distância entre os tabs
    const currentIndex = tabs.findIndex(tab => tab.id === activeTab);
    const targetIndex = tabs.findIndex(tab => tab.id === tabId);

    // Garantir que a direção seja correta
    const correctDirection = determineDirection(currentIndex, targetIndex);

    // Se a direção fornecida não corresponder à direção real, usar a direção correta
    if (correctDirection && navDirection !== correctDirection) {
      navDirection = correctDirection;
    }

    // Definir a direção da navegação (necessário para as classes CSS)
    setDirection(navDirection);

    // Iniciar a transição
    setIsTransitioning(true);

    // Obter o tab que entrará na visualização
    const enteringTab = getNextEnteringTab(currentIndex, navDirection);
    setNewTabEntering(enteringTab);

    // Atualizar o tab ativo
    setActiveTab(tabId);

    // Atualizar os tabs exibidos após a transição
    const transitionDuration = 600; // Deve corresponder à variável --carousel-animation-duration no CSS (0.6s = 600ms)
    setTimeout(() => {
      const newActiveIndex = tabs.findIndex(tab => tab.id === tabId);
      const newDisplayedTabs = getVisibleTabsForIndex(newActiveIndex);
      setDisplayedTabs(newDisplayedTabs);
      setIsTransitioning(false);
      setDirection(null);
      setNewTabEntering(null);
    }, transitionDuration);
  };

  // Obter o próximo tab (com suporte para loop infinito)
  const nextTab = () => {
    const currentIndex = tabs.findIndex(tab => tab.id === activeTab);
    // Usar operador módulo para garantir que voltamos ao início quando chegamos ao fim
    const nextIndex = (currentIndex + 1) % tabs.length;
    switchTab(tabs[nextIndex].id, 'next');
  };

  // Obter o tab anterior (com suporte para loop infinito)
  const prevTab = () => {
    const currentIndex = tabs.findIndex(tab => tab.id === activeTab);
    // Adicionar tabs.length antes de aplicar o módulo para garantir que o resultado seja positivo
    const prevIndex = (currentIndex - 1 + tabs.length) % tabs.length;
    switchTab(tabs[prevIndex].id, 'prev');
  };

  // Adicionar suporte para navegação com teclado
  useEffect(() => {
    const handleKeyDown = (e) => {
      if (e.key === 'ArrowRight') {
        nextTab();
      } else if (e.key === 'ArrowLeft') {
        prevTab();
      }
    };

    window.addEventListener('keydown', handleKeyDown);
    return () => {
      window.removeEventListener('keydown', handleKeyDown);
    };
  }, [activeTab]); // eslint-disable-line react-hooks/exhaustive-deps

  // Verificar se tabs existe e tem pelo menos um item
  if (!tabs || tabs.length === 0) {
    return <div className="tab-switcher-error">No tabs provided</div>;
  }

  return (
    <div className={`tab-switcher ${className}`} style={style}>
      {/* Navegação superior */}
      <div className={`tab-navigation ${menuClassName}`} style={menuStyle}>
        <div className="tab-nav-container">
          <button
            className="tab-nav-button prev"
            onClick={prevTab}
            title="Previous"
          >
            &#9664;
          </button>

          <div
            ref={tabsContainerRef}
            className={`tab-buttons ${isTransitioning ? 'transitioning' : ''} ${direction ? `direction-${direction}` : ''}`}
          >
            {/* Tabs atualmente visíveis */}
            {displayedTabs.map((tab) => (
              <button
                key={`${tab.id}-${tab.position}`}
                className={`tab-button position-${tab.position} ${activeTab === tab.id ? 'active' : ''}`}
                onClick={() => switchTab(tab.id, tab.position === 'left' ? 'prev' : 'next')}
              >
                {tab.title}
              </button>
            ))}

            {/* Tab que está entrando durante a transição */}
            {isTransitioning && newTabEntering && (
              <button
                key={`${newTabEntering.id}-entering`}
                className={`tab-button entering-${newTabEntering.entering}`}
                onClick={() => {}}
              >
                {newTabEntering.title}
              </button>
            )}
          </div>

          <button
            className="tab-nav-button next"
            onClick={nextTab}
            title="Next"
          >
            &#9654;
          </button>
        </div>
      </div>

      {/* Conteúdo dos tabs */}
      <div className={`tab-content ${contentClassName}`} style={contentStyle}>
        <div className={`tab-content-wrapper ${isTransitioning ? 'fade' : 'visible'}`}>
          {tabs.find(tab => tab.id === activeTab)?.component}
        </div>
      </div>
    </div>
  );
};

export default TabSwitcher;
