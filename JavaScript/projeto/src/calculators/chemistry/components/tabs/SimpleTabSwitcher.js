import React from 'react';
import TabSwitcher from './TabSwitcher';

/**
 * SimpleTabSwitcher - Versão do SimpleCarousel que usa o TabSwitcher
 * em vez de um carousel tradicional
 *
 * @param {Object} props - Propriedades do componente
 * @param {Array} props.slides - Array de objetos com {id, title, component}
 * @param {Object} props.style - Estilos adicionais para o componente
 * @param {Object} props.menuStyle - Estilos adicionais para o menu de navegação
 * @param {Object} props.contentStyle - Estilos adicionais para o conteúdo
 * @param {string} props.className - Classes adicionais para o componente
 * @param {string} props.menuClassName - Classes adicionais para o menu
 * @param {string} props.contentClassName - Classes adicionais para o conteúdo
 */
const SimpleTabSwitcher = ({
  slides,
  style = {},
  menuStyle = {},
  contentStyle = {},
  className = '',
  menuClassName = '',
  contentClassName = ''
}) => {
  // Converter os slides para o formato esperado pelo TabSwitcher
  const tabs = slides.map(slide => ({
    id: slide.id,
    title: slide.title,
    component: slide.component
  }));

  return (
    <TabSwitcher
      tabs={tabs}
      style={style}
      menuStyle={menuStyle}
      contentStyle={contentStyle}
      className={className}
      menuClassName={menuClassName}
      contentClassName={contentClassName}
    />
  );
};

export default SimpleTabSwitcher;
