/* Variáveis para animações e cores - similar ao carousel original */
:root {
  --primary-color: #4CAF50;
  --border-color: #444;
  --carousel-transition-duration: 0.5s;
  --carousel-transition-timing: ease-in-out;
  --carousel-item-width: 120px;
  --carousel-item-height: 30px;
  --carousel-item-gap: 180px;
  --carousel-item-scale: 0.85;
  --carousel-item-z-distance: -50px;
  --carousel-animation-duration: 0.6s;

  /* Variáveis para o TabSwitcher */
  --tab-transition-duration: var(--carousel-animation-duration);
  --tab-transition-timing: cubic-bezier(0.25, 0.1, 0.25, 1);
  --tab-active-color: var(--primary-color);
  --tab-inactive-color: #aaa;
  --tab-bg-color: rgba(0, 0, 0, 0.2);
  --tab-active-bg-color: rgba(0, 0, 0, 0.3);
  --tab-border-color: var(--border-color);
  --tab-active-border-color: var(--primary-color);
  --tab-shadow-color: rgba(0, 0, 0, 0.3);
  --tab-active-shadow-color: rgba(76, 175, 80, 0.3);
}

/* Container principal - similar ao carousel-wrapper */
.tab-switcher {
  display: flex;
  flex-direction: column;
  width: 100%;
  margin-bottom: 10px;
  gap: 5px;
  align-items: center;
  position: relative;
  box-sizing: border-box;
}

/* Navegação superior - similar ao carousel-navigation */
.tab-navigation {
  margin-bottom: 5px;
  width: 100%;
  display: flex;
  flex-direction: column;
  align-items: center;
  position: relative;
  z-index: 10;
}

/* Container das abas - similar ao carousel-tabs */
.tab-nav-container {
  display: flex;
  align-items: center;
  justify-content: center;
  width: 100%;
  margin: 0;
  position: relative;
  background-color: rgba(0, 0, 0, 0.3);
  border-top: 1px solid var(--border-color);
  border-bottom: 1px solid var(--border-color);
  border-left: none;
  border-right: none;
  border-radius: 0;
  padding: 8px 0;
  perspective: 1200px;
  perspective-origin: center center;
  overflow: hidden;
}

/* Container das abas visíveis - similar ao carousel-tabs-container */
.tab-buttons {
  display: flex;
  align-items: center;
  justify-content: center;
  transform-style: preserve-3d;
  position: relative;
  width: 100%;
  max-width: 500px;
  margin: 0 auto;
  height: 50px;
  transition: transform 0.3s ease;
  perspective: 1000px;
}

/* Botões de abas - similar ao carousel-tab */
.tab-button {
  background-color: rgba(0, 0, 0, 0.2);
  border: 2px solid var(--border-color);
  border-radius: 8px;
  color: #aaa;
  font-size: 13px;
  font-weight: 600;
  cursor: pointer;
  padding: 0;
  width: var(--carousel-item-width);
  height: var(--carousel-item-height);
  text-align: center;
  position: absolute;
  transform-style: preserve-3d;
  backface-visibility: hidden;
  display: flex;
  align-items: center;
  justify-content: center;
  transition: transform var(--carousel-transition-duration) var(--carousel-transition-timing),
              opacity var(--carousel-transition-duration) var(--carousel-transition-timing),
              color var(--carousel-transition-duration) var(--carousel-transition-timing),
              border-color var(--carousel-transition-duration) var(--carousel-transition-timing),
              background-color var(--carousel-transition-duration) var(--carousel-transition-timing);
  will-change: transform, opacity, color, border-color, background-color;
  left: 50%;
  margin-left: calc(-1 * var(--carousel-item-width) / 2);
  top: 50%;
  margin-top: calc(-1 * var(--carousel-item-height) / 2);
}

/* Posicionamento das abas - similar ao carousel-tab.position-X */
.tab-button.position-left {
  transform: translateX(calc(-1 * var(--carousel-item-gap)))
             scale(var(--carousel-item-scale))
             translateZ(var(--carousel-item-z-distance))
             rotateY(25deg);
  opacity: 0.7;
}

.tab-button.position-center {
  transform: translateX(0) scale(1) translateZ(0) rotateY(0);
  z-index: 2;
}

.tab-button.position-right {
  transform: translateX(var(--carousel-item-gap))
             scale(var(--carousel-item-scale))
             translateZ(var(--carousel-item-z-distance))
             rotateY(-25deg);
  opacity: 0.7;
}

/* Efeito de roda - aba ativa (central) */
.tab-button.active {
  background-color: rgba(76, 175, 80, 0.1);
  border-color: var(--primary-color);
  color: var(--primary-color);
  box-shadow: 0 0 10px rgba(76, 175, 80, 0.3);
  transition: transform var(--carousel-transition-duration) var(--carousel-transition-timing),
              opacity var(--carousel-transition-duration) var(--carousel-transition-timing),
              color var(--carousel-transition-duration) var(--carousel-transition-timing),
              border-color var(--carousel-transition-duration) var(--carousel-transition-timing),
              background-color var(--carousel-transition-duration) var(--carousel-transition-timing),
              box-shadow var(--carousel-transition-duration) var(--carousel-transition-timing);
}

/* Efeito de hover e foco nas abas */
.tab-button:hover,
.tab-button:focus {
  color: var(--primary-color);
  outline: none;
  transition: transform var(--carousel-transition-duration) var(--carousel-transition-timing),
              opacity var(--carousel-transition-duration) var(--carousel-transition-timing),
              color 0.3s ease;
}

/* Preservar posição no hover para cada posição */
.tab-button.position-left:hover {
  transform: translateX(calc(-1 * var(--carousel-item-gap)))
             scale(var(--carousel-item-scale))
             translateZ(var(--carousel-item-z-distance))
             rotateY(25deg);
}

.tab-button.position-center:hover {
  transform: translateX(0) scale(1) translateZ(0) rotateY(0);
}

.tab-button.position-right:hover {
  transform: translateX(var(--carousel-item-gap))
             scale(var(--carousel-item-scale))
             translateZ(var(--carousel-item-z-distance))
             rotateY(-25deg);
}

/* Transições para todos os tabs */
.tab-button {
  transition: transform var(--carousel-animation-duration) cubic-bezier(0.25, 0.1, 0.25, 1),
              opacity var(--carousel-animation-duration) cubic-bezier(0.25, 0.1, 0.25, 1);
}

/* Botões de navegação (setas) - similar ao carousel-tab-button */
.tab-nav-button {
  background: none;
  border: none;
  color: #aaa;
  font-size: 16px;
  cursor: pointer;
  padding: 0;
  width: 25px;
  height: 25px;
  display: flex;
  align-items: center;
  justify-content: center;
  position: absolute;
  top: 50%;
  transform: translateY(-50%);
  z-index: 10;
  transition: color 0.3s ease;
  transform-style: flat;
  backface-visibility: visible;
  perspective: none;
  transform-origin: 50% 50%;
}

.tab-nav-button.prev {
  left: 10%;
}

.tab-nav-button.next {
  right: 10%;
}

.tab-nav-button:hover {
  color: var(--primary-color);
}

/* Sobrescrever qualquer transformação que possa ser aplicada por outros estilos */
.tab-nav-button:hover,
.tab-nav-button:focus,
.tab-nav-button:active {
  transform: translateY(-50%) !important;
}

/* Conteúdo das abas - similar ao carousel-content */
.tab-content {
  padding: 0;
  background-color: transparent;
  border: none;
  width: 100%;
  display: flex;
  justify-content: center;
  position: relative;
  min-height: 150px;
  overflow: visible;
  margin: 0 auto;
}

/* Wrapper do conteúdo - similar ao slide-content-wrapper */
.tab-content-wrapper {
  width: 100%;
  transition: opacity 0.4s ease;
}

.tab-content-wrapper.fade {
  opacity: 0.5;
}

.tab-content-wrapper.visible {
  opacity: 1;
}

/* Animações para transição de abas */
.tab-buttons.transitioning {
  will-change: contents;
  animation-play-state: running !important;
  transition: none;
}

/* Forçar animações para serem aplicadas */
.tab-buttons.transitioning .tab-button {
  animation-play-state: running !important;
  transition: transform var(--carousel-animation-duration) cubic-bezier(0.25, 0.1, 0.25, 1),
              opacity var(--carousel-animation-duration) cubic-bezier(0.25, 0.1, 0.25, 1) !important;
}

/* Efeito de transição para a direção 'next' */
.tab-buttons.direction-next.transitioning .tab-button.position-left {
  animation: slideOutToLeft var(--carousel-animation-duration) cubic-bezier(0.25, 0.1, 0.25, 1) forwards;
}

@keyframes slideOutToLeft {
  0% {
    transform: translateX(calc(-1 * var(--carousel-item-gap)))
               scale(var(--carousel-item-scale))
               translateZ(var(--carousel-item-z-distance))
               rotateY(25deg);
    opacity: 0.7;
    color: #aaa;
  }
  100% {
    transform: translateX(calc(-1.2 * var(--carousel-item-gap)))
               scale(0.8)
               translateZ(var(--carousel-item-z-distance))
               rotateY(30deg);
    opacity: 0;
    color: #aaa;
  }
}

.tab-buttons.direction-next.transitioning .tab-button.position-center:not(.active) {
  transform: translateX(calc(-1 * var(--carousel-item-gap)))
             scale(var(--carousel-item-scale))
             translateZ(var(--carousel-item-z-distance))
             rotateY(25deg);
  opacity: 0.7;
}

.tab-buttons.direction-next.transitioning .tab-button.position-right.active {
  transform: translateX(0) scale(1) translateZ(0) rotateY(0);
  opacity: 1;
  z-index: 3;
}

/* Efeito para novas opções entrando da direita */
.tab-buttons.direction-next.transitioning .tab-button.entering-right {
  transform: translateX(calc(1.2 * var(--carousel-item-gap)))
             scale(0.8)
             translateZ(calc(1.2 * var(--carousel-item-z-distance)))
             rotateY(-30deg);
  opacity: 0;
  animation: slideInFromRight var(--carousel-animation-duration) cubic-bezier(0.25, 0.1, 0.25, 1) forwards;
  position: absolute;
  color: #aaa; /* Manter a cor original durante a animação */
  transition: transform var(--carousel-transition-duration) var(--carousel-transition-timing),
              opacity var(--carousel-transition-duration) var(--carousel-transition-timing),
              color var(--carousel-transition-duration) var(--carousel-transition-timing);
}

@keyframes slideInFromRight {
  0% {
    transform: translateX(calc(1.2 * var(--carousel-item-gap)))
               scale(0.8)
               translateZ(calc(1.2 * var(--carousel-item-z-distance)))
               rotateY(-30deg);
    opacity: 0;
    color: #aaa;
  }
  80% {
    transform: translateX(var(--carousel-item-gap))
               scale(var(--carousel-item-scale))
               translateZ(var(--carousel-item-z-distance))
               rotateY(-25deg);
    opacity: 0.7;
    color: #aaa;
  }
  100% {
    transform: translateX(var(--carousel-item-gap))
               scale(var(--carousel-item-scale))
               translateZ(var(--carousel-item-z-distance))
               rotateY(-25deg);
    opacity: 0.7;
    color: #aaa;
  }
}

/* Efeito de transição para a direção 'prev' */
.tab-buttons.direction-prev.transitioning .tab-button.position-left.active {
  transform: translateX(0) scale(1) translateZ(0) rotateY(0);
  opacity: 1;
  z-index: 3;
}

.tab-buttons.direction-prev.transitioning .tab-button.position-center:not(.active) {
  transform: translateX(var(--carousel-item-gap))
             scale(var(--carousel-item-scale))
             translateZ(var(--carousel-item-z-distance))
             rotateY(-25deg);
  opacity: 0.7;
}

.tab-buttons.direction-prev.transitioning .tab-button.position-right {
  animation: slideOutToRight var(--carousel-animation-duration) cubic-bezier(0.25, 0.1, 0.25, 1) forwards;
}

@keyframes slideOutToRight {
  0% {
    transform: translateX(var(--carousel-item-gap))
               scale(var(--carousel-item-scale))
               translateZ(var(--carousel-item-z-distance))
               rotateY(-25deg);
    opacity: 0.7;
    color: #aaa;
  }
  100% {
    transform: translateX(calc(1.2 * var(--carousel-item-gap)))
               scale(0.8)
               translateZ(calc(1.2 * var(--carousel-item-z-distance)))
               rotateY(-30deg);
    opacity: 0;
    color: #aaa;
  }
}

/* Efeito para novas opções entrando da esquerda */
.tab-buttons.direction-prev.transitioning .tab-button.entering-left {
  transform: translateX(calc(-1.2 * var(--carousel-item-gap)))
             scale(0.8)
             translateZ(calc(1.2 * var(--carousel-item-z-distance)))
             rotateY(30deg);
  opacity: 0;
  animation: slideInFromLeft var(--carousel-animation-duration) cubic-bezier(0.25, 0.1, 0.25, 1) forwards;
  position: absolute;
  color: #aaa; /* Manter a cor original durante a animação */
  transition: transform var(--carousel-transition-duration) var(--carousel-transition-timing),
              opacity var(--carousel-transition-duration) var(--carousel-transition-timing),
              color var(--carousel-transition-duration) var(--carousel-transition-timing);
}

@keyframes slideInFromLeft {
  0% {
    transform: translateX(calc(-1.2 * var(--carousel-item-gap)))
               scale(0.8)
               translateZ(calc(1.2 * var(--carousel-item-z-distance)))
               rotateY(30deg);
    opacity: 0;
    color: #aaa;
  }
  80% {
    transform: translateX(calc(-1 * var(--carousel-item-gap)))
               scale(var(--carousel-item-scale))
               translateZ(var(--carousel-item-z-distance))
               rotateY(25deg);
    opacity: 0.7;
    color: #aaa;
  }
  100% {
    transform: translateX(calc(-1 * var(--carousel-item-gap)))
               scale(var(--carousel-item-scale))
               translateZ(var(--carousel-item-z-distance))
               rotateY(25deg);
    opacity: 0.7;
    color: #aaa;
  }
}

/* Efeito de ripple nos botões */
.tab-button::after {
  content: '';
  position: absolute;
  top: 50%;
  left: 50%;
  width: 5px;
  height: 5px;
  background: rgba(255, 255, 255, 0.5);
  opacity: 0;
  border-radius: 100%;
  transform: scale(1, 1) translate(-50%, -50%);
  transform-origin: 50% 50%;
}

.tab-button:focus:not(:active)::after {
  animation: ripple 1s ease-out;
}

@keyframes ripple {
  0% {
    transform: scale(0, 0);
    opacity: 0.5;
  }
  20% {
    transform: scale(25, 25);
    opacity: 0.3;
  }
  100% {
    opacity: 0;
    transform: scale(40, 40);
  }
}
