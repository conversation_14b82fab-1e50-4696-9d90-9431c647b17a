import React from 'react';
import { components } from 'react-select';

// Componentes personalizados para os selects do painel de unidades
// Estes componentes aplicam estilos inline para garantir que os selects
// funcionem corretamente, independentemente dos estilos CSS globais

// Componente personalizado para o controle do select
export const CustomControl = (props) => {
  return (
    <components.Control
      {...props}
      className="custom-control"
      style={{
        ...props.style,
        position: 'relative',
        border: 'none',
        backgroundColor: 'transparent',
        boxShadow: 'none',
        minHeight: '22.5px',
        height: '22.5px',
        flexDirection: 'row-reverse',
        justifyContent: 'flex-end'
      }}
    />
  );
};

// Componente personalizado para o valor selecionado
export const CustomSingleValue = (props) => {
  return (
    <components.SingleValue
      {...props}
      className="custom-single-value"
      style={{
        ...props.style,
        color: 'white',
        fontSize: '10.505px',
        fontFamily: 'monospace',
        textAlign: 'right',
        marginLeft: 0,
        marginRight: '8px'
      }}
    />
  );
};

// Componente personalizado para o container de valor
export const CustomValueContainer = (props) => {
  return (
    <components.ValueContainer
      {...props}
      className="custom-value-container"
      style={{
        ...props.style,
        padding: '0 8px',
        justifyContent: 'flex-end',
        flexDirection: 'row-reverse'
      }}
    />
  );
};

// Componente personalizado para o indicador de dropdown
export const CustomDropdownIndicator = (props) => {
  return (
    <components.DropdownIndicator
      {...props}
      className="custom-dropdown-indicator"
      style={{
        ...props.style,
        padding: '0 4px',
        color: 'var(--primary-color)'
      }}
    >
      <span style={{
        transition: 'transform 0.3s ease',
        transform: props.selectProps.menuIsOpen ? 'rotate(180deg)' : 'rotate(0deg)'
      }}>
        ▼
      </span>
    </components.DropdownIndicator>
  );
};

// Componente personalizado para o menu dropdown
export const CustomMenu = (props) => {
  return (
    <components.Menu
      {...props}
      className="custom-menu"
      style={{
        ...props.style,
        backgroundColor: '#000000',
        border: '1px solid rgba(76, 175, 80, 0.5)',
        borderRadius: '0 0 4px 4px',
        marginTop: '0',
        paddingTop: '0',
        zIndex: 1002,
        position: 'absolute',
        left: 'auto',
        right: 0,
        top: '21px',
        width: '120px',
        minWidth: '120px',
        overflow: 'hidden'
      }}
    />
  );
};

// Componente personalizado para a lista de opções do menu
export const CustomMenuList = (props) => {
  return (
    <components.MenuList
      {...props}
      className="custom-menu-list"
      style={{
        ...props.style,
        padding: 0,
        margin: 0,
        textAlign: 'right',
        whiteSpace: 'nowrap',
        maxHeight: '200px',
        display: 'flex',
        flexDirection: 'column',
        alignItems: 'flex-end'
      }}
    />
  );
};

// Componente personalizado para as opções do menu
export const CustomOption = (props) => {
  return (
    <components.Option
      {...props}
      className="custom-option"
      style={{
        ...props.style,
        backgroundColor: props.isSelected ? 'rgba(76, 175, 80, 0.2)' :
                         props.isFocused ? 'rgba(76, 175, 80, 0.1)' : 'transparent',
        color: 'white',
        fontSize: '10.505px',
        fontFamily: 'monospace',
        textAlign: 'right',
        padding: '2px 8px',
        margin: '0',
        height: '22px',
        lineHeight: '22px',
        whiteSpace: 'nowrap',
        overflow: 'visible',
        textOverflow: 'clip',
        display: 'flex',
        justifyContent: 'flex-end',
        alignItems: 'center'
      }}
    />
  );
};

const SelectComponents = {
  CustomControl,
  CustomSingleValue,
  CustomValueContainer,
  CustomDropdownIndicator,
  CustomMenu,
  CustomMenuList,
  CustomOption
};

export default SelectComponents;
