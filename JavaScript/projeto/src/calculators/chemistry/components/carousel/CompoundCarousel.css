/* Estilos específicos do CompoundCarousel */

/* Wrapper específico para o compound carousel */
.compound-carousel-wrapper {
  display: flex;
  flex-direction: column;
  width: 100%;
  margin-bottom: 10px;
  gap: 5px;
  align-items: center;
  position: relative;
  box-sizing: border-box;
}

/* Navegação específica do compound carousel */
.compound-carousel-navigation {
  margin-bottom: 5px;
  width: 100%;
  display: flex;
  flex-direction: column;
  align-items: center;
  position: relative;
  z-index: 10;
}

/* Tabs específicas do compound carousel */
.compound-carousel-tabs {
  display: flex;
  align-items: center;
  justify-content: center;
  width: 100%;
  margin: 0;
  position: relative;
  background-color: rgba(0, 0, 0, 0.3);
  border-top: 1px solid var(--primary-color);
  border-bottom: 1px solid var(--primary-color);
  border-left: none;
  border-right: none;
  border-radius: 0;
  padding: 8px 0;
  perspective: 1200px;
  perspective-origin: center center;
  overflow: hidden;
}

/* Botões de navegação específicos do compound carousel */
.compound-carousel-tab-button {
  background: none;
  border: none;
  color: #aaa;
  font-size: 16px;
  cursor: pointer;
  padding: 0;
  width: 25px;
  height: 25px;
  display: flex;
  align-items: center;
  justify-content: center;
  position: absolute;
  top: 50%;
  transform: translateY(-50%);
  z-index: 10;
  transition: color 0.3s ease;
}

.compound-carousel-tab-button.prev {
  left: 15%;
}

.compound-carousel-tab-button.next {
  right: 15%;
}

.compound-carousel-tab-button:hover {
  color: var(--primary-color);
}

/* Conteúdo específico do compound carousel */
.compound-carousel-content {
  padding: 0;
  background-color: transparent;
  border: none;
  width: 100%;
  max-width: 750px; /* Limitar largura */
  display: flex;
  justify-content: center;
  position: relative;
  min-height: 250px;
  overflow: hidden;
  margin: 0 auto;
  box-sizing: border-box;
}

/* Estilos para os slides */
.compound-slide {
  width: 100%;
  display: flex;
  flex-direction: column;
  align-items: center;
  padding: 20px;
  min-height: 200px;
  box-sizing: border-box;
}

.compound-info {
  width: 100%;
  max-width: 100%;
}

.element-summary {
  margin-top: 10px;
}

.element-percentages {
  margin-top: 10px;
}

.percentage-table {
  width: 100%;
  border-collapse: collapse;
  margin-top: 10px;
}

.percentage-table th,
.percentage-table td {
  padding: 5px;
  text-align: center;
  border: 1px solid var(--border-color);
}

.percentage-table th {
  background-color: rgba(76, 175, 80, 0.1);
  color: var(--primary-color);
}

.compound-properties {
  width: 100%;
}

.no-data {
  color: #aaa;
  font-style: italic;
  text-align: center;
  margin-top: 20px;
}
