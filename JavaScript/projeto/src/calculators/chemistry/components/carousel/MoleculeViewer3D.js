import React, { useEffect, useRef, useState } from 'react';
import './MoleculeViewer3D.css';
import '../../utils/ultimateResizeObserverSuppressor.js';
// import { generatePrecise3DCoordinates } from '../../utils/molecularGeometry.js';

// Supressão ultra-robusta do ResizeObserver
const suppressResizeObserverErrors = (() => {
  let isSuppressionActive = false;

  return () => {
    if (isSuppressionActive) return;
    isSuppressionActive = true;

    // Função para verificar se é erro do ResizeObserver
    const isResizeObserverError = (message) => {
      const msg = String(message || '').toLowerCase();
      return msg.includes('resizeobserver') ||
             msg.includes('undelivered notifications') ||
             msg.includes('loop completed') ||
             msg.includes('loop limit exceeded') ||
             msg.includes('resize observer loop') ||
             msg.includes('handleerror');
    };

    // Suprimir console.error
    const originalError = console.error;
    console.error = (...args) => {
      if (isResizeObserverError(args[0])) {
        return;
      }
      originalError.apply(console, args);
    };

    // Suprimir console.warn
    const originalWarn = console.warn;
    console.warn = (...args) => {
      if (isResizeObserverError(args[0])) {
        return;
      }
      originalWarn.apply(console, args);
    };

    // Suprimir window.onerror
    const originalWindowError = window.onerror;
    window.onerror = (message, source, lineno, colno, error) => {
      if (isResizeObserverError(message)) {
        return true;
      }
      if (originalWindowError) {
        return originalWindowError(message, source, lineno, colno, error);
      }
      return false;
    };

    // Suprimir unhandledrejection
    const originalUnhandledRejection = window.onunhandledrejection;
    window.onunhandledrejection = (event) => {
      if (isResizeObserverError(event.reason?.message || event.reason)) {
        event.preventDefault();
        return;
      }
      if (originalUnhandledRejection) {
        return originalUnhandledRejection(event);
      }
    };

    // Suprimir addEventListener de error
    const originalAddEventListener = window.addEventListener;
    window.addEventListener = function(type, listener, options) {
      if (type === 'error') {
        const wrappedListener = function(event) {
          if (isResizeObserverError(event.message || event.error?.message)) {
            event.preventDefault();
            event.stopPropagation();
            event.stopImmediatePropagation();
            return false;
          }
          if (typeof listener === 'function') {
            return listener.call(this, event);
          }
        };
        return originalAddEventListener.call(this, type, wrappedListener, options);
      }
      return originalAddEventListener.call(this, type, listener, options);
    };

    // Interceptar ResizeObserver diretamente
    if (window.ResizeObserver) {
      const OriginalResizeObserver = window.ResizeObserver;
      window.ResizeObserver = class extends OriginalResizeObserver {
        constructor(callback) {
          const wrappedCallback = (entries, observer) => {
            try {
              callback(entries, observer);
            } catch (error) {
              if (!isResizeObserverError(error.message)) {
                throw error;
              }
              // Silenciar erros do ResizeObserver
            }
          };
          super(wrappedCallback);
        }
      };
    }
  };
})();

// Aplicar supressão imediatamente
suppressResizeObserverErrors();

const MoleculeViewer3D = ({ composto, elementosInfo }) => {
  const containerRef = useRef(null);
  const [viewer, setViewer] = useState(null);
  const [isLoading, setIsLoading] = useState(true);
  const [error, setError] = useState(null);

  // Função para gerar coordenadas 3D precisas baseadas na fórmula
  const generateMoleculeStructure = (formula, elements) => {
    if (!elements || elements.length === 0) {
      return null;
    }

    console.log('🧪 Gerando estrutura para:', formula, elements);

    // Tentar usar a nova biblioteca de geometria molecular (temporariamente desabilitada)
    /*
    try {
      const preciseStructure = generatePrecise3DCoordinates(formula, elements);
      if (preciseStructure) {
        console.log('✅ Estrutura precisa gerada:', preciseStructure);
        return preciseStructure;
      }
    } catch (error) {
      console.warn('⚠️ Erro na biblioteca de geometria molecular:', error);
    }
    */

    // Fallback para estruturas conhecidas simples (mantido para compatibilidade)
    const knownStructures = {
      'H2O': {
        atoms: [
          { element: 'O', x: 0, y: 0, z: 0 },
          { element: 'H', x: 0.5877, y: 0.7591, z: 0 },
          { element: 'H', x: 0.5877, y: -0.7591, z: 0 }
        ],
        bonds: [
          { from: 0, to: 1, order: 1 },
          { from: 0, to: 2, order: 1 }
        ],
        geometry: 'bent_tetrahedral',
        bondAngle: 104.5,
        bondLength: 0.96
      },
      'CO2': {
        atoms: [
          { element: 'C', x: 0, y: 0, z: 0 },
          { element: 'O', x: -1.244, y: 0, z: 0 },
          { element: 'O', x: 1.244, y: 0, z: 0 }
        ],
        bonds: [
          { from: 0, to: 1, order: 2 },
          { from: 0, to: 2, order: 2 }
        ],
        geometry: 'linear',
        bondAngle: 180,
        bondLength: 1.244
      },
      'NH3': {
        atoms: [
          { element: 'N', x: 0, y: 0, z: 0.3 },
          { element: 'H', x: 1.01, y: 0, z: -0.7 },
          { element: 'H', x: -0.505, y: 0.875, z: -0.7 },
          { element: 'H', x: -0.505, y: -0.875, z: -0.7 }
        ],
        bonds: [
          { from: 0, to: 1, order: 1 },
          { from: 0, to: 2, order: 1 },
          { from: 0, to: 3, order: 1 }
        ],
        geometry: 'trigonal_pyramidal',
        bondAngle: 107,
        bondLength: 1.01
      },
      'CH4': {
        atoms: [
          { element: 'C', x: 0, y: 0, z: 0 },
          { element: 'H', x: 1.09, y: 0, z: 0 },
          { element: 'H', x: -0.36, y: 1.03, z: 0 },
          { element: 'H', x: -0.36, y: -0.51, z: 0.89 },
          { element: 'H', x: -0.36, y: -0.51, z: -0.89 }
        ],
        bonds: [
          { from: 0, to: 1, order: 1 },
          { from: 0, to: 2, order: 1 },
          { from: 0, to: 3, order: 1 },
          { from: 0, to: 4, order: 1 }
        ],
        geometry: 'tetrahedral',
        bondAngle: 109.47,
        bondLength: 1.09
      }
    };

    // Verificar se temos uma estrutura conhecida (fallback)
    if (knownStructures[formula]) {
      console.log('📚 Usando estrutura conhecida para:', formula);
      return knownStructures[formula];
    }

    // Fallback final: estrutura simples
    console.log('🔄 Gerando estrutura simples para:', formula);
    const atoms = [];
    const bonds = [];
    let atomIndex = 0;

    elements.forEach((element, elementIndex) => {
      for (let i = 0; i < element.quantidade; i++) {
        const angle = (atomIndex * 2 * Math.PI) / elements.reduce((sum, el) => sum + el.quantidade, 0);
        const radius = elementIndex === 0 ? 0 : 1.5;

        atoms.push({
          element: element.simbolo,
          x: radius * Math.cos(angle),
          y: radius * Math.sin(angle),
          z: 0
        });

        if (atomIndex > 0) {
          bonds.push({ from: 0, to: atomIndex, order: 1 });
        }

        atomIndex++;
      }
    });

    return {
      atoms,
      bonds,
      geometry: 'unknown',
      bondAngle: 109.47,
      bondLength: 1.0
    };
  };



  useEffect(() => {
    // Aplicar supressão adicional no contexto do componente
    suppressResizeObserverErrors();

    // Capturar referência do container para cleanup
    const currentContainer = containerRef.current;

    // Função para verificar se é erro do ResizeObserver
    const isResizeObserverError = (message) => {
      const msg = String(message || '').toLowerCase();
      return msg.includes('resizeobserver') ||
             msg.includes('undelivered notifications') ||
             msg.includes('loop completed') ||
             msg.includes('loop limit exceeded') ||
             msg.includes('resize observer loop') ||
             msg.includes('handleerror');
    };

    // Backup dos handlers originais
    const originalError = console.error;
    const originalWarn = console.warn;
    const originalWindowError = window.onerror;

    // Supressão mais robusta
    console.error = (...args) => {
      if (isResizeObserverError(args[0])) {
        return;
      }
      originalError.apply(console, args);
    };

    console.warn = (...args) => {
      if (isResizeObserverError(args[0])) {
        return;
      }
      originalWarn.apply(console, args);
    };

    window.onerror = (message, source, lineno, colno, error) => {
      if (isResizeObserverError(message)) {
        return true;
      }
      if (originalWindowError) {
        return originalWindowError(message, source, lineno, colno, error);
      }
      return false;
    };

    // Função para converter estrutura para formato XYZ (movida para dentro do useEffect)
    const structureToXYZ = (structure) => {
      if (!structure || !structure.atoms) return '';

      let xyz = `${structure.atoms.length}\n`;
      xyz += `Generated structure for ${composto}\n`;

      structure.atoms.forEach(atom => {
        xyz += `${atom.element} ${atom.x.toFixed(6)} ${atom.y.toFixed(6)} ${atom.z.toFixed(6)}\n`;
      });

      return xyz;
    };

    const initViewer = async () => {
      try {
        setIsLoading(true);
        setError(null);

        // Importar 3Dmol dinamicamente com tratamento de erro
        const $3Dmol = await import('3dmol').catch(err => {
          console.warn('Erro ao carregar 3DMol:', err);
          throw new Error('Failed to load 3DMol library');
        });

        if (containerRef.current) {
          // Limpar container de forma segura sem quebrar o DOM do React
          const container = containerRef.current;

          // Limpar apenas o conteúdo interno, mantendo o elemento
          while (container.firstChild) {
            container.removeChild(container.firstChild);
          }

          // Aguardar um pouco para garantir limpeza
          await new Promise(resolve => setTimeout(resolve, 100));

          // Criar novo viewer com configuração otimizada
          const config = {
            backgroundColor: 'black',
            antialias: true,
            alpha: true,
            preserveDrawingBuffer: true,
            premultipliedAlpha: false
          };

          // Envolver criação do viewer em try-catch para capturar erros do ResizeObserver
          let newViewer;
          try {
            console.log('🔬 Criando viewer 3DMol...');
            newViewer = $3Dmol.createViewer(containerRef.current, config);
            console.log('✅ Viewer 3DMol criado com sucesso');
          } catch (viewerError) {
            console.warn('⚠️ Erro ao criar viewer:', viewerError.message);
            if (isResizeObserverError(viewerError.message)) {
              // Tentar novamente ignorando o erro do ResizeObserver
              console.log('🔄 Tentando novamente após erro do ResizeObserver...');
              await new Promise(resolve => setTimeout(resolve, 100));
              newViewer = $3Dmol.createViewer(containerRef.current, config);
              console.log('✅ Viewer criado na segunda tentativa');
            } else {
              throw viewerError;
            }
          }

          // Verificar se o viewer foi criado corretamente
          if (!newViewer) {
            throw new Error('Failed to create 3DMol viewer');
          }

          // Gerar estrutura da molécula
          console.log('🧪 Gerando estrutura para:', composto, elementosInfo);
          const structure = generateMoleculeStructure(composto, elementosInfo);
          console.log('📐 Estrutura gerada:', structure);

          if (structure && structure.atoms && structure.atoms.length > 0) {
            // Converter para formato XYZ
            const xyzData = structureToXYZ(structure);
            console.log('📄 Dados XYZ gerados:', xyzData);

            if (!xyzData || xyzData.trim().length === 0) {
              throw new Error('Failed to generate XYZ data');
            }

            // Adicionar molécula ao viewer com tratamento robusto de erro
            console.log('📥 Adicionando modelo ao viewer...');
            await new Promise((resolve, reject) => {
              try {
                const model = newViewer.addModel(xyzData, 'xyz');
                console.log('✅ Modelo adicionado:', model);
                setTimeout(resolve, 100);
              } catch (modelError) {
                console.warn('⚠️ Erro ao adicionar modelo:', modelError);
                if (isResizeObserverError(modelError.message)) {
                  console.log('🔄 Ignorando erro do ResizeObserver...');
                  setTimeout(resolve, 100);
                } else {
                  reject(modelError);
                }
              }
            });

            // Configurar estilo com suporte a ligações múltiplas
            console.log('🎨 Configurando estilo...');
            try {
              // Estilo base para átomos
              newViewer.setStyle({}, {
                stick: { radius: 0.1 },
                sphere: { scale: 0.3 }
              });

              // Adicionar ligações múltiplas se existirem
              if (structure.bonds) {
                structure.bonds.forEach((bond, index) => {
                  if (bond.order && bond.order > 1) {
                    // Para ligações duplas e triplas, adicionar cilindros extras
                    const atom1 = structure.atoms[bond.from];
                    const atom2 = structure.atoms[bond.to];

                    if (atom1 && atom2) {
                      // Calcular vetor da ligação
                      const dx = atom2.x - atom1.x;
                      const dy = atom2.y - atom1.y;
                      const dz = atom2.z - atom1.z;
                      const length = Math.sqrt(dx*dx + dy*dy + dz*dz);

                      // Vetor perpendicular para deslocamento
                      const perpX = -dy / length * 0.1;
                      const perpY = dx / length * 0.1;
                      const perpZ = 0;

                      for (let i = 1; i < bond.order; i++) {
                        const offset = i * 0.15;
                        try {
                          newViewer.addCylinder({
                            start: {
                              x: atom1.x + perpX * offset,
                              y: atom1.y + perpY * offset,
                              z: atom1.z + perpZ * offset
                            },
                            end: {
                              x: atom2.x + perpX * offset,
                              y: atom2.y + perpY * offset,
                              z: atom2.z + perpZ * offset
                            },
                            radius: 0.05,
                            color: 'white'
                          });
                        } catch (cylinderError) {
                          console.warn(`⚠️ Erro ao adicionar cilindro para ligação ${index}:`, cylinderError);
                        }
                      }
                    }
                  }
                });
              }

              console.log('✅ Estilo configurado com ligações múltiplas');
            } catch (styleError) {
              console.warn('⚠️ Erro ao configurar estilo:', styleError);
              if (!isResizeObserverError(styleError.message)) {
                console.warn('Erro não relacionado ao ResizeObserver:', styleError);
              }
            }

            // Adicionar labels aos átomos com tratamento robusto e centralização
            console.log('🏷️ Adicionando labels aos átomos...');
            await new Promise(resolve => {
              try {
                const atoms = newViewer.getModel().selectedAtoms({});
                console.log('📍 Átomos encontrados:', atoms.length);

                atoms.forEach((atom, index) => {
                  try {
                    // Configuração original dos labels sem bordas
                    newViewer.addLabel(atom.elem, {
                      position: {
                        x: atom.x,
                        y: atom.y,
                        z: atom.z
                      },
                      backgroundColor: 'rgba(0,0,0,0.8)',
                      fontColor: 'white',
                      fontSize: 12,
                      showBackground: true
                    });
                    console.log(`✅ Label adicionado para ${atom.elem} (${index + 1}/${atoms.length})`);
                  } catch (labelError) {
                    console.warn(`⚠️ Erro ao adicionar label para átomo ${index}:`, labelError);
                  }
                });
                console.log('✅ Todos os labels processados');
              } catch (atomsError) {
                console.warn('⚠️ Erro ao obter átomos:', atomsError);
              }
              setTimeout(resolve, 150);
            });

            // Centralizar e renderizar com tratamento de erro
            console.log('🖼️ Renderizando viewer...');
            await new Promise(resolve => {
              try {
                newViewer.zoomTo();
                newViewer.render();
                console.log('✅ Viewer renderizado com sucesso');
              } catch (renderError) {
                console.warn('⚠️ Erro ao renderizar:', renderError);
                if (!isResizeObserverError(renderError.message)) {
                  console.warn('Erro não relacionado ao ResizeObserver:', renderError);
                }
              }
              setTimeout(resolve, 150);
            });

            console.log('🎉 Viewer 3D inicializado com sucesso!');
            setViewer(newViewer);
          } else {
            setError('Unable to generate 3D structure for this compound');
          }
        }

        setIsLoading(false);
      } catch (err) {
        if (!isResizeObserverError(err.message)) {
          console.error('Error initializing 3D viewer:', err);
          setError('Failed to load 3D viewer');
        }
        setIsLoading(false);
      }
    };

    if (composto && elementosInfo) {
      initViewer();
    }

    // Cleanup
    return () => {
      // Restaurar handlers originais
      console.error = originalError;
      console.warn = originalWarn;
      window.onerror = originalWindowError;

      // Limpar viewer de forma segura
      if (viewer) {
        try {
          // Limpar modelos e labels
          viewer.removeAllModels();
          viewer.removeAllLabels();
          viewer.clear();
        } catch (clearError) {
          // Ignorar erros de limpeza
        }
      }

      // Limpar container de forma segura usando a referência capturada
      if (currentContainer) {
        try {
          while (currentContainer.firstChild) {
            currentContainer.removeChild(currentContainer.firstChild);
          }
        } catch (domError) {
          // Ignorar erros de DOM
        }
      }
    };
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, [composto, elementosInfo]);

  // Funções de controle
  const resetView = () => {
    if (viewer) {
      viewer.zoomTo();
      viewer.render();
    }
  };

  const toggleStyle = () => {
    if (viewer) {
      // Alternar entre diferentes estilos de visualização
      const currentStyle = viewer.getModel().getStyle();
      if (currentStyle.stick) {
        viewer.setStyle({}, { sphere: { scale: 0.5 } });
      } else {
        viewer.setStyle({}, {
          stick: { radius: 0.1 },
          sphere: { scale: 0.3 }
        });
      }
      viewer.render();
    }
  };

  if (!composto || !elementosInfo) {
    return (
      <div className="molecule-viewer-container">
        <div className="no-data">
          <p>No compound data available for 3D visualization</p>
        </div>
      </div>
    );
  }

  return (
    <div className="molecule-viewer-container">
      <div className="viewer-header">
        <h4>3D Molecular Structure</h4>
        <div className="viewer-controls">
          <button onClick={resetView} className="control-button" title="Reset View">
            🔄
          </button>
          <button onClick={toggleStyle} className="control-button" title="Toggle Style">
            🎨
          </button>
        </div>
      </div>
      
      {isLoading && (
        <div className="loading-indicator">
          <div className="spinner"></div>
          <p>Loading 3D structure...</p>
        </div>
      )}
      
      {error && (
        <div className="error-message">
          <p>{error}</p>
          <p className="error-details">
            Showing simplified structure for: <strong>{composto}</strong>
          </p>
        </div>
      )}
      
      <div
        ref={containerRef}
        className={`molecule-viewer molecule-viewer-stable ${isLoading ? 'loading' : ''}`}
        style={{
          width: '100%',
          height: '300px',
          display: isLoading || error ? 'none' : 'block'
        }}
      />
      
      <div className="viewer-info">
        <p>Formula: <strong>{composto}</strong></p>
        <p className="interaction-hint">
          💡 Click and drag to rotate • Scroll to zoom • Right-click and drag to pan
        </p>
      </div>
    </div>
  );
};

export default MoleculeViewer3D;
