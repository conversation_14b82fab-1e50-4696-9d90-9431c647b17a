import React, { useState, useEffect, useRef } from 'react';
import './CarouselMenu.css';

const SimpleCarousel = ({ slides }) => {
  const [activeSlide, setActiveSlide] = useState(slides[0].id);
  const [displayedSlides, setDisplayedSlides] = useState([]);
  const [isTransitioning, setIsTransitioning] = useState(false);
  const [direction, setDirection] = useState(null); // 'next' ou 'prev'
  const [newSlideEntering, setNewSlideEntering] = useState(null); // Slide que está entrando
  const tabsContainerRef = useRef(null);

  // Inicializar os slides exibidos
  useEffect(() => {
    initializeDisplayedSlides();
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, []);

  // Efeito para logar o estado das variáveis de estado
  useEffect(() => {
    console.log('Estado atual:', {
      isTransitioning,
      direction,
      activeSlide,
      newSlideEntering: newSlideEntering ? `${newSlideEntering.id} (${newSlideEntering.entering})` : 'null'
    });
  }, [isTransitioning, direction, activeSlide, newSlideEntering]);

  // Adicionar suporte para navegação com teclado
  useEffect(() => {
    const handleKeyDown = (e) => {
      if (e.key === 'ArrowLeft') {
        handlePrevSlide();
      } else if (e.key === 'ArrowRight') {
        handleNextSlide();
      }
    };

    window.addEventListener('keydown', handleKeyDown);

    return () => {
      window.removeEventListener('keydown', handleKeyDown);
    };
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, []);

  // Adicionar suporte para swipe em dispositivos móveis
  useEffect(() => {
    const carousel = document.querySelector('.carousel-wrapper');
    let touchStartX = 0;
    let touchEndX = 0;

    const handleTouchStart = (e) => {
      touchStartX = e.changedTouches[0].screenX;
    };

    const handleTouchEnd = (e) => {
      touchEndX = e.changedTouches[0].screenX;
      handleSwipe();
    };

    const handleSwipe = () => {
      // Swipe mínimo de 50px para evitar toques acidentais
      if (touchEndX < touchStartX - 50) {
        // Swipe para a esquerda
        handleNextSlide();
      } else if (touchEndX > touchStartX + 50) {
        // Swipe para a direita
        handlePrevSlide();
      }
    };

    if (carousel) {
      carousel.addEventListener('touchstart', handleTouchStart, { passive: true });
      carousel.addEventListener('touchend', handleTouchEnd, { passive: true });

      return () => {
        carousel.removeEventListener('touchstart', handleTouchStart);
        carousel.removeEventListener('touchend', handleTouchEnd);
      };
    }
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, []);

  // Inicializar os slides exibidos
  const initializeDisplayedSlides = () => {
    const activeIndex = slides.findIndex(slide => slide.id === activeSlide);
    let initialSlides = getVisibleSlidesForIndex(activeIndex);
    setDisplayedSlides(initialSlides);
  };

  // Obter os slides visíveis para um determinado índice
  const getVisibleSlidesForIndex = (activeIndex) => {
    // Caso normal: mostrar o slide ativo e um de cada lado
    if (activeIndex > 0 && activeIndex < slides.length - 1) {
      return [
        { ...slides[activeIndex - 1], position: 0 },
        { ...slides[activeIndex], position: 1 },
        { ...slides[activeIndex + 1], position: 2 }
      ];
    }
    // Caso especial: primeiro slide ativo - mostrar o último slide à esquerda
    else if (activeIndex === 0) {
      return [
        { ...slides[slides.length - 1], position: 0 },
        { ...slides[0], position: 1 },
        { ...slides[1], position: 2 }
      ];
    }
    // Caso especial: último slide ativo - mostrar o primeiro slide à direita
    else if (activeIndex === slides.length - 1) {
      return [
        { ...slides[slides.length - 2], position: 0 },
        { ...slides[slides.length - 1], position: 1 },
        { ...slides[0], position: 2 }
      ];
    }
    return [];
  };

  // Determinar a direção correta com base na posição dos slides
  const determineDirection = (currentIndex, targetIndex) => {
    // Caso especial: navegando do último para o primeiro slide
    if (currentIndex === slides.length - 1 && targetIndex === 0) {
      return 'next'; // Continuar para a direita (efeito de loop)
    }

    // Caso especial: navegando do primeiro para o último slide
    if (currentIndex === 0 && targetIndex === slides.length - 1) {
      return 'prev'; // Continuar para a esquerda (efeito de loop)
    }

    // Casos normais
    // Se estamos indo para a direita (índice maior)
    if (targetIndex > currentIndex) {
      return 'next';
    }
    // Se estamos indo para a esquerda (índice menor)
    else if (targetIndex < currentIndex) {
      return 'prev';
    }

    // Se estamos no mesmo slide (não deveria acontecer)
    return null;
  };

  // Obter o próximo slide que entrará na visualização
  const getNextEnteringSlide = (currentActiveIndex, navDirection) => {
    if (navDirection === 'next') {
      // Se estamos indo para a direita, o novo slide entrará pela direita
      const nextIndex = (currentActiveIndex + 2) % slides.length;
      return { ...slides[nextIndex], entering: 'right' };
    } else if (navDirection === 'prev') {
      // Se estamos indo para a esquerda, o novo slide entrará pela esquerda
      const prevIndex = (currentActiveIndex - 2 + slides.length) % slides.length;
      return { ...slides[prevIndex], entering: 'left' };
    }
    return null;
  };

  // Função para lidar com a mudança de slide
  const handleSlideChange = (slideId, navDirection) => {
    // Evitar múltiplas transições simultâneas ou mudança para o slide atual
    if (slideId === activeSlide || isTransitioning) {
      return;
    }

    // Determinar a direção com base na distância entre os slides
    const currentIndex = slides.findIndex(slide => slide.id === activeSlide);
    const targetIndex = slides.findIndex(slide => slide.id === slideId);

    // Garantir que a direção seja correta
    const correctDirection = determineDirection(currentIndex, targetIndex);

    // Se a direção fornecida não corresponder à direção real, usar a direção correta
    if (correctDirection && navDirection !== correctDirection) {
      navDirection = correctDirection;
    }

    // Definir a direção da navegação (necessário para as classes CSS)
    setDirection(navDirection);

    // Iniciar a transição
    setIsTransitioning(true);

    // Obter o slide que entrará na visualização
    const enteringSlide = getNextEnteringSlide(currentIndex, navDirection);
    setNewSlideEntering(enteringSlide);

    // Atualizar o slide ativo
    setActiveSlide(slideId);

    // Atualizar os slides exibidos após a transição
    const transitionDuration = 600; // Deve corresponder à variável --carousel-animation-duration no CSS (0.6s = 600ms)
    setTimeout(() => {
      const newActiveIndex = slides.findIndex(slide => slide.id === slideId);
      const newDisplayedSlides = getVisibleSlidesForIndex(newActiveIndex);
      setDisplayedSlides(newDisplayedSlides);
      setIsTransitioning(false);
      setDirection(null);
      setNewSlideEntering(null);
    }, transitionDuration);
  };

  // Obter o próximo slide (com suporte para loop infinito)
  const getNextSlide = () => {
    const currentIndex = slides.findIndex(slide => slide.id === activeSlide);
    // Usar operador módulo para garantir que voltamos ao início quando chegamos ao fim
    const nextIndex = (currentIndex + 1) % slides.length;
    return slides[nextIndex].id;
  };

  // Obter o slide anterior (com suporte para loop infinito)
  const getPrevSlide = () => {
    const currentIndex = slides.findIndex(slide => slide.id === activeSlide);
    // Adicionar slides.length antes de aplicar o módulo para garantir que o resultado seja positivo
    const prevIndex = (currentIndex - 1 + slides.length) % slides.length;
    return slides[prevIndex].id;
  };

  const handleNextSlide = () => {
    handleSlideChange(getNextSlide(), 'next');
  };

  const handlePrevSlide = () => {
    handleSlideChange(getPrevSlide(), 'prev');
  };

  return (
    <div className="carousel-wrapper">
      {/* Menu de navegação */}
      <div className="carousel-navigation">
        <div className="carousel-tabs">
          <button
            className="carousel-tab-button prev"
            onClick={handlePrevSlide}
            title="Previous"
          >
            &#9664;
          </button>

          <div
            ref={tabsContainerRef}
            className={`carousel-tabs-container ${isTransitioning ? 'transitioning' : ''} ${direction ? `direction-${direction}` : ''}`}
          >
            {/* Slides atualmente visíveis */}
            {displayedSlides.map((slide) => (
              <button
                key={`${slide.id}-${slide.position}`}
                className={`carousel-tab position-${slide.position} ${activeSlide === slide.id ? 'active' : ''}`}
                onClick={() => handleSlideChange(slide.id, slide.position < 1 ? 'prev' : 'next')}
              >
                {slide.title}
              </button>
            ))}

            {/* Slide que está entrando durante a transição */}
            {isTransitioning && newSlideEntering && (
              <button
                key={`${newSlideEntering.id}-entering`}
                className={`carousel-tab entering-${newSlideEntering.entering}`}
                onClick={() => {}}
              >
                {newSlideEntering.title}
              </button>
            )}
          </div>

          <button
            className="carousel-tab-button next"
            onClick={handleNextSlide}
            title="Next"
          >
            &#9654;
          </button>
        </div>
      </div>

      {/* Conteúdo dos slides */}
      <div className="carousel-content">
        <div className={`slide-content-wrapper ${isTransitioning ? 'fade' : 'visible'}`}>
          {slides.find(slide => slide.id === activeSlide)?.component}
        </div>
      </div>
    </div>
  );
};

export default SimpleCarousel;
