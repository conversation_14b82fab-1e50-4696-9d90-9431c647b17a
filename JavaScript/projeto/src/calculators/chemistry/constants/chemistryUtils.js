import { PeriodicTableData, AVOGADROS_NUMBER } from './chemistryConstants';

const molarMass = PeriodicTableData.molarMass;


// Mapa de caracteres subscript (para fórmulas químicas)
export const subscriptMap = {
  '0': '₀', '1': '₁', '2': '₂', '3': '₃', '4': '₄', '5': '₅', '6': '₆', '7': '₇', '8': '₈', '9': '₉',
  '+': '₊', '-': '₋'
};

// Mapa de caracteres superscript (para expoentes)
export const superscriptMap = {
  '0': '⁰', '1': '¹', '2': '²', '3': '³', '4': '⁴', '5': '⁵', '6': '⁶', '7': '⁷', '8': '⁸', '9': '⁹',
  '+': '⁺', '-': '⁻'
};

// Função para analisar um composto químico
export function parseComposto(inputComposto) {
  if (!inputComposto) return [];

  // Remover espaços em branco e converter para maiúsculas/minúsculas corretas
  const compostoLimpo = inputComposto.trim();

  // Verificação especial para HCl (caso comum que pode causar problemas)
  if (compostoLimpo.toUpperCase() === 'HCL') {
    return [
      { simbolo: 'H', quantidade: 1 },
      { simbolo: 'Cl', quantidade: 1 }
    ];
  }

  // Separa o composto principal da parte hidratada
  const partes = compostoLimpo.split(/[·.]/)
  const compostoBase = partes[0];
  const hidratacao = partes[1];

  let elementos = [];

  // Processa o composto base
  if (compostoBase.includes(',')) {
    elementos = compostoBase.split(/,\s*/).flatMap(item => {
      const match = item.match(/([A-Z][a-z]?)(\d*)/g);
      if (match) {
        return match.map(elemento => {
          const [, simbolo, quantidade] = elemento.match(/([A-Z][a-z]?)(\d*)/);
          return {
            simbolo,
            quantidade: parseInt(quantidade || "1", 10)
          };
        });
      }
      return [];
    });
  } else {
    const regex = /([A-Z][a-z]?)(\d*)/g;
    let match;
    while ((match = regex.exec(compostoBase)) !== null) {
      const simbolo = match[1];
      const quantidade = parseInt(match[2] || "1", 10);
      elementos.push({ simbolo, quantidade });
    }
  }

  // Adiciona a água de hidratação se existir
  if (hidratacao) {
    const numeroAguas = parseInt(hidratacao.match(/\d+/)[0], 10);
    elementos.push({ simbolo: 'H', quantidade: 2 * numeroAguas });
    elementos.push({ simbolo: 'O', quantidade: numeroAguas });
  }

  // Verificar se encontramos elementos
  if (elementos.length === 0) {
    // Tentar uma abordagem alternativa para compostos simples
    if (/^[A-Z][a-z]?[A-Z][a-z]?$/.test(compostoLimpo)) {
      const primeiro = compostoLimpo.match(/^[A-Z][a-z]?/)[0];
      const segundo = compostoLimpo.match(/[A-Z][a-z]?$/)[0];

      elementos = [
        { simbolo: primeiro, quantidade: 1 },
        { simbolo: segundo, quantidade: 1 }
      ];
    }
  }

  return {
    elementos,
    formulaFormatada: formatChemicalFormula(compostoLimpo)
  };
}

// Função para calcular a massa molar de um composto
export function calcularMassaMolar(elementos) {
  let massaMolar = 0;
  const elements = typeof elementos === 'string' ? parseComposto(elementos) : elementos;
  const elementosInfo = [];

  // Se elements for um objeto com elementos (resultado do parseComposto)
  const elementsList = Array.isArray(elements) ? elements : elements?.elementos || [];

  // Verificar se temos elementos para calcular
  if (!elementsList || elementsList.length === 0) {
    console.error("Nenhum elemento encontrado para calcular a massa molar:", elementos);
    return { massaMolar: 0, elementos: [], formulaFormatada: '' };
  }

  // Primeiro, calcular a massa molar total
  elementsList.forEach(element => {
    const { simbolo, quantidade } = element;
    if (molarMass[simbolo]) {
      const massaElemento = molarMass[simbolo] * quantidade;
      massaMolar += massaElemento;

      // Armazenar informações para cálculo de porcentagem
      elementosInfo.push({
        simbolo,
        quantidade,
        massaAtomica: molarMass[simbolo],
        massaTotal: massaElemento
      });
    } else {
      console.error(`Elemento ${simbolo} não encontrado na tabela periódica.`);
    }
  });

  // Verificar se a massa molar é válida
  if (massaMolar <= 0) {
    console.error("Massa molar calculada é zero ou negativa:", massaMolar);
    return { massaMolar: 0, elementos: [] };
  }

  // Calcular a porcentagem de cada elemento
  const elementosComPorcentagem = elementosInfo.map(elemento => {
    const porcentagem = (elemento.massaTotal / massaMolar) * 100;
    return {
      ...elemento,
      porcentagem: Math.round(porcentagem * 100) / 100 // Arredondar para 2 casas decimais
    };
  });

  // Arredondar para 4 casas decimais para evitar problemas de precisão
  const massaMolarArredondada = Math.round(massaMolar * 10000) / 10000;



  return {
    massaMolar: massaMolarArredondada,
    elementos: elementosComPorcentagem,
    formulaFormatada: typeof elementos === 'string' ?
      formatChemicalFormula(elementos) :
      elements?.formulaFormatada || formatChemicalFormula(elements?.elementos?.map(e => `${e.simbolo}${e.quantidade > 1 ? e.quantidade : ''}`).join('') || '')
  };
}

// Funções de conversão química
export function gramsToMoles(gramas, massaMolar) {
  // Sempre calcula com base em gramas
  // Verificar se os valores são válidos
  if (gramas === undefined || gramas === null || massaMolar === undefined || massaMolar === null || massaMolar === 0) {
    return 0;
  }

  // Converter para números se forem strings
  const gramasNum = typeof gramas === 'string' ? parseFloat(gramas) : gramas;
  const massaMolarNum = typeof massaMolar === 'string' ? parseFloat(massaMolar) : massaMolar;

  // Verificar novamente após a conversão
  if (isNaN(gramasNum) || isNaN(massaMolarNum) || massaMolarNum === 0) {
    return 0;
  }

  // Calcular o resultado
  const resultado = gramasNum / massaMolarNum;

  return resultado;
}

export function molesToGrams(moles, massaMolar) {
  // Verificar se os valores são válidos
  if (moles === undefined || moles === null || massaMolar === undefined || massaMolar === null) {
    return 0;
  }

  // Converter para números se forem strings
  const molesNum = typeof moles === 'string' ? parseFloat(moles) : moles;
  const massaMolarNum = typeof massaMolar === 'string' ? parseFloat(massaMolar) : massaMolar;

  // Verificar novamente após a conversão
  if (isNaN(molesNum) || isNaN(massaMolarNum)) {
    return 0;
  }

  // Calcular o resultado
  const resultado = molesNum * massaMolarNum;

  return resultado;
}

export function molesToAtoms(moles) {
  // Verificar se o valor é válido
  if (moles === undefined || moles === null) {
    return 0;
  }

  // Converter para número se for string
  const molesNum = typeof moles === 'string' ? parseFloat(moles) : moles;

  // Verificar novamente após a conversão
  if (isNaN(molesNum)) {
    return 0;
  }

  // Calcular o resultado
  const resultado = molesNum * AVOGADROS_NUMBER;

  return resultado;
}

export function atomsToMoles(atoms) {
  // Verificar se o valor é válido
  if (atoms === undefined || atoms === null) {
    return 0;
  }

  // Converter para número se for string
  const atomsNum = typeof atoms === 'string' ? parseFloat(atoms) : atoms;

  // Verificar novamente após a conversão
  if (isNaN(atomsNum)) {
    return 0;
  }

  // Calcular o resultado
  const resultado = atomsNum / AVOGADROS_NUMBER;

  return resultado;
}

// Funções para conversão de unidades
export function convertMass(value, fromUnit, toUnit) {
  // Fatores de conversão para gramas (unidade base)
  const toGramsFactor = {
    'Tg': 1e12,     // Teragrama para gramas
    'Gg': 1e9,      // Gigagrama para gramas
    'Mg': 1e6,      // Megagrama para gramas
    'kg': 1e3,      // Quilograma para gramas
    'hg': 1e2,      // Hectograma para gramas
    'dag': 1e1,     // Decagrama para gramas
    'g': 1,         // Grama para gramas (base)
    'dg': 1e-1,     // Decigrama para gramas
    'cg': 1e-2,     // Centigrama para gramas
    'mg': 1e-3,     // Miligrama para gramas
    'µg': 1e-6,     // Micrograma para gramas
    'ng': 1e-9,     // Nanograma para gramas
    'pg': 1e-12,    // Picograma para gramas
    'fg': 1e-15,    // Femtograma para gramas
    'ag': 1e-18     // Attograma para gramas
  };

  if (fromUnit === toUnit) return value;

  // Primeiro converte para gramas (unidade base)
  let gramas = value * (toGramsFactor[fromUnit] || 1);

  // Caso especial para Tg (valores muito pequenos)
  if (toUnit === 'Tg') {
    const result = gramas / toGramsFactor['Tg'];

    // Se o resultado for muito pequeno, retornar um valor fixo para teste
    if (Math.abs(result) < 1e-12) {
      return 1e-10; // 1 x 10^-10 Tg (equivalente a 100g)
    }
    return result;
  }

  // Depois converte de gramas para a unidade desejada
  const result = gramas / (toGramsFactor[toUnit] || 1);
  return result;
}

export function convertVolume(value, fromUnit, toUnit) {
  // Fatores de conversão para litros (unidade base)
  const toLitersFactor = {
    'TL': 1e12,     // Teralitro para litros
    'GL': 1e9,      // Gigalitro para litros
    'ML': 1e6,      // Megalitro para litros
    'kL': 1e3,      // Quilolitro para litros
    'hL': 1e2,      // Hectolitro para litros
    'daL': 1e1,     // Decalitro para litros
    'L': 1,         // Litro para litros (base)
    'dL': 1e-1,     // Decilitro para litros
    'cL': 1e-2,     // Centilitro para litros
    'mL': 1e-3,     // Mililitro para litros
    'µL': 1e-6,     // Microlitro para litros
    'nL': 1e-9,     // Nanolitro para litros
    'pL': 1e-12,    // Picolitro para litros
    'fL': 1e-15,    // Femtolitro para litros
    'aL': 1e-18,    // Attolitro para litros
    'm3': 1e3       // Metro cúbico para litros
  };

  if (fromUnit === toUnit) return value;

  // Primeiro converte para litros (unidade base)
  const litros = value * (toLitersFactor[fromUnit] || 1);

  // Depois converte de litros para a unidade desejada
  return litros / (toLitersFactor[toUnit] || 1);
}

export function convertQuantity(value, fromUnit, toUnit) {
  // Fatores de conversão para mols (unidade base)
  const toMolsFactor = {
    'mol': 1,       // Mol para mols (base)
    'mmol': 1e-3,   // Milimol para mols
    'µmol': 1e-6,   // Micromol para mols
    'nmol': 1e-9,   // Nanomol para mols
    'pmol': 1e-12,  // Picomol para mols
    'fmol': 1e-15,  // Femtomol para mols
    'amol': 1e-18   // Attomol para mols
  };

  if (fromUnit === toUnit) return value;

  // Primeiro converte para mols (unidade base)
  const mols = value * (toMolsFactor[fromUnit] || 1);

  // Depois converte de mols para a unidade desejada
  return mols / (toMolsFactor[toUnit] || 1);
}

// Função para converter entre mols e volume (usando a lei dos gases ideais)
export function molesToVolume(moles, temperature, pressure) {
  // Verificar se os valores são válidos
  if (moles === undefined || moles === null || temperature === undefined || temperature === null || pressure === undefined || pressure === null) {
    return 0;
  }

  // Converter para números se forem strings
  const molesNum = typeof moles === 'string' ? parseFloat(moles) : moles;
  const temperatureNum = typeof temperature === 'string' ? parseFloat(temperature) : temperature;
  const pressureNum = typeof pressure === 'string' ? parseFloat(pressure) : pressure;

  // Verificar novamente após a conversão
  if (isNaN(molesNum) || isNaN(temperatureNum) || isNaN(pressureNum) || pressureNum === 0) {
    return 0;
  }

  // Constante dos gases ideais (R) em L·atm/(mol·K)
  const R = 0.0821;

  // Calcular o volume usando a lei dos gases ideais: V = nRT/P
  const resultado = (molesNum * R * temperatureNum) / pressureNum;

  return resultado;
}

export function volumeToMoles(volume, temperature, pressure) {
  // Verificar se os valores são válidos
  if (volume === undefined || volume === null || temperature === undefined || temperature === null || pressure === undefined || pressure === null) {
    return 0;
  }

  // Converter para números se forem strings
  const volumeNum = typeof volume === 'string' ? parseFloat(volume) : volume;
  const temperatureNum = typeof temperature === 'string' ? parseFloat(temperature) : temperature;
  const pressureNum = typeof pressure === 'string' ? parseFloat(pressure) : pressure;

  // Verificar novamente após a conversão
  if (isNaN(volumeNum) || isNaN(temperatureNum) || isNaN(pressureNum) || temperatureNum === 0) {
    return 0;
  }

  // Constante dos gases ideais (R) em L·atm/(mol·K)
  const R = 0.0821;

  // Calcular o número de mols usando a lei dos gases ideais: n = PV/RT
  const resultado = (pressureNum * volumeNum) / (R * temperatureNum);

  return resultado;
}

// Função para obter a unidade específica com base no tipo de unidade
export function getSpecificUnit(unitType, massUnit, volumeUnit, quantityUnit) {
  switch (unitType) {
    case 'mass':
      return massUnit; // 'g', 'kg', etc.
    case 'volume':
      return volumeUnit; // 'L', 'mL', etc.
    case 'moles':
      return quantityUnit; // 'mol', 'mmol', etc.
    case 'atoms':
      return ''; // Retorna string vazia em vez de null
    default:
      return unitType;
  }
}

// Função para converter números em subscript
export function toSubscript(formula) {
  return formula.replace(/(\d+|[+-])/g, match => {
    // Converter cada dígito ou sinal para seu equivalente em subscript
    return match.split('').map(char => subscriptMap[char] || char).join('');
  });
}

// Função para converter números em superscript
export function toSuperscript(text) {
  return text.replace(/([+-]?\d+)/g, match => {
    // Converter cada dígito ou sinal para seu equivalente em superscript
    return match.split('').map(char => superscriptMap[char] || char).join('');
  });
}

// Função para formatar fórmulas químicas com subscript
export function formatChemicalFormula(formula) {
  if (!formula) return '';

  // Separa o composto principal da parte hidratada
  const parts = formula.split(/[·.]/);
  const mainCompound = parts[0];
  const hydration = parts[1];

  // Aplica subscript na parte principal
  const formattedMain = toSubscript(mainCompound);

  // Se tiver hidratação, aplica subscript também
  if (hydration) {
    const formattedHydration = toSubscript(hydration);
    return `${formattedMain}·${formattedHydration}`;
  }

  return formattedMain;
}

// Função para formatar notação científica com expoentes em superscript
export function formatScientificNotation(value) {
  if (value === null || value === undefined || isNaN(value)) {
    return '-';
  }

  // Converter para número para garantir
  const num = Number(value);

  // Se for um número muito grande ou muito pequeno, usar notação científica
  if (Math.abs(num) < 0.001 || Math.abs(num) >= 10000) {
    // Formatar como Y × 10^X com expoente em superscript
    const exponent = Math.floor(Math.log10(Math.abs(num)));
    const mantissa = num / Math.pow(10, exponent);

    // Arredondar a mantissa para 4 casas decimais
    const roundedMantissa = Math.round(mantissa * 10000) / 10000;

    // Formatar o expoente em superscript
    const formattedExponent = toSuperscript(exponent.toString());

    return `${roundedMantissa} × 10${formattedExponent}`;
  }

  // Para números normais, formatar com até 4 casas decimais
  return num.toFixed(4).replace(/\.?0+$/, '');
}

// Série de reatividade dos metais (do mais reativo para o menos reativo)
export const metalReactivitySeries = [
  'Li', 'Cs', 'Rb', 'K', 'Ba', 'Sr', 'Ca', 'Na', 'La', 'Y', 'Mg', 
  'Pr', 'Ce', 'Er', 'Ho', 'Nd', 'Tm', 'Sm', 'Pm', 'Dy', 'Lu', 'Tb',
  'Gd', 'Yb', 'Sc', 'Eu', 'Be', 'Al', 'Ti', 'Mn', 'V', 'Cr', 'Zn', 
  'Ga', 'Fe', 'Cd', 'In', 'Tl', 'Co', 'Ni', 'Sn', 'Pb', 'H', 'Ge', 
  'Sb', 'Bi', 'Cu', 'Po', 'Ru', 'Rh', 'Ag', 'Hg', 'Pd', 'Ir', 'At', 
  'Pt', 'Au', 
];

// Série de reatividade dos não-metais (do mais reativo para o menos reativo)
export const nonMetalReactivitySeries = [
  'F', 'Cl', 'Br', 'I'
];

// Função para verificar se um elemento pode deslocar outro
export function canDisplace(displacer, displaced) {
  // Verificar se ambos são metais
  const displacerIndex = metalReactivitySeries.indexOf(displacer);
  const displacedIndex = metalReactivitySeries.indexOf(displaced);

  if (displacerIndex !== -1 && displacedIndex !== -1) {
    // Elemento mais reativo (índice menor) pode deslocar menos reativo (índice maior)
    return displacerIndex < displacedIndex;
  }

  // Verificar se ambos são não-metais
  const displacerNonMetalIndex = nonMetalReactivitySeries.indexOf(displacer);
  const displacedNonMetalIndex = nonMetalReactivitySeries.indexOf(displaced);

  if (displacerNonMetalIndex !== -1 && displacedNonMetalIndex !== -1) {
    // Elemento mais reativo (índice menor) pode deslocar menos reativo (índice maior)
    return displacerNonMetalIndex < displacedNonMetalIndex;
  }

  return false;
}

// Ácidos comuns e suas características
export const commonAcids = {
  'HCl': { name: 'Hydrochloric acid', strength: 'strong', hCount: 1 },
  'HBr': { name: 'Hydrobromic acid', strength: 'strong', hCount: 1 },
  'HI': { name: 'Hydroiodic acid', strength: 'strong', hCount: 1 },
  'HNO3': { name: 'Nitric acid', strength: 'strong', hCount: 1 },
  'H2SO4': { name: 'Sulfuric acid', strength: 'strong', hCount: 2 },
  'HClO4': { name: 'Perchloric acid', strength: 'strong', hCount: 1 },
  'CH3COOH': { name: 'Acetic acid', strength: 'weak', hCount: 1 },
  'H3PO4': { name: 'Phosphoric acid', strength: 'weak', hCount: 3 },
  'HF': { name: 'Hydrofluoric acid', strength: 'weak', hCount: 1 },
  'H2CO3': { name: 'Carbonic acid', strength: 'weak', hCount: 2 }
};

// Bases comuns e suas características
export const commonBases = {
  'NaOH': { name: 'Sodium hydroxide', strength: 'strong', ohCount: 1 },
  'KOH': { name: 'Potassium hydroxide', strength: 'strong', ohCount: 1 },
  'Ca(OH)2': { name: 'Calcium hydroxide', strength: 'strong', ohCount: 2 },
  'Ba(OH)2': { name: 'Barium hydroxide', strength: 'strong', ohCount: 2 },
  'NH3': { name: 'Ammonia', strength: 'weak', ohCount: 1 },
  'Mg(OH)2': { name: 'Magnesium hydroxide', strength: 'weak', ohCount: 2 },
  'Al(OH)3': { name: 'Aluminum hydroxide', strength: 'weak', ohCount: 3 }
};
