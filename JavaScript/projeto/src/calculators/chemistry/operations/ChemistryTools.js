import React, { useState } from 'react';
import * as openchemlib from 'openchemlib';
import chemcalc from 'chemcalc';
import { formatChemicalFormula } from '../constants/chemistryUtils';

// Estilos para o componente

const ChemistryTools = () => {
  const [smiles, setSmiles] = useState('');
  const [formula, setFormula] = useState('');
  const [chemcalcResult, setChemcalcResult] = useState(null);
  const [openchemlibResult, setOpenchemlibResult] = useState(null);

  // Função para analisar fórmula molecular com ChemCalc
  const analyzeWithChemCalc = () => {
    if (!formula.trim()) {
      setChemcalcResult({ error: 'Please enter a molecular formula' });
      return;
    }

    try {
      // Usando a função correta do módulo chemcalc
      const result = chemcalc.analyseMF(formula);
      setChemcalcResult(result);
    } catch (error) {
      console.error('ChemCalc error:', error);
      setChemcalcResult({ error: error.message || 'Error analyzing formula' });
    }
  };

  // Função para analisar SMILES com OpenChemLib
  const analyzeWithOpenChemLib = () => {
    if (!smiles.trim()) {
      setOpenchemlibResult({ error: 'Please enter a SMILES notation' });
      return;
    }

    try {
      // Usando a classe Molecule do módulo openchemlib
      const molecule = openchemlib.Molecule.fromSmiles(smiles);
      const mw = molecule.getMolecularWeight().toFixed(4);
      const formula = molecule.getMolecularFormula().formula;
      setOpenchemlibResult({ mw, formula });
    } catch (error) {
      console.error('OpenChemLib error:', error);
      setOpenchemlibResult({ error: error.message || 'Error analyzing SMILES' });
    }
  };



  // Função para formatar a composição elementar em uma tabela
  const renderComposition = (parts) => {
    if (!parts || !Array.isArray(parts) || !chemcalcResult || !chemcalcResult.mw) return null;

    return (
      <table className="chemistry-composition-table">
        <thead>
          <tr>
            <th className="chemistry-table-header">Element</th>
            <th className="chemistry-table-header">Count</th>
            <th className="chemistry-table-header">Mass</th>
            <th className="chemistry-table-header">Percentage</th>
          </tr>
        </thead>
        <tbody>
          {parts.map((part, index) => {
            // Verificar se part.mass existe e é um número
            if (!part || typeof part.mass !== 'number') return null;

            return (
              <tr key={index} className="chemistry-table-row">
                <td className="chemistry-table-cell">{part.element}</td>
                <td className="chemistry-table-cell">{part.number}</td>
                <td className="chemistry-table-cell">{part.mass.toFixed(4)}</td>
                <td className="chemistry-table-cell">{((part.mass / chemcalcResult.mw) * 100).toFixed(2)}%</td>
              </tr>
            );
          })}
        </tbody>
      </table>
    );
  };

  return (
    <div className="chemistry-tools-container" style={{ display: 'flex', flexDirection: 'row', flexWrap: 'nowrap', justifyContent: 'center', gap: '30px', width: '100%', maxWidth: '600px', overflow: 'visible', margin: '0 auto' }}>
      {/* ChemCalc Section */}
      <div className="chemistry-tool-section" style={{ flex: '1', maxWidth: '280px' }}>
        <div className="chemistry-section-header">
          <span>Formula Analysis</span>
        </div>

        <div className="chemistry-input-group">
          <label className="chemistry-input-label">Molecular Formula:</label>
          <input
            type="text"
            value={formula}
            onChange={(e) => setFormula(e.target.value)}
            placeholder="Enter molecular formula (e.g., H2O, CH3COOH, C6H12O6)"
            className="chemistry-input"
          />
        </div>

        <button
          className="chemistry-button"
          onClick={analyzeWithChemCalc}
        >
          Analyze Formula
        </button>

        {chemcalcResult && (
          <div className="chemistry-result-container">
            <div className="chemistry-result-title">Analysis Results</div>

            {chemcalcResult.error ? (
              <div className="chemistry-error-message">{chemcalcResult.error}</div>
            ) : (
              <>
                <div className="chemistry-result-item">
                  <span className="chemistry-result-label">Formula:</span>
                  <span className="chemistry-result-value">{formatChemicalFormula(formula)}</span>
                </div>

                <div className="chemistry-result-item">
                  <span className="chemistry-result-label">Molecular Weight:</span>
                  <span className="chemistry-result-value">{chemcalcResult.mw?.toFixed(4)} g/mol</span>
                </div>

                <div className="chemistry-result-item">
                  <span className="chemistry-result-label">Monoisotopic Mass:</span>
                  <span className="chemistry-result-value">{chemcalcResult.em?.toFixed(4)} Da</span>
                </div>

                <div className="chemistry-result-item">
                  <span className="chemistry-result-label">Elemental Composition:</span>
                  {renderComposition(chemcalcResult.parts)}
                </div>
              </>
            )}
          </div>
        )}
      </div>

      {/* OpenChemLib Section */}
      <div className="chemistry-tool-section" style={{ flex: '1', maxWidth: '280px' }}>
        <div className="chemistry-section-header">
          <span>SMILES Analysis</span>
        </div>

        <div className="chemistry-input-group">
          <label className="chemistry-input-label">SMILES Notation:</label>
          <input
            type="text"
            value={smiles}
            onChange={(e) => setSmiles(e.target.value)}
            placeholder="Enter SMILES (e.g., CCO for ethanol, c1ccccc1 for benzene)"
            className="chemistry-input"
          />
        </div>

        <button
          className="chemistry-button"
          onClick={analyzeWithOpenChemLib}
        >
          Analyze SMILES
        </button>

        {openchemlibResult && (
          <div className="chemistry-result-container">
            <div className="chemistry-result-title">Analysis Results</div>

            {openchemlibResult.error ? (
              <div className="chemistry-error-message">{openchemlibResult.error}</div>
            ) : (
              <>
                <div className="chemistry-result-item">
                  <span className="chemistry-result-label">Molecular Weight:</span>
                  <span className="chemistry-result-value">{openchemlibResult.mw} g/mol</span>
                </div>

                <div className="chemistry-result-item">
                  <span className="chemistry-result-label">Molecular Formula:</span>
                  <span className="chemistry-result-value">{formatChemicalFormula(openchemlibResult.formula)}</span>
                </div>

                <div className="chemistry-result-item">
                  <span className="chemistry-result-label">Structure:</span>
                  <span className="chemistry-result-value">{smiles}</span>
                </div>
              </>
            )}
          </div>
        )}
      </div>
    </div>
  );
};

export default ChemistryTools;
