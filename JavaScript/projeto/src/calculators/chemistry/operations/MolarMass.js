import React from 'react';
import { formatChemicalFormula } from '../constants/chemistryUtils';

const MolarMass = ({
  composto,
  setComposto,
  massaMolar,
  elementosInfo,
  formatValue,
  removeTrailingZeros
}) => {
  return (
    <div style={{ width: '100%', display: 'flex', flexDirection: 'column', alignItems: 'center', position: 'static' }}>
      <h2>Molar Mass Calculation</h2>
      <div className="molar-mass-container operation-container">
        <div className="input-group">
          <input
            type="text"
            value={composto}
            onChange={(e) => setComposto(e.target.value)}
            placeholder="Enter chemical formula"
            className="chemical-input operation-input"
            style={{ textAlign: 'center' }}
          />
        </div>

      {/* Exibição da massa molar e porcentagem dos elementos */}
      {massaMolar !== null && (
        <div className="compound-info" style={{ maxWidth: '220px', width: '220px' }}>
          <div className="molar-mass">
            <p>Formula: <strong>{formatChemicalFormula(composto)}</strong></p>
            <p>Molar Mass: <strong>{removeTrailingZeros(massaMolar)} g/mol</strong></p>
          </div>

          {elementosInfo.length > 0 && (
            <div className="element-percentages">
              <h4>Composition:</h4>
              <table className="percentage-table">
                <thead>
                  <tr>
                    <th>Elem</th>
                    <th>Qty</th>
                    <th>Mass</th>
                    <th>%</th>
                  </tr>
                </thead>
                <tbody>
                  {elementosInfo.map((elemento, index) => (
                    <tr key={index}>
                      <td>{elemento.simbolo}</td>
                      <td>{elemento.quantidade}</td>
                      <td>{formatValue(elemento.massaTotal)}</td>
                      <td>{elemento.porcentagem}%</td>
                    </tr>
                  ))}
                </tbody>
              </table>
            </div>
          )}
        </div>
      )}
      </div>
    </div>
  );
};

export default MolarMass;
