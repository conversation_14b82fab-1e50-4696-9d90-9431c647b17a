import React from 'react';
import TabSwitcher from '../../components/tabs/TabSwitcher';
import './CompoundCarousel.css'; // Reutilizamos os estilos dos slides

/**
 * CompoundTabSwitcher - Versão do CompoundCarousel que usa o TabSwitcher
 * em vez de um carousel tradicional
 *
 * @param {Object} props - Propriedades do componente
 * @param {string} props.composto - Fórmula do composto
 * @param {number} props.massaMolar - Massa molar do composto
 * @param {Array} props.elementosInfo - Informações sobre os elementos do composto
 * @param {Function} props.formatValue - Função para formatar valores numéricos
 * @param {Function} props.removeTrailingZeros - Função para remover zeros à direita
 * @param {Object} props.style - Estilos adicionais para o componente
 * @param {Object} props.menuStyle - Estilos adicionais para o menu de navegação
 * @param {Object} props.contentStyle - Estilos adicionais para o conteúdo
 * @param {string} props.className - Classes adicionais para o componente
 * @param {string} props.menuClassName - Classes adicionais para o menu
 * @param {string} props.contentClassName - Classes adicionais para o conteúdo
 */
const CompoundTabSwitcher = ({
  composto,
  massaMolar,
  elementosInfo,
  formatValue,
  removeTrailingZeros,
  style = {},
  menuStyle = {},
  contentStyle = {},
  className = '',
  menuClassName = '',
  contentClassName = ''
}) => {
  // Definir as abas disponíveis (mesmo conteúdo do CompoundCarousel original)
  const tabs = [
    {
      id: 'info',
      title: 'Information',
      component: (
        <div className="compound-slide">
          <div className="compound-info">
            <div className="molar-mass">
              <p>Molar Mass: <strong>{massaMolar ? `${removeTrailingZeros(massaMolar)} g/mol` : '-'}</strong></p>
            </div>
            {elementosInfo && elementosInfo.length > 0 && (
              <div className="element-summary">
                <p>Elements: {elementosInfo.map(el => el.simbolo).join(', ')}</p>
              </div>
            )}
          </div>
        </div>
      )
    },
    {
      id: 'composition',
      title: 'Composition',
      component: (
        <div className="compound-slide">
          {elementosInfo && elementosInfo.length > 0 ? (
            <div className="element-percentages">
              <h4>Elemental Composition:</h4>
              <table className="percentage-table">
                <thead>
                  <tr>
                    <th>Element</th>
                    <th>Quantity</th>
                    <th>Mass (g/mol)</th>
                    <th>Percentage (%)</th>
                  </tr>
                </thead>
                <tbody>
                  {elementosInfo.map((elemento, index) => (
                    <tr key={index}>
                      <td>{elemento.simbolo}</td>
                      <td>{elemento.quantidade}</td>
                      <td>{formatValue(elemento.massaTotal)}</td>
                      <td>{elemento.porcentagem}%</td>
                    </tr>
                  ))}
                </tbody>
              </table>
            </div>
          ) : (
            <p className="no-data">No composition data available</p>
          )}
        </div>
      )
    },
    {
      id: 'properties',
      title: 'Properties',
      component: (
        <div className="compound-slide">
          <div className="compound-properties">
            <h4>Physical Properties:</h4>
            <p>Formula: <strong>{composto || '-'}</strong></p>
            <p>Molar Mass: <strong>{massaMolar ? `${removeTrailingZeros(massaMolar)} g/mol` : '-'}</strong></p>
            {/* Aqui podem ser adicionadas mais propriedades no futuro */}
          </div>
        </div>
      )
    }
  ];

  return (
    <div className={`compound-tab-switcher ${className}`} style={style}>
      <TabSwitcher
        tabs={tabs}
        menuStyle={menuStyle}
        contentStyle={contentStyle}
        menuClassName={menuClassName}
        contentClassName={contentClassName}
      />
    </div>
  );
};

export default CompoundTabSwitcher;
