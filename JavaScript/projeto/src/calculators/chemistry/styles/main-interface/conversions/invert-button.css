/**
 * Estilos para o botão de inverter conversão
 */

.invert-button {
  width: 30px;
  height: 30px;
  border-radius: 50%;
  background-color: rgba(0, 0, 0, 0.8);
  border: 2px solid var(--border-color);
  color: white;
  display: flex;
  align-items: center;
  justify-content: center;
  cursor: pointer;
  transition: all 0.3s ease;
  margin: 0 10px;
  padding: 0;
  font-size: 16px;
}

.invert-button:hover {
  background-color: rgba(76, 175, 80, 0.2);
  border-color: var(--primary-color);
  transform: rotate(180deg);
}

.invert-button:active {
  transform: scale(0.9) rotate(180deg);
}

.invert-button svg {
  width: 16px;
  height: 16px;
  fill: white;
}

/* Animação de rotação */
@keyframes rotate {
  from {
    transform: rotate(0deg);
  }
  to {
    transform: rotate(180deg);
  }
}

.invert-button.rotating {
  animation: rotate 0.3s ease-in-out;
}
