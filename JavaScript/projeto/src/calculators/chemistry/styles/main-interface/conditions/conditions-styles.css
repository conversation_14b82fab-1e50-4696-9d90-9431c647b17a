/**
 * Estilos para a interface de condições
 */

/* Estilos para os novos componentes */
.temperatura-container, .ph-container, .pressao-container, .diluicao-container {
  margin-bottom: 15px;
  width: 100%;
}

.temperatura-input, .ph-input, .pressao-input {
  display: flex;
  align-items: center;
  margin-bottom: 10px;
}

.temperatura-input input, .ph-input input, .pressao-input input {
  margin-right: 10px;
}

.temperatura-input label, .pressao-input label {
  min-width: 30px;
}

.temperatura-buttons, .pressao-buttons {
  display: flex;
  gap: 5px;
}

.diluicao-container input {
  margin-bottom: 10px;
}

/* Presets movidos para presets.css */

.conditions-inputs {
  display: grid;
  grid-template-columns: repeat(2, 1fr);
  gap: 20px;
  margin-top: 20px;
}

.conditions-inputs .input-group {
  display: flex;
  flex-direction: column;
  gap: 8px;
}

.conditions-inputs label {
  color: var(--text-color);
  font-size: 0.9em;
}

.conditions-inputs input {
  padding: 10px;
  border-radius: 6px;
  border: 1px solid var(--border-color);
  background: rgba(255, 255, 255, 0.05);
  color: white;
  transition: all 0.3s ease;
}

.conditions-inputs input:focus {
  border-color: var(--primary-color);
  box-shadow: 0 0 15px rgba(76, 175, 80, 0.3);
  outline: none;
}

.condition-row {
  display: flex;
  flex-wrap: nowrap !important;
  justify-content: center;
  align-items: center;
  margin-bottom: 10px;
  position: relative;
  left: -7px !important;
  gap: 7px !important;
}

/* Responsividade */
@media (max-width: 768px) {
  .conditions-inputs {
    grid-template-columns: 1fr;
  }

  .presets-buttons {
    flex-direction: column;
    align-items: stretch;
  }

  .preset-button {
    width: 100%;
    text-align: center;
  }
}

/* Estilos para inputs dentro da calculadora de diluição */
.dilution-inputs {
  display: grid;
  gap: 15px;
  width: 100%;
}

.dilution-inputs .input-group {
  margin: 0;
}

.dilution-inputs label {
  display: block;
  margin-bottom: 5px;
  color: var(--text-color);
  font-size: 0.9em;
}
