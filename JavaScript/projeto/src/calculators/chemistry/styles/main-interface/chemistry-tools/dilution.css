/* Estilos para inputs dentro da calculadora de diluição */
.dilution-inputs input {
  margin-bottom: 10px;
}

.dilution-inputs {
  display: grid;
  gap: 15px;
  width: 100%;
}

.dilution-inputs .input-group {
  margin: 0;
}

.dilution-inputs label {
  display: block;
  margin-bottom: 5px;
  color: var(--text-color);
  font-size: 0.9em;
}

.swap-button,
.calculate-button {
  width: 100%;
  margin-top: 15px;
}

.input-group {
  width: 100%;
  display: flex;
  flex-direction: column;
  align-items: center;
  gap: 15px;
}

.input-group input {
  width: 100%;
  max-width: 400px;
  padding: 10px;
  border-radius: 5px;
  border: 1px solid var(--border-color);
  background: rgba(255, 255, 255, 0.05);
  color: var(--text-color);
  text-align: center;
}

.result {
  width: 100%;
  max-width: 400px;
  text-align: center;
  padding: 10px;
  background-color: rgba(76, 175, 80, 0.1);
  border-radius: 5px;
  color: var(--text-color);
  font-size: 1.1em;
  margin: 0 auto;
}
