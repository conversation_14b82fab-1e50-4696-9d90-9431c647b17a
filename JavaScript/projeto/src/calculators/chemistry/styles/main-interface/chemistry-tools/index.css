/* Estilos para o componente ChemistryTools */

@import './dilution.css';

.chemistry-tools-container {
  display: flex;
  flex-direction: column;
  align-items: center;
  width: 100%;
  max-width: 800px;
  margin: 0 auto;
  padding: 15px;
  background-color: rgba(0, 0, 0, 0.2);
  border: 2px solid var(--border-color);
  border-radius: var(--border-radius-md);
}

.chemistry-tools-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  width: 100%;
  margin-bottom: 15px;
}

.chemistry-tools-title {
  font-size: var(--font-size-large);
  color: var(--primary-color);
  margin: 0;
}

.chemistry-tools-grid {
  display: grid;
  grid-template-columns: repeat(auto-fill, minmax(200px, 1fr));
  gap: 15px;
  width: 100%;
}

.chemistry-tool-card {
  display: flex;
  flex-direction: column;
  align-items: center;
  padding: 15px;
  background-color: rgba(0, 0, 0, 0.3);
  border: 1px solid var(--border-color);
  border-radius: var(--border-radius-sm);
  transition: transform var(--transition-speed) var(--transition-timing),
              box-shadow var(--transition-speed) var(--transition-timing);
  cursor: pointer;
}

.chemistry-tool-card:hover {
  transform: translateY(-5px);
  box-shadow: 0 5px 15px rgba(76, 175, 80, 0.2);
}

.chemistry-tool-icon {
  font-size: 32px;
  color: var(--primary-color);
  margin-bottom: 10px;
}

.chemistry-tool-name {
  font-size: var(--font-size-normal);
  color: var(--text-color);
  text-align: center;
  margin: 0;
}

.chemistry-tool-description {
  font-size: var(--font-size-small);
  color: #aaa;
  text-align: center;
  margin: 5px 0 0 0;
}

/* Estilos para ferramentas específicas */
.periodic-table-container {
  width: 100%;
  overflow-x: auto;
  margin-top: 15px;
}

.molecular-viewer-container {
  width: 100%;
  height: 300px;
  margin-top: 15px;
  border: 1px solid var(--border-color);
  border-radius: var(--border-radius-sm);
}

.reaction-balancer-container {
  width: 100%;
  margin-top: 15px;
}

.reaction-input {
  width: 100%;
  padding: 8px 12px;
  background-color: rgba(0, 0, 0, 0.3);
  border: 1px solid var(--border-color);
  border-radius: var(--border-radius-sm);
  color: var(--text-color);
  font-size: var(--font-size-normal);
  text-align: center;
  margin-bottom: 10px;
}

.balanced-reaction {
  padding: 10px;
  background-color: rgba(0, 0, 0, 0.3);
  border: 1px solid var(--primary-color);
  border-radius: var(--border-radius-sm);
  color: var(--text-color);
  font-size: var(--font-size-normal);
  text-align: center;
  margin-top: 10px;
}

/* Estilos responsivos */
@media (max-width: 768px) {
  .chemistry-tools-grid {
    grid-template-columns: repeat(auto-fill, minmax(150px, 1fr));
  }
}
