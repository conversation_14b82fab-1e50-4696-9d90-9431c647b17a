/**
 * Estilos para a interface de conversão de unidades
 */

.unit-conversion-block {
  margin-top: 20px;
  padding: 20px;
}

.unit-conversion-container {
  display: flex;
  flex-direction: column;
  gap: 15px;
  align-items: center;
}

.unit-conversion-input,
.unit-conversion-output {
  display: flex;
  flex-direction: column;
  gap: 10px;
  width: 100%;
  max-width: 400px;
}

.unit-selectors {
  display: flex;
  gap: 10px;
}

.unit-selectors select {
  flex: 1;
  padding: 8px;
  background-color: rgba(0, 0, 0, 0.3);
  color: white;
  border: 1px solid var(--border-color);
  border-radius: 4px;
}

.unit-conversion-container input {
  width: 100%;
  padding: 10px;
  background: rgba(0, 0, 0, 0.3);
  color: white;
  border: 1px solid var(--border-color);
  border-radius: 4px;
}

.unit-conversion-container input:focus {
  outline: none;
  border-color: var(--primary-color);
  box-shadow: 0 0 15px rgba(76, 175, 80, 0.5);
}

.convert-button {
  padding: 10px 20px;
  background-color: var(--primary-color);
  color: white;
  border: none;
  border-radius: 4px;
  cursor: pointer;
  transition: background-color 0.3s;
}

.convert-button:hover {
  background-color: #45a049;
}

/* Estilos adicionais do App.css */
.unit-select {
  width: 100%;
  padding: 10px;
  padding-right: 30px;
  background-color: black !important;
  background-image: url("data:image/svg+xml;charset=UTF-8,%3csvg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 24 24' fill='white'%3e%3cpath d='M7 10l5 5 5-5z'/%3e%3c/svg%3e") !important;
  background-repeat: no-repeat !important;
  background-position: right 10px center !important;
  background-size: 20px !important;
  border: none;
  color: white;
  font-size: 16px;
  cursor: pointer;
  -webkit-appearance: none;
  -moz-appearance: none;
  appearance: none;
}

.unit-select:focus {
  background-color: black !important;
  background-image: url("data:image/svg+xml;charset=UTF-8,%3csvg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 24 24' fill='white'%3e%3cpath d='M7 10l5 5 5-5z'/%3e%3c/svg%3e") !important;
  background-repeat: no-repeat !important;
  background-position: right 10px center !important;
  background-size: 20px !important;
  outline: none;
  box-shadow: none;
}

.unit-select option {
  background-color: black !important;
  color: white;
  padding: 12px;
}

.unit-select:focus-visible {
  outline: none;
  border: none;
}

.unit-select::-ms-expand {
  display: none;
}

@media (max-width: 600px) {
  .unit-selectors {
    flex-direction: column;
  }
}
