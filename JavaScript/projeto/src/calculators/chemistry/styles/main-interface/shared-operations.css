/* =========================================================================
   CONTAINER DE OPERAÇÕES
   ========================================================================= */
.operation-container {
  width: 90%;                             /* Largura reduzida */
  max-width: 540px;                       /* Largura máxima */
  margin: 0 auto;                         /* Centralização horizontal */
  padding: 15px;                          /* Espaçamento interno */
  background: rgba(255, 255, 255, 0.1);   /* Fundo branco semi-transparente */
  border-radius: 12px;                    /* Bordas arredondadas */
  border: 2px solid var(--border-color);  /* Borda verde */
  transition: all 0.3s ease;              /* Transição suave */
  display: flex;                          /* Layout flexbox */
  flex-direction: column;                 /* Direção de coluna */
  align-items: center;                    /* Alinhamento centralizado */
  position: static;                       /* Posicionamento estático */
  box-shadow: 0 4px 8px rgba(0, 0, 0, 0.2); /* Sombra suave */
}

/* =========================================================================
   BLOCOS DE OPERAÇÃO
   ========================================================================= */
.operation-block {
  width: 100%;                            /* Largura total */
  background-color: rgba(255, 255, 255, 0.05); /* Fundo branco transparente */
  border: 2px solid rgba(76, 175, 80, 0.5); /* Borda verde */
  border-radius: 10px;                    /* Bordas arredondadas */
  padding: 15px;                          /* Espaçamento interno */
  margin-bottom: 15px;                    /* Margem inferior */
  box-shadow: 0 2px 5px rgba(0, 0, 0, 0.1); /* Sombra suave */
  transition: all 0.3s ease;              /* Transição suave */
}

.operation-block:hover {
  border-color: var(--primary-color);     /* Borda verde mais forte no hover */
  box-shadow: 0 0 10px rgba(76, 175, 80, 0.3); /* Sombra verde */
  background-color: rgba(255, 255, 255, 0.1); /* Fundo mais claro no hover */
}

.operation-container:hover {
  border-color: var(--primary-color);     /* Borda verde mais forte no hover */
  box-shadow: 0 0 15px rgba(76, 175, 80, 0.5); /* Sombra verde */
  background: rgba(255, 255, 255, 0.15);  /* Fundo mais claro no hover */
}

/* =========================================================================
   TÍTULOS DE OPERAÇÃO
   ========================================================================= */
.operation-title {
  color: var(--primary-color);            /* Cor verde */
  font-size: 1.5rem;                      /* Tamanho da fonte */
  margin-bottom: 15px;                    /* Margem inferior */
  text-align: center;                     /* Alinhamento centralizado */
}

/* =========================================================================
   CAMPOS DE ENTRADA
   ========================================================================= */
.operation-input {
  width: 100%;                            /* Largura total */
  height: 35px;                           /* Altura fixa */
  padding: 0 15px;                        /* Espaçamento horizontal */
  border-radius: 8px;                     /* Bordas arredondadas */
  border: 1px solid var(--border-color);  /* Borda verde sutil */
  background: rgba(255, 255, 255, 0.05);  /* Fundo branco transparente */
  color: white;                           /* Texto branco */
  font-size: 14px;                        /* Tamanho da fonte */
  font-weight: 500;                       /* Peso da fonte */
  transition: all 0.3s ease;              /* Transição suave */
  text-align: center;                     /* Alinhamento centralizado */
  max-width: 90%;                         /* Largura máxima */
  margin-bottom: 10px;                    /* Margem inferior */
}

.operation-input:focus {
  outline: none;                          /* Remove contorno padrão */
  border-color: var(--primary-color);     /* Borda verde */
  box-shadow: 0 0 10px rgba(76, 175, 80, 0.5); /* Brilho verde */
  background: rgba(255, 255, 255, 0.08);  /* Fundo mais claro no foco */
}

/* =========================================================================
   SELECTS DE OPERAÇÃO
   ========================================================================= */
.operation-select {
  width: 100%;                            /* Largura total */
  height: 35px;                           /* Altura fixa */
  padding: 0 15px;                        /* Espaçamento horizontal */
  border-radius: 8px;                     /* Bordas arredondadas */
  border: 1px solid var(--border-color);  /* Borda verde sutil */
  background: rgba(255, 255, 255, 0.05);  /* Fundo branco transparente */
  color: white;                           /* Texto branco */
  font-size: 14px;                        /* Tamanho da fonte */
  font-weight: 500;                       /* Peso da fonte */
  transition: all 0.3s ease;              /* Transição suave */
  text-align: center;                     /* Alinhamento centralizado */
  max-width: 90%;                         /* Largura máxima */
  margin-bottom: 10px;                    /* Margem inferior */
  -webkit-appearance: none;               /* Remove aparência padrão no Safari */
  -moz-appearance: none;                  /* Remove aparência padrão no Firefox */
  appearance: none;                       /* Remove aparência padrão */
  background-image: url("data:image/svg+xml;utf8,<svg fill='white' height='24' viewBox='0 0 24 24' width='24' xmlns='http://www.w3.org/2000/svg'><path d='M7 10l5 5 5-5z'/><path d='M0 0h24v24H0z' fill='none'/></svg>");
  background-repeat: no-repeat;           /* Sem repetição da imagem */
  background-position: right 10px center;  /* Posição da seta */
}

/* =========================================================================
   BOTÕES DE OPERAÇÃO
   ========================================================================= */
.operation-button {
  padding: 8px 20px;                      /* Espaçamento interno */
  background-color: var(--primary-color); /* Fundo verde */
  color: white;                           /* Texto branco */
  border: none;                           /* Sem borda */
  border-radius: 8px;                     /* Bordas arredondadas */
  cursor: pointer;                        /* Cursor de ponteiro */
  font-size: 14px;                        /* Tamanho da fonte */
  transition: all 0.3s ease;              /* Transição suave */
  margin-top: 15px;                       /* Margem superior */
  width: 90%;                             /* Largura relativa */
  max-width: 180px;                       /* Largura máxima */
}

.operation-button:hover {
  background-color: var(--primary-dark);  /* Fundo verde escuro no hover */
  transform: translateY(-2px);            /* Efeito de elevação */
  box-shadow: 0 0 12px rgba(76, 175, 80, 0.6); /* Brilho verde */
}

/* =========================================================================
   RESPONSIVIDADE
   ========================================================================= */
@media (max-width: 768px) {
  .operation-container {
    padding: 10px;                        /* Espaçamento interno reduzido */
    width: 95%;                           /* Largura aumentada */
    max-width: 500px;                     /* Largura máxima ajustada */
  }

  .operation-input {
    height: 30px;                         /* Altura reduzida */
    font-size: 12px;                      /* Fonte menor */
    max-width: 95%;                       /* Largura máxima aumentada */
  }

  .operation-button {
    padding: 6px 15px;                    /* Espaçamento interno reduzido */
    font-size: 12px;                      /* Fonte menor */
    width: 95%;                           /* Largura aumentada */
    max-width: 160px;                     /* Largura máxima reduzida */
  }
}
