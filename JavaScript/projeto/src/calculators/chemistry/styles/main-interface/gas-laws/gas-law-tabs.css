/* =========================================================================
   TABS DAS LEIS DOS GASES
   ========================================================================= */

/* Container dos tabs */
.gas-law-tabs {
  display: flex;
  justify-content: space-between;
  align-items: center;
  width: 100%;
  margin-bottom: 20px;
  box-shadow: 0 4px 8px rgba(0, 0, 0, 0.2);
  gap: 10px;
}

/* Botões dos tabs */
.gas-law-tab {
  flex: 1;
  padding: 12px 8px;
  background: rgba(0, 0, 0, 0.2);
  border: 1px solid var(--border-color);
  color: var(--text-color);
  border-radius: 5px;
  cursor: pointer;
  transition: all 0.3s ease;
  text-align: center;
  font-size: 12px;
  line-height: 1.2;
  min-height: 50px;
  display: flex;
  flex-direction: column;
  justify-content: center;
  align-items: center;
  white-space: nowrap;
  overflow: hidden;
}

/* Quebra de linha para separar nome da fórmula */
.gas-law-tab .law-name {
  display: block;
  font-weight: bold;
  margin-bottom: 2px;
}

.gas-law-tab .law-formula {
  display: block;
  font-size: 10px;
  opacity: 0.8;
  font-family: monospace;
}

/* Estado ativo */
.gas-law-tab.active {
  background: var(--primary-color);
  border-color: var(--primary-color);
  color: white;
}

/* Hover */
.gas-law-tab:hover {
  background: rgba(76, 175, 80, 0.2);
  border-color: var(--primary-color);
}

.gas-law-tab.active:hover {
  background: var(--primary-color);
}

/* =========================================================================
   EQUAÇÃO COM FUNDO PRETO
   ========================================================================= */
.gas-law-equation {
  position: relative;
  padding: 15px;
  margin-bottom: 15px;
  background: rgba(0, 0, 0, 0.8);
  border-radius: 8px;
  border: 1px solid var(--primary-color);
}

.gas-law-equation .r-constant {
  position: absolute;
  top: 5px;
  left: 10px;
  color: var(--primary-color);
  font-size: 12px;
  font-family: monospace;
  font-weight: bold;
  margin: 0;
  padding: 0;
}

.gas-law-equation .equation-main {
  color: var(--primary-color);
  font-size: 18px;
  font-weight: bold;
  text-align: center;
  margin: 0 0 5px 0;
  font-family: monospace;
}

.gas-law-equation .equation-description {
  color: #ccc;
  font-size: 12px;
  text-align: center;
  margin: 0;
  font-style: italic;
}

/* =========================================================================
   CONTAINER DE PRESETS HORIZONTAL
   ========================================================================= */
.presets-container {
  display: flex;
  gap: 10px;
  margin-bottom: 20px;
  justify-content: center;
  width: 100%;
  flex-wrap: wrap;
}

.presets-container .preset-button {
  flex: 1;
  min-width: 120px;
  max-width: 150px;
  padding: 10px 15px;
  border-radius: 6px;
  border: 1px solid var(--border-color);
  background: rgba(255, 255, 255, 0.05);
  color: white;
  cursor: pointer;
  transition: all 0.3s ease;
  text-align: center;
  font-size: 12px;
  white-space: nowrap;
}

.presets-container .preset-button:hover {
  background: rgba(255, 255, 255, 0.1);
  border-color: var(--primary-color);
}

.presets-container .preset-button.active {
  background: var(--primary-color);
  border-color: var(--primary-color);
}

/* =========================================================================
   INPUTS PADRONIZADOS COM UNIDADES
   ========================================================================= */

/* Layout dos parâmetros */
.gas-law-parameters {
  display: flex;
  flex-direction: column;
  gap: 15px;
  width: 100%;
}

.gas-law-row {
  display: flex;
  gap: 15px;
  width: 100%;
}

.gas-law-parameter {
  flex: 1;
  display: flex;
  flex-direction: column;
  gap: 5px;
}

.gas-law-parameter label {
  color: var(--text-color);
  font-size: 14px;
  font-weight: 500;
  text-align: left;
}

/* Container do input com unidade */
.parameter-input-container {
  position: relative;
  display: inline-block;
  width: 100%;
}

.gas-law-content input {
  width: 100%;
  height: 35px;
  padding: 8px 40px 8px 12px; /* Padding direito maior para a unidade */
  border-radius: 5px;
  border: 1px solid var(--border-color);
  background: rgba(255, 255, 255, 0.05);
  color: var(--text-color);
  text-align: center;
  font-size: 14px;
  transition: all 0.3s ease;
  box-sizing: border-box;
}

.gas-law-content input:focus {
  outline: none;
  border-color: var(--primary-color);
  background: rgba(255, 255, 255, 0.1);
  box-shadow: 0 0 5px rgba(76, 175, 80, 0.3);
}

.gas-law-content input:invalid {
  border-color: #ff6b6b;
  background: rgba(255, 107, 107, 0.1);
}

/* Estilo para a unidade ao lado do input */
.parameter-unit {
  position: absolute;
  right: 12px;
  top: 50%;
  transform: translateY(-50%);
  color: rgba(255, 255, 255, 0.7);
  font-size: 12px;
  font-weight: 500;
  font-family: monospace;
  pointer-events: none;
  z-index: 1;
}

/* Responsividade */
@media (max-width: 768px) {
  .gas-law-tab {
    font-size: 10px;
    padding: 8px 4px;
    min-height: 45px;
  }

  .gas-law-tab .law-formula {
    font-size: 9px;
  }

  .presets-container {
    flex-direction: column;
    align-items: center;
  }

  .presets-container .preset-button {
    max-width: 200px;
  }

  .gas-law-row {
    flex-direction: column;
    gap: 10px;
  }

  .gas-law-parameter {
    width: 100%;
  }

  .parameter-input-container {
    width: 100%;
  }

  .gas-law-content input {
    width: 100%;
  }
}
