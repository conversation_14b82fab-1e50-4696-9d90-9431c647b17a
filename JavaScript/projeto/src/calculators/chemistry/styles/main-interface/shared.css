/* =========================================================================
   BLOCO PRINCIPAL DA CALCULADORA
   ========================================================================= */
.calculator-block {
  background-color: var(--panel-bg);       /* Fundo semi-transparente */
  border-radius: 15px;                     /* Bordas arredondadas */
  padding: 25px;                           /* Espaçamento interno original */
  margin: 20px 0;                          /* Margens verticais originais */
  width: 100%;                             /* Largura total */
  max-width: 800px;                        /* Largura máxima original */
  border: 1px solid var(--border-color);   /* Borda verde sutil */
  box-shadow: 0 4px 6px rgba(0, 0, 0, 0.1); /* Sombra suave */
  box-sizing: border-box;                   /* Incluir padding na largura */
  transition: all 0.3s ease;               /* Transição suave */
  box-sizing: border-box;                  /* Modelo de caixa */
}

.calculator-block:hover {
  border-color: var(--primary-color);      /* Borda verde mais forte no hover */
  box-shadow: 0 0 20px rgba(76, 175, 80, 0.3); /* Sombra mais intensa */
}

/* =========================================================================
   NAVEGAÇÃO POR ABAS
   ========================================================================= */
.tabs {
  position: relative;                      /* Posicionamento relativo */
  left: 0;                                 /* Alinhamento à esquerda */
  display: flex;                           /* Layout flexbox */
  gap: 20px;                               /* Espaçamento entre botões */
  margin-bottom: 20px;                     /* Margem inferior */
  justify-content: center;                 /* Centralização horizontal */
  width: 100%;                             /* Largura total */
}

.tab-button {
  padding: 10px 20px;                      /* Espaçamento interno */
  background: rgba(0, 0, 0, 0.2);          /* Fundo escuro transparente */
  border: 1px solid var(--border-color);   /* Borda verde sutil */
  color: var(--text-color);                /* Cor do texto */
  border-radius: 5px;                      /* Bordas arredondadas */
  cursor: pointer;                         /* Cursor de ponteiro */
  transition: all 0.3s ease;               /* Transição suave */
  width: 150px;                            /* Largura fixa */
  text-align: center;                      /* Centralização do texto */
  margin: 0;                               /* Sem margens */
}

.tab-button.active {
  background: var(--primary-color);        /* Fundo verde */
  border-color: var(--primary-color);      /* Borda verde */
  color: white;                            /* Texto branco */
}

/* =========================================================================
   ELEMENTOS DE ENTRADA
   ========================================================================= */
.input-base {
  width: 100%;                             /* Largura total */
  background: transparent;                 /* Fundo transparente */
  border: none;                            /* Sem borda */
  color: white;                            /* Texto branco */
  font-size: 18px;                         /* Tamanho da fonte */
  resize: none;                            /* Sem redimensionamento */
  outline: none;                           /* Sem contorno */
  box-sizing: border-box;                  /* Modelo de caixa */
}

/* =========================================================================
   PAINÉIS BASE
   ========================================================================= */
.panel-base {
  background: rgba(0, 0, 0, 0.2);          /* Fundo escuro transparente */
  border-radius: 12px;                     /* Bordas arredondadas */
  border: 2px solid var(--border-color);   /* Borda verde sutil */
  overflow: hidden;                        /* Oculta conteúdo excedente */
  transition: border-color 0.3s ease, box-shadow 0.3s ease; /* Transição suave */
}

.panel-base:focus-within {
  border-color: var(--primary-color);      /* Borda verde mais forte no foco */
  box-shadow: 0 0 15px rgba(76, 175, 80, 0.5); /* Sombra verde */
}