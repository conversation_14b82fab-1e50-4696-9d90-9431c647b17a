/**
 * Debug info panel styles
 */

.debug-info-panel {
  position: fixed;
  top: 20px;
  right: 20px;
  background-color: rgba(0, 0, 0, 0.8);
  border: 1px solid var(--primary-color);
  border-radius: 4px;
  padding: 15px;
  z-index: 9999;
  width: 300px;
  max-height: 80vh;
  overflow-y: auto;
  color: white;
  font-family: monospace;
  font-size: 12px;
  box-shadow: 0 0 10px rgba(0, 0, 0, 0.5);
  transition: all 0.3s ease;
}

.debug-info-panel-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 15px;
  padding-bottom: 10px;
  border-bottom: 1px solid var(--primary-color);
}

.debug-info-panel-title {
  font-weight: bold;
  color: var(--primary-color);
  margin: 0;
  font-size: 14px;
}

.debug-info-panel-close {
  background: none;
  border: none;
  color: white;
  cursor: pointer;
  font-size: 16px;
  padding: 0;
}

.debug-info-panel-section {
  margin-bottom: 15px;
  padding-bottom: 10px;
  border-bottom: 1px solid rgba(255, 255, 255, 0.1);
}

.debug-info-panel-section-title {
  font-weight: bold;
  margin-bottom: 8px;
  color: var(--primary-color);
  font-size: 13px;
}

.debug-info-panel-item {
  margin-bottom: 5px;
  display: flex;
  flex-direction: column;
}

.debug-info-panel-item-label {
  font-weight: bold;
  margin-right: 5px;
  color: rgba(255, 255, 255, 0.7);
}

.debug-info-panel-item-value {
  font-family: monospace;
  word-break: break-all;
}

/* Checkbox styles */
.debug-info-panel label {
  display: flex;
  align-items: center;
  margin-bottom: 8px;
  cursor: pointer;
}

.debug-info-panel input[type="checkbox"] {
  margin-right: 8px;
}

.debug-info-panel label span {
  font-size: 12px;
}

/* Button styles */
.debug-info-panel-button {
  background-color: rgba(76, 175, 80, 0.2);
  border: 1px solid var(--primary-color);
  color: white;
  padding: 5px 10px;
  border-radius: 3px;
  cursor: pointer;
  font-size: 12px;
  margin-right: 5px;
  margin-bottom: 5px;
  transition: all 0.2s ease;
}

.debug-info-panel-button:hover {
  background-color: var(--primary-color);
}

.debug-info-panel-buttons {
  display: flex;
  flex-wrap: wrap;
  margin-top: 10px;
}

/* Collapsible sections */
.debug-info-panel-collapsible {
  margin-bottom: 10px;
}

.debug-info-panel-collapsible-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 5px;
  background-color: rgba(255, 255, 255, 0.1);
  border-radius: 3px;
  cursor: pointer;
}

.debug-info-panel-collapsible-title {
  font-weight: bold;
  color: var(--primary-color);
}

.debug-info-panel-collapsible-content {
  padding: 10px 5px;
  border-left: 1px solid rgba(255, 255, 255, 0.1);
  margin-left: 5px;
}

/* Responsive styles */
@media (max-width: 768px) {
  .debug-info-panel {
    width: 250px;
    top: 10px;
    right: 10px;
    padding: 10px;
  }
}
