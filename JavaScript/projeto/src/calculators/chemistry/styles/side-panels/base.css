/* ==========================================================================
   PAINÉIS LATERAIS - ESTILOS BASE
   ========================================================================== */

/* Classe base para painéis */
.panel {
  position: fixed;
  background: rgba(0, 0, 0, 0.8);
  border: 1px solid var(--border-color);
  border-radius: 6px;
  padding: 10px;
  width: 222px;
  box-sizing: border-box;
  backdrop-filter: blur(5px);
  z-index: 1000;
  box-shadow: 0 4px 6px rgba(0, 0, 0, 0.1);
  transition: opacity 0.3s ease, visibility 0.3s ease, transform 0.3s ease;
}

/* Ocultar painéis quando a classe panels-hidden estiver no body */
body.panels-hidden .panel {
  opacity: 0;
  visibility: hidden;
  transform: translateX(-20px);
  pointer-events: none;
}

/* Painéis do lado direito se movem para a direita quando ocultos */
body.panels-hidden .panel.right {
  transform: translateX(20px);
}

/* Estilização da barra de rolagem para painéis */
.panel::-webkit-scrollbar {
  width: 4px;
}

.panel::-webkit-scrollbar-track {
  background: transparent;
}

.panel::-webkit-scrollbar-thumb {
  background-color: var(--primary-color);
  border-radius: 2px;
}

.panel::-webkit-scrollbar-thumb:hover {
  background-color: var(--primary-dark);
}

/* Estilo para cada bloco dentro do painel */
.panel-block {
  background-color: rgba(255, 255, 255, 0.1);
  border: 2px solid rgba(76, 175, 80, 0.5);
  border-radius: 12px;
  padding: 15px;
  margin-bottom: 20px;
  box-shadow: 0 4px 8px rgba(0, 0, 0, 0.2);
  transition: all 0.3s ease;
}

.panel-block:hover {
  border-color: var(--primary-color);
  box-shadow: 0 0 15px rgba(76, 175, 80, 0.3);
  background-color: rgba(255, 255, 255, 0.15);
}

.panel-block-title {
  color: var(--primary-color);
  font-size: 1.2rem;
  margin-bottom: 10px;
  text-align: center;
  border-bottom: 1px solid rgba(76, 175, 80, 0.3);
  padding-bottom: 8px;
}

/* =========================================================================
   ITENS DOS PAINÉIS - CONFIGURAÇÕES DE LAYOUT E ESTILO
   ========================================================================= */
.panel .parameter-item {
  display: flex;                 /* Layout flexbox */
  justify-content: space-between; /* Espaço entre label e valor */
  align-items: center;           /* Centralização vertical */
  padding: 4px 0;                /* Espaçamento vertical */
  border-bottom: 1px solid rgba(255, 255, 255, 0.1); /* Linha separadora */
}

.panel .parameter-item:last-child {
  border-bottom: none;           /* Remove a borda do último item */
}

/* =========================================================================
   LABELS DOS PAINÉIS - CONFIGURAÇÕES DE ESTILO
   ========================================================================= */
.panel .parameter-label {
  color: var(--primary-color);   /* Cor verde */
  font-size: 10.505px;              /* Tamanho da fonte */
  font-weight: 500;              /* Peso da fonte */
  white-space: nowrap;           /* Evita quebra de linha */
  width: 90px;                   /* Largura fixa */
  text-align: left;              /* Alinhamento à esquerda */
  padding-left: 10px;            /* Espaçamento à esquerda */
}

/* =========================================================================
   VALORES DOS PAINÉIS - CONFIGURAÇÕES DE ESTILO
   ========================================================================= */
.panel .parameter-value {
  color: var(--text-color);      /* Cor do texto (branco) */
  font-family: monospace;        /* Fonte monoespaçada */
  font-size: 10.505px;           /* Tamanho da fonte */
  margin-left: 10px;             /* Margem à esquerda */
  display: flex;                 /* Layout flexbox */
  align-items: center;           /* Centralização vertical */
  gap: 5px;                      /* Espaçamento entre elementos */
  justify-content: flex-end;     /* Alinha o conteúdo à direita */
  width: auto;                   /* Largura automática */
}

/* Estilo específico para o painel de unidades */
.units-panel .parameter-value {
  width: 120px;                  /* Largura fixa para o painel de unidades */
  justify-content: flex-end;     /* Alinha o conteúdo à direita */
}

/* =========================================================================
   SELECTS DOS PAINÉIS - CONFIGURAÇÕES GERAIS
   ========================================================================= */
.panel select {
  color: var(--text-color);      /* Cor do texto (branco) */
  font-family: monospace;        /* Fonte monoespaçada */
  font-size: 1em;                /* Tamanho da fonte */
  background-color: transparent; /* Fundo transparente */
  border: none;                  /* Sem borda */
  padding: 0;                    /* Sem padding */
  margin: 0;                     /* Sem margem */
  cursor: pointer;               /* Cursor de ponteiro */
  -webkit-appearance: none;      /* Remove aparência padrão (Safari/Chrome) */
  -moz-appearance: none;         /* Remove aparência padrão (Firefox) */
  appearance: none;              /* Remove aparência padrão (Padrão) */
}

/* =========================================================================
   SELECTS DOS PAINÉIS - ESTADO DE FOCO
   ========================================================================= */
.panel select:focus {
  outline: none;                 /* Remove o contorno ao focar */
}

.panel select option {
  background-color: #000000;
  color: var(--text-color);
  font-family: monospace;
}

/* Responsividade */
@media (max-width: 768px) {
  .panel {
    position: relative;
    top: 0;
    left: 0;
    margin: 8px auto;
  }
}
