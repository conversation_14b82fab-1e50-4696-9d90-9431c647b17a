/* =========================================================================
   POSICIONAMENTO DOS PAINÉIS PRINCIPAIS
   ========================================================================= */
.parameters-panel {
  top: 20px;                               /* Distância do topo */
  left: 20px;                              /* Distância da esquerda */
}

.units-panel {
  top: calc(var(--parameters-height) + 40px); /* Posição abaixo do painel de parâmetros */
  left: 20px;                              /* Distância da esquerda */
  height: auto;                            /* Altura automática */
  z-index: 1002 !important;                /* Z-index elevado */
  overflow: visible !important;            /* Permite que dropdowns apareçam */
}

.elements-panel {
  top: 300px;                              /* Distância do topo */
  left: 20px;                              /* Distância da esquerda */
}

.history-panel {
  top: 300px;                              /* Distância do topo */
  right: 20px;                             /* Distância da direita */
  left: auto;                              /* Ignora posicionamento à esquerda */
}

.tools-panel {
  bottom: 20px;                            /* Distância da base */
  left: 20px;                              /* Distância da esquerda */
  top: auto;                               /* Ignora posicionamento do topo */
}

.settings-panel {
  bottom: 20px;                            /* Distância da base */
  right: 20px;                             /* Distância da direita */
  left: auto;                              /* Ignora posicionamento à esquerda */
  top: auto;                               /* Ignora posicionamento do topo */
}

/* =========================================================================
   CONFIGURAÇÕES ESPECÍFICAS PARA SELECTS DO PAINEL DE UNIDADES
   ========================================================================= */
.units-panel .parameter-value {
  justify-content: flex-end !important;    /* Alinhamento à direita */
  width: 120px !important;                 /* Largura fixa */
  display: flex !important;                /* Layout flexbox */
}

.units-select {
  width: 100% !important;                  /* Largura total */
  margin-left: auto !important;            /* Margem automática à esquerda */
}

.units-panel .parameter-value div[class*="container"] {
  padding: 0 !important;                   /* Sem padding */
  width: 100% !important;                  /* Largura total */
  justify-content: flex-end !important;    /* Alinhamento à direita */
}

.units-panel .parameter-value div[class*="control"] {
  width: 100% !important;                  /* Largura total */
  flex-direction: row-reverse !important;  /* Direção reversa */
  justify-content: flex-end !important;    /* Alinhamento à direita */
  display: flex !important;                /* Layout flexbox */
  min-height: 22px !important;             /* Altura mínima */
  height: 22px !important;                 /* Altura fixa */
  align-items: center !important;          /* Alinhamento vertical */
  align-content: center;                   /* Alinhamento de conteúdo */
}

.units-panel .parameter-value div[class*="valueContainer"] {
  padding: 0 !important;                   /* Sem padding */
  margin-right: 0 !important;              /* Sem margem à direita */
  justify-content: flex-end !important;    /* Alinhamento à direita */
  display: flex !important;                /* Layout flexbox */
  width: 100% !important;                  /* Largura total */
  height: 22px !important;                 /* Altura fixa */
  align-items: center !important;          /* Alinhamento vertical */
}

.units-panel .parameter-value div[class*="singleValue"] {
  padding-right: 0 !important;             /* Sem padding à direita */
  text-align: right !important;            /* Alinhamento de texto à direita */
  margin-right: 0 !important;              /* Sem margem à direita */
  display: flex !important;                /* Layout flexbox */
  justify-content: flex-end !important;    /* Alinhamento à direita */
  width: 100% !important;                  /* Largura total */
}

.units-panel .parameter-value div[class*="dropdownIndicator"] {
  padding-right: 0 !important;             /* Sem padding à direita */
}

/* =========================================================================
   CONFIGURAÇÕES DO MENU DROPDOWN
   ========================================================================= */
.units-panel div[class*="menu"],
body > div[class*="react-select__menu"],
body > div[id^="react-select"][id$="-listbox"] {
  z-index: 1002 !important;                /* Z-index elevado */
  position: absolute !important;           /* Posicionamento absoluto */
  width: 120px !important;                 /* Largura fixa */
  box-shadow: 0 4px 8px rgba(0, 0, 0, 0.5) !important; /* Sombra */
  background-color: #000000 !important;    /* Fundo preto */
  border: 1px solid rgba(255, 255, 255, 0.05) !important; /* Borda sutil */
  border-radius: 0 0 4px 4px !important;   /* Bordas arredondadas na base */
  top: auto !important;                    /* Posicionamento vertical automático */
  left: auto !important;                   /* Posicionamento horizontal automático */
}

/* =========================================================================
   RESPONSIVIDADE
   ========================================================================= */
@media (max-width: 768px) {
  .panel {
    position: relative;                    /* Posicionamento relativo */
    top: auto !important;                  /* Ignora posicionamento do topo */
    left: auto !important;                 /* Ignora posicionamento à esquerda */
    right: auto !important;                /* Ignora posicionamento à direita */
    bottom: auto !important;               /* Ignora posicionamento da base */
    margin: 10px auto;                     /* Margem automática */
    width: 90%;                            /* Largura relativa */
    max-width: 300px;                      /* Largura máxima */
  }
}
