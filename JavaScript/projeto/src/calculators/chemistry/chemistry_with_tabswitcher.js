import React, { useState, useEffect, useCallback, useRef } from 'react';
import '../../App.css';
import '../../styles/index.css';
import Squares from '../../Squares/Squares.jsx';
import Dock from '../../Dock/Dock.jsx';
import DebugMode from '../../components/debug/index.js';
// Importar os novos componentes TabSwitcher em vez dos Carousel
import SimpleTabSwitcher from './components/tabs/SimpleTabSwitcher.js';
import CompoundTabSwitcher from './components/compound/CompoundTabSwitcher.js';
import { Conversions, Conditions, GasLaws, ChemistryToolsOp } from './operations/index.js';
import { parseComposto, calcularMassaMolar, gramsToMoles, molesToGrams, molesToAtoms, atomsToMoles, convertMass, convertVolume, convertQuantity } from './utils/chemistryUtils.js';
import {
  VscHome,
  VscArchive,
  VscAccount,
  VscSettingsGear
} from 'react-icons/vsc';
import Select from 'react-select';
import { CustomMenu, CustomOption, CustomMenuList, CustomDropdownIndicator } from './components/Select.js';

function Chemistry() {
  // Estado para o composto químico
  const [composto, setComposto] = useState('');
  const [massaMolar, setMassaMolar] = useState(null);
  const [elementosInfo, setElementosInfo] = useState([]);

  // Estados para conversões
  const [fromUnit, setFromUnit] = useState({ value: 'g', label: 'grams (g)' });
  const [toUnit, setToUnit] = useState({ value: 'mol', label: 'moles (mol)' });
  const [inputValue, setInputValue] = useState('');
  const [result, setResult] = useState('');
  const [error, setError] = useState('');
  const [isLoading, setIsLoading] = useState(false);
  const [isRotating, setIsRotating] = useState(false);
  const [unitUpdateTrigger, setUnitUpdateTrigger] = useState(0);

  // Estados para condições de gás
  const [pressure, setPressure] = useState(1);
  const [temperature, setTemperature] = useState(273.15);
  const [pressureUnit, setPressureUnit] = useState({ value: 'atm', label: 'atm' });
  const [temperatureUnit, setTemperatureUnit] = useState({ value: 'K', label: 'K' });
  const [selectedPreset, setSelectedPreset] = useState('custom');

  // Estados para valores derivados
  const [massValue, setMassValue] = useState('');
  const [massUnit, setMassUnit] = useState('g');
  const [volumeValue, setVolumeValue] = useState('');
  const [volumeUnit, setVolumeUnit] = useState('L');
  const [molesValue, setMolesValue] = useState('');
  const [atomsValue, setAtomsValue] = useState('');

  // Referências para elementos DOM
  const parametersRef = useRef(null);

  // Opções para os selects de unidades
  const unitOptions = {
    mass: [
      { value: 'ag', label: 'attograms (ag)' },
      { value: 'fg', label: 'femtograms (fg)' },
      { value: 'pg', label: 'picograms (pg)' },
      { value: 'ng', label: 'nanograms (ng)' },
      { value: 'µg', label: 'micrograms (µg)' },
      { value: 'mg', label: 'milligrams (mg)' },
      { value: 'cg', label: 'centigrams (cg)' },
      { value: 'dg', label: 'decigrams (dg)' },
      { value: 'g', label: 'grams (g)' },
      { value: 'dag', label: 'decagrams (dag)' },
      { value: 'hg', label: 'hectograms (hg)' },
      { value: 'kg', label: 'kilograms (kg)' },
      { value: 'Mg', label: 'megagrams (Mg)' },
      { value: 'Gg', label: 'gigagrams (Gg)' },
      { value: 'Tg', label: 'teragrams (Tg)' },
      { value: 'lb', label: 'pounds (lb)' },
      { value: 'oz', label: 'ounces (oz)' },
      { value: 'u', label: 'atomic mass units (u)' }
    ],
    volume: [
      { value: 'aL', label: 'attoliters (aL)' },
      { value: 'fL', label: 'femtoliters (fL)' },
      { value: 'pL', label: 'picoliters (pL)' },
      { value: 'nL', label: 'nanoliters (nL)' },
      { value: 'µL', label: 'microliters (µL)' },
      { value: 'mL', label: 'milliliters (mL)' },
      { value: 'cL', label: 'centiliters (cL)' },
      { value: 'dL', label: 'deciliters (dL)' },
      { value: 'L', label: 'liters (L)' },
      { value: 'daL', label: 'decaliters (daL)' },
      { value: 'hL', label: 'hectoliters (hL)' },
      { value: 'kL', label: 'kiloliters (kL)' },
      { value: 'ML', label: 'megaliters (ML)' },
      { value: 'GL', label: 'gigaliters (GL)' },
      { value: 'TL', label: 'teraliters (TL)' },
      { value: 'in³', label: 'cubic inches (in³)' },
      { value: 'ft³', label: 'cubic feet (ft³)' },
      { value: 'yd³', label: 'cubic yards (yd³)' },
      { value: 'gal', label: 'gallons (gal)' }
    ],
    quantity: [
      { value: 'mol', label: 'moles (mol)' },
      { value: 'mmol', label: 'millimoles (mmol)' },
      { value: 'µmol', label: 'micromoles (µmol)' },
      { value: 'nmol', label: 'nanomoles (nmol)' },
      { value: 'pmol', label: 'picomoles (pmol)' },
      { value: 'atoms', label: 'atoms' },
      { value: 'molecules', label: 'molecules' }
    ],
    pressure: [
      { value: 'Pa', label: 'pascals (Pa)' },
      { value: 'kPa', label: 'kilopascals (kPa)' },
      { value: 'bar', label: 'bar' },
      { value: 'atm', label: 'atmospheres (atm)' },
      { value: 'torr', label: 'torr' },
      { value: 'mmHg', label: 'millimeters of mercury (mmHg)' },
      { value: 'psi', label: 'pounds per square inch (psi)' }
    ],
    temperature: [
      { value: 'K', label: 'kelvin (K)' },
      { value: 'C', label: 'celsius (°C)' },
      { value: 'F', label: 'fahrenheit (°F)' }
    ]
  };

  // Itens para o dock
  const items = [
    { icon: <VscHome />, label: 'Home', onClick: () => console.log('Home clicked') },
    { icon: <VscArchive />, label: 'Archive', onClick: () => console.log('Archive clicked') },
    { icon: <VscAccount />, label: 'Account', onClick: () => console.log('Account clicked') },
    { icon: <VscSettingsGear />, label: 'Settings', onClick: () => console.log('Settings clicked') }
  ];

  // Função para formatar valores numéricos
  const formatValue = (value) => {
    if (value === null || value === undefined || isNaN(value)) {
      return '-';
    }

    // Converter para número para garantir
    const num = Number(value);

    // Se for um número muito grande ou muito pequeno, usar notação científica
    if (Math.abs(num) < 0.001 || Math.abs(num) >= 10000) {
      // Formatar como Y × 10^X
      const exponent = Math.floor(Math.log10(Math.abs(num)));
      const mantissa = num / Math.pow(10, exponent);

      // Arredondar a mantissa para 4 casas decimais
      const roundedMantissa = Math.round(mantissa * 10000) / 10000;

      return `${roundedMantissa} × 10^${exponent}`;
    }

    // Caso contrário, formatar com até 4 casas decimais
    return num.toLocaleString(undefined, {
      maximumFractionDigits: 4,
      minimumFractionDigits: 0
    });
  };

  // Função para remover zeros à direita
  const removeTrailingZeros = (num) => {
    if (num === null || num === undefined) return '';
    return num.toString().replace(/\.?0+$/, '');
  };

  // Função para lidar com a mudança de entrada
  const handleInputChange = (e) => {
    setInputValue(e.target.value);
  };

  // Função para converter unidades
  const handleConvert = () => {
    setIsLoading(true);

    setTimeout(() => {
      try {
        let convertedValue = 0;
        const input = parseFloat(inputValue);

        if (isNaN(input)) {
          setError('Please enter a valid number');
          setResult('');
          setIsLoading(false);
          return;
        }

        // Determinar o tipo de conversão com base nas unidades
        const fromType = getUnitType(fromUnit.value);
        const toType = getUnitType(toUnit.value);

        if (fromType === 'mass' && toType === 'mass') {
          convertedValue = convertMass(input, fromUnit.value, toUnit.value);
        } else if (fromType === 'volume' && toType === 'volume') {
          convertedValue = convertVolume(input, fromUnit.value, toUnit.value);
        } else if (fromType === 'quantity' && toType === 'quantity') {
          convertedValue = convertQuantity(input, fromUnit.value, toUnit.value, massaMolar);
        } else if (fromType === 'mass' && toType === 'quantity') {
          if (toUnit.value === 'mol' || toUnit.value === 'mmol' || toUnit.value === 'µmol' || toUnit.value === 'nmol' || toUnit.value === 'pmol') {
            convertedValue = gramsToMoles(input, fromUnit.value, toUnit.value, massaMolar);
          } else {
            convertedValue = molesToAtoms(gramsToMoles(input, fromUnit.value, 'mol', massaMolar), toUnit.value);
          }
        } else if (fromType === 'quantity' && toType === 'mass') {
          if (fromUnit.value === 'mol' || fromUnit.value === 'mmol' || fromUnit.value === 'µmol' || fromUnit.value === 'nmol' || fromUnit.value === 'pmol') {
            convertedValue = molesToGrams(input, fromUnit.value, toUnit.value, massaMolar);
          } else {
            convertedValue = molesToGrams(atomsToMoles(input, fromUnit.value), 'mol', toUnit.value, massaMolar);
          }
        } else {
          setError('Incompatible unit types');
          setResult('');
          setIsLoading(false);
          return;
        }

        setResult(convertedValue.toString());
        setError('');
      } catch (err) {
        setError(err.message);
        setResult('');
      } finally {
        setIsLoading(false);
      }
    }, 500); // Simular um pequeno atraso para mostrar o loading
  };

  // Função para trocar as unidades
  const handleSwapUnits = () => {
    setIsRotating(true);

    // Trocar as unidades
    const tempUnit = fromUnit;
    setFromUnit(toUnit);
    setToUnit(tempUnit);

    // Atualizar o trigger para forçar a atualização dos valores
    setUnitUpdateTrigger(prev => prev + 1);

    // Desativar a rotação após a animação
    setTimeout(() => {
      setIsRotating(false);
    }, 500);
  };

  // Função para obter o tipo de unidade
  const getUnitType = (unit) => {
    if (unitOptions.mass.some(option => option.value === unit)) {
      return 'mass';
    } else if (unitOptions.volume.some(option => option.value === unit)) {
      return 'volume';
    } else if (unitOptions.quantity.some(option => option.value === unit)) {
      return 'quantity';
    } else if (unitOptions.pressure.some(option => option.value === unit)) {
      return 'pressure';
    } else if (unitOptions.temperature.some(option => option.value === unit)) {
      return 'temperature';
    }
    return null;
  };

  // Função para obter uma unidade específica
  const getSpecificUnit = (type, value) => {
    return unitOptions[type].find(option => option.value === value) || unitOptions[type][0];
  };

  // Função para lidar com a mudança de preset
  const handlePresetChange = (preset) => {
    setSelectedPreset(preset);

    switch (preset) {
      case 'stp':
        setPressure(1);
        setPressureUnit({ value: 'atm', label: 'atm' });
        setTemperature(273.15);
        setTemperatureUnit({ value: 'K', label: 'K' });
        break;
      case 'satp':
        setPressure(1);
        setPressureUnit({ value: 'atm', label: 'atm' });
        setTemperature(298.15);
        setTemperatureUnit({ value: 'K', label: 'K' });
        break;
      case 'custom':
        // Manter os valores atuais
        break;
      default:
        break;
    }
  };

  // Função para obter o texto do botão de preset
  const getPresetButtonText = (preset) => {
    switch (preset) {
      case 'stp':
        return 'STP (0°C, 1 atm)';
      case 'satp':
        return 'SATP (25°C, 1 atm)';
      case 'custom':
        return 'Custom';
      default:
        return preset;
    }
  };

  return (
    <div className="App">
      <div className="panel parameters-panel" ref={parametersRef}>
        {/* Painel de parâmetros... */}
      </div>

      <div className="main-content-wrapper">
        <Squares
          speed={0.3}
          squareSize={30}
          direction="diagonal"
          borderColor="#222"
          hoverFillColor="#222"
          className="background-squares"
        />
        <div className="content-container">
        <h1>Chemistry Calculator</h1>

        {/* Compound Block */}
        <div className="calculator-block calculo-container">
          <h2>Compound</h2>
          <div className="input-group">
            <input
              type="text"
              value={composto}
              onChange={(e) => setComposto(e.target.value)}
              placeholder="Enter chemical formula"
              className="chemical-input"
            />
          </div>

          {/* Usar o TabSwitcher em vez do Carousel */}
          {composto && (
            <CompoundTabSwitcher
              composto={composto}
              massaMolar={massaMolar}
              elementosInfo={elementosInfo}
              formatValue={formatValue}
              removeTrailingZeros={removeTrailingZeros}
              menuStyle={{ height: '50px' }}
              contentStyle={{ minHeight: '200px' }}
            />
          )}
        </div>

        {/* Usar o SimpleTabSwitcher em vez do SimpleCarousel */}
        <SimpleTabSwitcher
            menuStyle={{ height: '60px', marginBottom: '15px' }}
            contentStyle={{ minHeight: '300px', padding: '15px' }}
            slides={[
              {
                id: 'conversions',
                title: 'Unit Conversions',
                component: (
                  <Conversions
                    unitOptions={unitOptions}
                    fromUnit={fromUnit}
                    setFromUnit={setFromUnit}
                    toUnit={toUnit}
                    setToUnit={setToUnit}
                    inputValue={inputValue}
                    handleInputChange={handleInputChange}
                    result={result}
                    error={error}
                    isLoading={isLoading}
                    handleConvert={handleConvert}
                    handleSwapUnits={handleSwapUnits}
                    isRotating={isRotating}
                    setInputValue={setInputValue}
                    setUnitUpdateTrigger={setUnitUpdateTrigger}
                    getSpecificUnit={getSpecificUnit}
                  />
                )
              },
              {
                id: 'gasLaws',
                title: 'Gas Laws',
                component: (
                  <GasLaws
                    pressure={pressure}
                    volume={volumeValue}
                    temperature={temperature}
                    moles={molesValue}
                    pressureUnit={pressureUnit}
                    volumeUnit={volumeUnit}
                    temperatureUnit={temperatureUnit}
                    setPressure={setPressure}
                    setVolumeValue={setVolumeValue}
                    setTemperature={setTemperature}
                    setMolesValue={setMolesValue}
                    setPressureUnit={setPressureUnit}
                    setVolumeUnit={setVolumeUnit}
                    setTemperatureUnit={setTemperatureUnit}
                    formatValue={formatValue}
                  />
                )
              },
              {
                id: 'conditions',
                title: 'Gas Conditions',
                component: (
                  <Conditions
                    pressure={pressure}
                    setPressure={setPressure}
                    temperature={temperature}
                    setTemperature={setTemperature}
                    pressureUnit={pressureUnit}
                    temperatureUnit={temperatureUnit}
                    selectedPreset={selectedPreset}
                    setSelectedPreset={setSelectedPreset}
                    handlePresetChange={handlePresetChange}
                    getPresetButtonText={getPresetButtonText}
                  />
                )
              },
              {
                id: 'chemistryTools',
                title: 'Chemistry Tools',
                component: <ChemistryToolsOp />
              }
            ]}
          />

          <Dock
            items={items}
            panelHeight={70}
            baseItemSize={50}
            magnification={70}
          />

          {/* Componente de modo debug */}
          <DebugMode />
        </div>
      </div>
    </div>
  );
}

export default Chemistry;
