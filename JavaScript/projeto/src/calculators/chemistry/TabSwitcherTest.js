import React, { useState } from 'react';
import '../../App.css';
import '../../styles/index.css';
import TabSwitcher from './components/tabs/TabSwitcher';
import './TabSwitcherTest.css';

function TabSwitcherTest() {
  // Slides de exemplo simplificados
  const tabs = [
    {
      id: 'tab1',
      title: 'Tab 1',
      component: (
        <div className="test-slide">
          <h3>Tab 1 Content</h3>
          <p>This is the content of tab 1.</p>
        </div>
      )
    },
    {
      id: 'tab2',
      title: 'Tab 2',
      component: (
        <div className="test-slide">
          <h3>Tab 2 Content</h3>
          <p>This is the content of tab 2.</p>
        </div>
      )
    },
    {
      id: 'tab3',
      title: 'Tab 3',
      component: (
        <div className="test-slide">
          <h3>Tab 3 Content</h3>
          <p>This is the content of tab 3.</p>
        </div>
      )
    }
  ];

  return (
    <div className="App">
      <div className="main-content-wrapper">
        <div className="content-container">
          <h1>TabSwitcher Test</h1>

          <div className="test-section">
            <h2>Simple Tab Navigation</h2>
            <div className="test-container">
              <TabSwitcher tabs={tabs} />
            </div>
          </div>

          <div className="explanation">
            <h2>Sobre o TabSwitcher</h2>
            <p>O TabSwitcher é uma alternativa ao Carousel que:</p>
            <ul>
              <li>Usa um sistema de abas com animações de transição</li>
              <li>Mantém o efeito visual de navegação entre conteúdos</li>
              <li>Não utiliza um componente de carousel tradicional</li>
              <li>Oferece uma experiência mais direta e acessível</li>
            </ul>
          </div>
        </div>
      </div>
    </div>
  );
}

export default TabSwitcherTest;
