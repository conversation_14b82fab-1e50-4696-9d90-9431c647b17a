import React, { useState, useEffect } from 'react';
import './OrganicChemistry.css';
import MoleculeViewer3DAdvanced from './components/MoleculeViewer3DAdvanced';
import NomenclatureConverter from './components/NomenclatureConverter';
import OrganicReactions from './components/OrganicReactions';
import FunctionalGroups from './components/FunctionalGroups';

const OrganicChemistry = () => {
  const [activeTab, setActiveTab] = useState('viewer');
  const [currentMolecule, setCurrentMolecule] = useState('');
  const [moleculeData, setMoleculeData] = useState(null);

  // Dados de moléculas orgânicas comuns
  const commonMolecules = {
    // Alcanos
    'methane': { formula: 'CH4', smiles: 'C', iupac: 'methane' },
    'ethane': { formula: 'C2H6', smiles: 'CC', iupac: 'ethane' },
    'propane': { formula: 'C3H8', smiles: 'CCC', iupac: 'propane' },
    'butane': { formula: 'C4H10', smiles: 'CCCC', iupac: 'butane' },
    'pentane': { formula: 'C5H12', smiles: 'CCCCC', iupac: 'pentane' },
    
    // Alcenos
    'ethene': { formula: 'C2H4', smiles: 'C=C', iupac: 'ethene' },
    'propene': { formula: 'C3H6', smiles: 'C=CC', iupac: 'propene' },
    
    // Alcinos
    'ethyne': { formula: 'C2H2', smiles: 'C#C', iupac: 'ethyne' },
    
    // Aromáticos
    'benzene': { formula: 'C6H6', smiles: 'c1ccccc1', iupac: 'benzene' },
    'toluene': { formula: 'C7H8', smiles: 'Cc1ccccc1', iupac: 'methylbenzene' },
    
    // Álcoois
    'methanol': { formula: 'CH4O', smiles: 'CO', iupac: 'methanol' },
    'ethanol': { formula: 'C2H6O', smiles: 'CCO', iupac: 'ethanol' },
    
    // Ácidos carboxílicos
    'acetic acid': { formula: 'C2H4O2', smiles: 'CC(=O)O', iupac: 'ethanoic acid' },
    'formic acid': { formula: 'CH2O2', smiles: 'C(=O)O', iupac: 'methanoic acid' },
    
    // Cetonas
    'acetone': { formula: 'C3H6O', smiles: 'CC(=O)C', iupac: 'propanone' },
    
    // Aldeídos
    'formaldehyde': { formula: 'CH2O', smiles: 'C=O', iupac: 'methanal' },
    'acetaldehyde': { formula: 'C2H4O', smiles: 'CC=O', iupac: 'ethanal' }
  };

  const handleMoleculeInput = (input) => {
    setCurrentMolecule(input);
    
    // Tentar encontrar a molécula nos dados comuns
    const lowerInput = input.toLowerCase().trim();
    
    // Buscar por nome comum, IUPAC ou fórmula
    const found = Object.entries(commonMolecules).find(([key, data]) => 
      key === lowerInput || 
      data.iupac.toLowerCase() === lowerInput ||
      data.formula.toLowerCase() === lowerInput ||
      data.smiles.toLowerCase() === lowerInput
    );

    if (found) {
      setMoleculeData(found[1]);
    } else {
      // Se não encontrou, tentar interpretar como SMILES ou fórmula
      setMoleculeData({
        formula: input,
        smiles: input,
        iupac: input
      });
    }
  };

  const tabs = [
    {
      id: 'viewer',
      title: '3D Viewer',
      icon: '🧬',
      component: (
        <MoleculeViewer3DAdvanced 
          moleculeData={moleculeData}
          currentInput={currentMolecule}
        />
      )
    },
    {
      id: 'nomenclature',
      title: 'Nomenclature',
      icon: '📝',
      component: (
        <NomenclatureConverter 
          onMoleculeSelect={handleMoleculeInput}
          commonMolecules={commonMolecules}
        />
      )
    },
    {
      id: 'reactions',
      title: 'Reactions',
      icon: '⚗️',
      component: (
        <OrganicReactions 
          currentMolecule={moleculeData}
          onMoleculeSelect={handleMoleculeInput}
        />
      )
    },
    {
      id: 'functional-groups',
      title: 'Functional Groups',
      icon: '🔗',
      component: (
        <FunctionalGroups 
          onMoleculeSelect={handleMoleculeInput}
        />
      )
    }
  ];

  return (
    <div className="organic-chemistry-container">
      <div className="organic-header">
        <h2>Organic Chemistry</h2>
        <p>Explore organic molecules with 3D visualization, nomenclature, and reactions</p>
      </div>

      {/* Input para molécula */}
      <div className="molecule-input-section">
        <div className="input-group">
          <label htmlFor="molecule-input">Enter molecule (name, formula, or SMILES):</label>
          <input
            id="molecule-input"
            type="text"
            value={currentMolecule}
            onChange={(e) => handleMoleculeInput(e.target.value)}
            placeholder="e.g., methane, CH4, C, benzene, c1ccccc1"
            className="molecule-input"
          />
        </div>
        
        {moleculeData && (
          <div className="molecule-info">
            <div className="info-item">
              <strong>Formula:</strong> {moleculeData.formula}
            </div>
            <div className="info-item">
              <strong>SMILES:</strong> {moleculeData.smiles}
            </div>
            <div className="info-item">
              <strong>IUPAC:</strong> {moleculeData.iupac}
            </div>
          </div>
        )}
      </div>

      {/* Navegação por abas */}
      <div className="organic-tabs">
        {tabs.map(tab => (
          <button
            key={tab.id}
            className={`organic-tab ${activeTab === tab.id ? 'active' : ''}`}
            onClick={() => setActiveTab(tab.id)}
          >
            <span className="tab-icon">{tab.icon}</span>
            <span className="tab-title">{tab.title}</span>
          </button>
        ))}
      </div>

      {/* Conteúdo da aba ativa */}
      <div className="organic-content">
        {tabs.find(tab => tab.id === activeTab)?.component}
      </div>

      {/* Moléculas de exemplo */}
      <div className="example-molecules">
        <h3>Quick Examples:</h3>
        <div className="example-grid">
          {Object.entries(commonMolecules).slice(0, 8).map(([name, data]) => (
            <button
              key={name}
              className="example-molecule"
              onClick={() => handleMoleculeInput(name)}
              title={`${data.iupac} (${data.formula})`}
            >
              <div className="example-name">{name}</div>
              <div className="example-formula">{data.formula}</div>
            </button>
          ))}
        </div>
      </div>
    </div>
  );
};

export default OrganicChemistry;
