.functional-groups {
  padding: 20px;
  color: white;
  width: 100%;
  max-width: 100%;
  box-sizing: border-box;
}

.groups-header {
  text-align: center;
  margin-bottom: 25px;
}

.groups-header h3 {
  margin: 0 0 10px 0;
  color: #4CAF50;
  font-size: 1.5em;
}

.groups-header p {
  margin: 0;
  opacity: 0.8;
  font-size: 14px;
}

.groups-categories {
  margin-bottom: 30px;
}

.category-section {
  margin-bottom: 30px;
}

.category-title {
  margin: 0 0 15px 0;
  color: #4CAF50;
  font-size: 1.3em;
  text-align: center;
  padding: 10px;
  background: rgba(76, 175, 80, 0.1);
  border-radius: 6px;
  border: 1px solid rgba(76, 175, 80, 0.3);
}

.groups-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(280px, 1fr));
  gap: 15px;
  width: 100%;
  max-width: 100%;
  box-sizing: border-box;
  overflow: hidden;
}

.group-card {
  padding: 20px;
  background: rgba(255, 255, 255, 0.05);
  border: 2px solid rgba(255, 255, 255, 0.1);
  border-radius: 8px;
  cursor: pointer;
  transition: all 0.3s ease;
}

.group-card:hover {
  background: rgba(255, 255, 255, 0.1);
  border-color: rgba(76, 175, 80, 0.5);
  transform: translateY(-2px);
}

.group-card.selected {
  background: rgba(76, 175, 80, 0.2);
  border-color: #4CAF50;
  box-shadow: 0 4px 15px rgba(76, 175, 80, 0.3);
}

.group-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 15px;
}

.group-name {
  font-size: 1.1em;
  font-weight: 600;
  color: #4CAF50;
  margin: 0;
}

.group-suffix {
  font-size: 12px;
  padding: 4px 8px;
  background: rgba(76, 175, 80, 0.3);
  border-radius: 4px;
  font-family: 'Courier New', monospace;
}

.group-structure,
.group-formula {
  margin-bottom: 10px;
  font-size: 14px;
}

.group-structure strong,
.group-formula strong {
  color: #66BB6A;
}

.group-example {
  font-size: 13px;
  display: flex;
  align-items: center;
  gap: 8px;
}

.group-example strong {
  color: #66BB6A;
}

.example-btn {
  background: rgba(33, 150, 243, 0.3);
  border: 1px solid rgba(33, 150, 243, 0.5);
  border-radius: 4px;
  color: white;
  padding: 4px 8px;
  font-size: 12px;
  cursor: pointer;
  transition: all 0.3s ease;
}

.example-btn:hover {
  background: rgba(33, 150, 243, 0.5);
  transform: translateY(-1px);
}

.group-details {
  margin-bottom: 30px;
  padding: 20px;
  background: rgba(0, 0, 0, 0.3);
  border-radius: 8px;
  border: 1px solid rgba(255, 255, 255, 0.1);
  animation: fadeInUp 0.5s ease;
}

.group-details h4 {
  margin: 0 0 20px 0;
  color: #4CAF50;
  font-size: 1.3em;
  text-align: center;
}

.details-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
  gap: 20px;
  margin-bottom: 20px;
}

.properties-section,
.reactions-section {
  padding: 15px;
  background: rgba(255, 255, 255, 0.05);
  border-radius: 6px;
  border: 1px solid rgba(255, 255, 255, 0.1);
}

.properties-section h5,
.reactions-section h5 {
  margin: 0 0 10px 0;
  color: #66BB6A;
  font-size: 1.1em;
}

.properties-section ul,
.reactions-section ul {
  margin: 0;
  padding-left: 20px;
}

.properties-section li,
.reactions-section li {
  margin: 5px 0;
  font-size: 14px;
}

.structure-info {
  padding: 15px;
  background: rgba(33, 150, 243, 0.1);
  border-radius: 6px;
  border: 1px solid rgba(33, 150, 243, 0.3);
}

.structure-info h5 {
  margin: 0 0 10px 0;
  color: #2196F3;
  font-size: 1.1em;
}

.structure-info p {
  margin: 8px 0;
  font-size: 14px;
}

.structure-info strong {
  color: #64B5F6;
}

.priority-order {
  margin-bottom: 30px;
  padding: 20px;
  background: rgba(156, 39, 176, 0.1);
  border-radius: 8px;
  border: 1px solid rgba(156, 39, 176, 0.3);
}

.priority-order h4 {
  margin: 0 0 15px 0;
  color: #9C27B0;
  text-align: center;
}

.priority-order p {
  margin: 0 0 15px 0;
  text-align: center;
  opacity: 0.9;
}

.priority-list {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
  gap: 8px;
}

.priority-item {
  padding: 8px 12px;
  background: rgba(156, 39, 176, 0.2);
  border-radius: 4px;
  font-size: 13px;
  text-align: center;
  border: 1px solid rgba(156, 39, 176, 0.3);
}

.identification-tips {
  padding: 20px;
  background: rgba(255, 193, 7, 0.1);
  border-radius: 8px;
  border: 1px solid rgba(255, 193, 7, 0.3);
}

.identification-tips h4 {
  margin: 0 0 15px 0;
  color: #FFC107;
  text-align: center;
}

.tips-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
  gap: 15px;
}

.tip-card {
  padding: 15px;
  background: rgba(255, 193, 7, 0.1);
  border-radius: 6px;
  border: 1px solid rgba(255, 193, 7, 0.2);
}

.tip-card h5 {
  margin: 0 0 10px 0;
  color: #FFD54F;
  font-size: 1em;
}

.tip-card ul {
  margin: 0;
  padding-left: 20px;
}

.tip-card li {
  margin: 3px 0;
  font-size: 12px;
  opacity: 0.9;
}

/* Responsividade */
@media (max-width: 768px) {
  .functional-groups {
    padding: 15px;
  }
  
  .groups-grid {
    grid-template-columns: 1fr;
  }
  
  .group-header {
    flex-direction: column;
    align-items: flex-start;
    gap: 8px;
  }
  
  .details-grid {
    grid-template-columns: 1fr;
  }
  
  .priority-list {
    grid-template-columns: 1fr;
  }
  
  .tips-grid {
    grid-template-columns: 1fr;
  }
}

/* Animações */
@keyframes fadeInUp {
  from {
    opacity: 0;
    transform: translateY(20px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

/* Hover effects */
.priority-item:hover {
  background: rgba(156, 39, 176, 0.3);
  transform: translateY(-1px);
}

.tip-card:hover {
  background: rgba(255, 193, 7, 0.2);
  transform: translateY(-2px);
}
