import React, { useEffect, useRef, useState } from 'react';
import './MoleculeViewer3DAdvanced.css';
import { generatePrecise3DCoordinates } from '../../utils/molecularGeometry.js';
import { generateGeneric3DStructure } from '../../utils/genericMoleculeGenerator.js';
import { generateAdvancedMolecule, parseCommonNamesAndFormulas } from '../../utils/advancedMoleculeBuilder.js';
import { generateCleanMolecule } from '../../utils/cleanMoleculeBuilder.js';
import { generateRobustMolecule } from '../../utils/robustMoleculeBuilder.js';

// Supressão global e robusta do ResizeObserver
const suppressResizeObserverErrors = (() => {
  let isSuppressionActive = false;

  return () => {
    if (isSuppressionActive) return;
    isSuppressionActive = true;

    // Suprimir console.error
    const originalError = console.error;
    console.error = (...args) => {
      const message = String(args[0] || '');
      if (message.includes('ResizeObserver') ||
          message.includes('undelivered notifications') ||
          message.includes('loop completed') ||
          message.includes('loop limit exceeded')) {
        return;
      }
      originalError.apply(console, args);
    };

    // Suprimir window.onerror
    const originalWindowError = window.onerror;
    window.onerror = (message, source, lineno, colno, error) => {
      const msg = String(message || '');
      if (msg.includes('ResizeObserver') ||
          msg.includes('undelivered notifications') ||
          msg.includes('loop completed') ||
          msg.includes('loop limit exceeded')) {
        return true;
      }
      if (originalWindowError) {
        return originalWindowError(message, source, lineno, colno, error);
      }
      return false;
    };

    // Suprimir addEventListener de error
    const originalAddEventListener = window.addEventListener;
    window.addEventListener = function(type, listener, options) {
      if (type === 'error') {
        const wrappedListener = function(event) {
          const message = String(event.message || event.error?.message || '');
          if (message.includes('ResizeObserver') ||
              message.includes('undelivered notifications') ||
              message.includes('loop completed') ||
              message.includes('loop limit exceeded')) {
            event.preventDefault();
            event.stopPropagation();
            return;
          }
          if (typeof listener === 'function') {
            listener.call(this, event);
          }
        };
        return originalAddEventListener.call(this, type, wrappedListener, options);
      }
      return originalAddEventListener.call(this, type, listener, options);
    };
  };
})();

// Aplicar supressão imediatamente
suppressResizeObserverErrors();

const MoleculeViewer3DAdvanced = ({ moleculeData, currentInput }) => {
  const containerRef = useRef(null);
  const [viewer, setViewer] = useState(null);
  const [isLoading, setIsLoading] = useState(false);
  const [error, setError] = useState(null);
  const [viewerType, setViewerType] = useState('3dmol'); // '3dmol', 'babylon', 'openchemlib'
  const [renderStyle, setRenderStyle] = useState('ball-stick');

  // Estruturas 3D conhecidas expandidas
  const knownStructures = {
    'methane': {
      atoms: [
        { element: 'C', x: 0, y: 0, z: 0 },
        { element: 'H', x: 1.09, y: 0, z: 0 },
        { element: 'H', x: -0.36, y: 1.03, z: 0 },
        { element: 'H', x: -0.36, y: -0.51, z: 0.89 },
        { element: 'H', x: -0.36, y: -0.51, z: -0.89 }
      ],
      bonds: [
        { from: 0, to: 1 }, { from: 0, to: 2 }, { from: 0, to: 3 }, { from: 0, to: 4 }
      ]
    },
    'ethane': {
      atoms: [
        { element: 'C', x: -0.77, y: 0, z: 0 },
        { element: 'C', x: 0.77, y: 0, z: 0 },
        { element: 'H', x: -1.16, y: 1.03, z: 0 },
        { element: 'H', x: -1.16, y: -0.51, z: 0.89 },
        { element: 'H', x: -1.16, y: -0.51, z: -0.89 },
        { element: 'H', x: 1.16, y: 1.03, z: 0 },
        { element: 'H', x: 1.16, y: -0.51, z: 0.89 },
        { element: 'H', x: 1.16, y: -0.51, z: -0.89 }
      ],
      bonds: [
        { from: 0, to: 1 },
        { from: 0, to: 2 }, { from: 0, to: 3 }, { from: 0, to: 4 },
        { from: 1, to: 5 }, { from: 1, to: 6 }, { from: 1, to: 7 }
      ]
    },
    'ethene': {
      atoms: [
        { element: 'C', x: -0.67, y: 0, z: 0 },
        { element: 'C', x: 0.67, y: 0, z: 0 },
        { element: 'H', x: -1.23, y: 0.93, z: 0 },
        { element: 'H', x: -1.23, y: -0.93, z: 0 },
        { element: 'H', x: 1.23, y: 0.93, z: 0 },
        { element: 'H', x: 1.23, y: -0.93, z: 0 }
      ],
      bonds: [
        { from: 0, to: 1, order: 2 },
        { from: 0, to: 2 }, { from: 0, to: 3 },
        { from: 1, to: 4 }, { from: 1, to: 5 }
      ]
    },
    'benzene': {
      atoms: [
        { element: 'C', x: 1.40, y: 0, z: 0 },
        { element: 'C', x: 0.70, y: 1.21, z: 0 },
        { element: 'C', x: -0.70, y: 1.21, z: 0 },
        { element: 'C', x: -1.40, y: 0, z: 0 },
        { element: 'C', x: -0.70, y: -1.21, z: 0 },
        { element: 'C', x: 0.70, y: -1.21, z: 0 },
        { element: 'H', x: 2.49, y: 0, z: 0 },
        { element: 'H', x: 1.24, y: 2.15, z: 0 },
        { element: 'H', x: -1.24, y: 2.15, z: 0 },
        { element: 'H', x: -2.49, y: 0, z: 0 },
        { element: 'H', x: -1.24, y: -2.15, z: 0 },
        { element: 'H', x: 1.24, y: -2.15, z: 0 }
      ],
      bonds: [
        { from: 0, to: 1, order: 1.5 }, { from: 1, to: 2, order: 1.5 },
        { from: 2, to: 3, order: 1.5 }, { from: 3, to: 4, order: 1.5 },
        { from: 4, to: 5, order: 1.5 }, { from: 5, to: 0, order: 1.5 },
        { from: 0, to: 6 }, { from: 1, to: 7 }, { from: 2, to: 8 },
        { from: 3, to: 9 }, { from: 4, to: 10 }, { from: 5, to: 11 }
      ]
    },
    'ethanol': {
      atoms: [
        { element: 'C', x: -1.31, y: -0.25, z: 0 },
        { element: 'C', x: 0, y: 0.5, z: 0 },
        { element: 'O', x: 1.31, y: -0.25, z: 0 },
        { element: 'H', x: -1.31, y: -1.34, z: 0 },
        { element: 'H', x: -2.24, y: 0.25, z: 0 },
        { element: 'H', x: -1.31, y: -0.25, z: 1.09 },
        { element: 'H', x: 0, y: 1.59, z: 0 },
        { element: 'H', x: 0, y: 0.5, z: 1.09 },
        { element: 'H', x: 2.06, y: 0.25, z: 0 }
      ],
      bonds: [
        { from: 0, to: 1 }, { from: 1, to: 2 },
        { from: 0, to: 3 }, { from: 0, to: 4 }, { from: 0, to: 5 },
        { from: 1, to: 6 }, { from: 1, to: 7 }, { from: 2, to: 8 }
      ]
    },
    'propane': {
      atoms: [
        { element: 'C', x: -1.54, y: 0, z: 0 },
        { element: 'C', x: 0, y: 0, z: 0 },
        { element: 'C', x: 1.54, y: 0, z: 0 },
        { element: 'H', x: -1.88, y: 1.03, z: 0 },
        { element: 'H', x: -1.88, y: -0.51, z: 0.89 },
        { element: 'H', x: -1.88, y: -0.51, z: -0.89 },
        { element: 'H', x: 0, y: 1.03, z: 0 },
        { element: 'H', x: 0, y: -0.51, z: 0.89 },
        { element: 'H', x: 1.88, y: 1.03, z: 0 },
        { element: 'H', x: 1.88, y: -0.51, z: 0.89 },
        { element: 'H', x: 1.88, y: -0.51, z: -0.89 }
      ],
      bonds: [
        { from: 0, to: 1 }, { from: 1, to: 2 },
        { from: 0, to: 3 }, { from: 0, to: 4 }, { from: 0, to: 5 },
        { from: 1, to: 6 }, { from: 1, to: 7 },
        { from: 2, to: 8 }, { from: 2, to: 9 }, { from: 2, to: 10 }
      ]
    },
    'water': {
      atoms: [
        { element: 'O', x: 0, y: 0, z: 0 },
        { element: 'H', x: 0.76, y: 0.59, z: 0 },
        { element: 'H', x: -0.76, y: 0.59, z: 0 }
      ],
      bonds: [
        { from: 0, to: 1 }, { from: 0, to: 2 }
      ]
    },
    'ammonia': {
      atoms: [
        { element: 'N', x: 0, y: 0, z: 0 },
        { element: 'H', x: 0.94, y: 0.33, z: 0 },
        { element: 'H', x: -0.47, y: 0.33, z: 0.81 },
        { element: 'H', x: -0.47, y: 0.33, z: -0.81 }
      ],
      bonds: [
        { from: 0, to: 1 }, { from: 0, to: 2 }, { from: 0, to: 3 }
      ]
    }
  };

  // Função para gerar estrutura a partir de SMILES (simplificada)
  const generateFromSMILES = (smiles) => {
    // Primeiro, tentar usar o sistema de geometria molecular precisa
    try {
      const smilesFormulaMap = {
        'C': 'CH4',
        'CC': 'C2H6',
        'CCC': 'C3H8',
        'C=C': 'C2H4',
        'CCO': 'C2H6O',
        'c1ccccc1': 'C6H6',
        'O': 'H2O',
        'N': 'NH3'
      };

      const formula = smilesFormulaMap[smiles];
      if (formula) {
        const preciseStructure = generatePrecise3DCoordinates(formula, null);
        if (preciseStructure) {
          return preciseStructure;
        }
      }
    } catch (error) {
      console.warn('Erro ao gerar estrutura precisa a partir de SMILES:', error);
    }

    // Fallback: implementação básica para alguns SMILES comuns
    const smilesMap = {
      'C': knownStructures.methane,
      'CC': knownStructures.ethane,
      'CCC': knownStructures.propane,
      'C=C': knownStructures.ethene,
      'CCO': knownStructures.ethanol,
      'c1ccccc1': knownStructures.benzene,
      'O': knownStructures.water,
      'N': knownStructures.ammonia
    };

    return smilesMap[smiles] || null;
  };

  // Função para buscar estrutura por nome/alias
  const findStructureByName = (input) => {
    const normalizedInput = input.toLowerCase().trim();

    // Primeiro, tentar usar o sistema de geometria molecular precisa
    try {
      // Mapear nomes para fórmulas químicas
      const formulaMap = {
        'water': 'H2O',
        'h2o': 'H2O',
        'methane': 'CH4',
        'ch4': 'CH4',
        'ethane': 'C2H6',
        'c2h6': 'C2H6',
        'propane': 'C3H8',
        'c3h8': 'C3H8',
        'ethene': 'C2H4',
        'ethylene': 'C2H4',
        'c2h4': 'C2H4',
        'ethanol': 'C2H6O',
        'c2h5oh': 'C2H6O',
        'c2h6o': 'C2H6O',
        'benzene': 'C6H6',
        'c6h6': 'C6H6',
        'ammonia': 'NH3',
        'nh3': 'NH3',
        'h3n': 'NH3',
        'carbon dioxide': 'CO2',
        'co2': 'CO2',
        'hydrogen chloride': 'HCl',
        'hcl': 'HCl'
      };

      const formula = formulaMap[normalizedInput];
      if (formula) {
        // Tentar gerar estrutura precisa
        const preciseStructure = generatePrecise3DCoordinates(formula, null);
        if (preciseStructure) {
          return preciseStructure;
        }
      }
    } catch (error) {
      console.warn('Erro ao gerar estrutura precisa:', error);
    }

    // Fallback: usar aliases e estruturas conhecidas
    const aliases = {
      'ch4': 'methane',
      'c2h6': 'ethane',
      'c3h8': 'propane',
      'c2h4': 'ethene',
      'ethylene': 'ethene',
      'c6h6': 'benzene',
      'c2h5oh': 'ethanol',
      'c2h6o': 'ethanol',
      'h2o': 'water',
      'nh3': 'ammonia',
      'h3n': 'ammonia'
    };

    const structureName = aliases[normalizedInput] || normalizedInput;
    const knownStructure = knownStructures[structureName];

    if (knownStructure) {
      return knownStructure;
    }

    // Último fallback: sistema genérico
    try {
      const genericStructure = generateGeneric3DStructure(normalizedInput);
      if (genericStructure) {
        return genericStructure;
      }
    } catch (error) {
      console.warn('Erro no sistema genérico:', error);
    }

    return null;
  };

  // Função para converter estrutura para XYZ
  const structureToXYZ = (structure) => {
    if (!structure || !structure.atoms) return '';
    
    let xyz = `${structure.atoms.length}\n`;
    xyz += `Generated structure\n`;
    
    structure.atoms.forEach(atom => {
      xyz += `${atom.element} ${atom.x.toFixed(6)} ${atom.y.toFixed(6)} ${atom.z.toFixed(6)}\n`;
    });
    
    return xyz;
  };

  // Função para inicializar o viewer 3DMol
  const init3DMolViewer = async (structure) => {
    try {
      const $3Dmol = await import('3dmol');

      if (containerRef.current) {
        // Limpar container com timeout para evitar conflitos
        setTimeout(() => {
          if (containerRef.current) {
            containerRef.current.innerHTML = '';
          }
        }, 0);

        const config = {
          backgroundColor: 'black',
          antialias: true,
          alpha: true
        };

        // Criar viewer com timeout para evitar ResizeObserver
        await new Promise(resolve => setTimeout(resolve, 100));

        const newViewer = $3Dmol.createViewer(containerRef.current, config);

        if (structure) {
          const xyzData = structureToXYZ(structure);

          // Adicionar modelo com timeout
          await new Promise(resolve => {
            newViewer.addModel(xyzData, 'xyz');
            setTimeout(resolve, 50);
          });

          // Aplicar estilo baseado na seleção
          const styleConfig = getStyleConfig(renderStyle);
          newViewer.setStyle({}, styleConfig);

          // Adicionar ligações múltiplas se existirem
          if (structure.bonds) {
            structure.bonds.forEach((bond, index) => {
              if (bond.order && bond.order > 1) {
                console.log(`🔗 Adicionando ligação ${bond.order}x entre átomos ${bond.from} e ${bond.to}`);

                // Para ligações duplas e triplas, adicionar cilindros extras
                const atom1 = structure.atoms[bond.from];
                const atom2 = structure.atoms[bond.to];

                if (atom1 && atom2) {
                  // Calcular vetor da ligação
                  const dx = atom2.x - atom1.x;
                  const dy = atom2.y - atom1.y;
                  const dz = atom2.z - atom1.z;
                  const length = Math.sqrt(dx*dx + dy*dy + dz*dz);

                  // Vetores perpendiculares para deslocamento
                  const perpX1 = -dy / length * 0.1;
                  const perpY1 = dx / length * 0.1;
                  const perpZ1 = 0;

                  const perpX2 = 0;
                  const perpY2 = 0;
                  const perpZ2 = 0.1;

                  // Para ligação dupla (order = 2)
                  if (bond.order === 2) {
                    try {
                      newViewer.addCylinder({
                        start: {
                          x: atom1.x + perpX1 * 0.8,
                          y: atom1.y + perpY1 * 0.8,
                          z: atom1.z + perpZ1 * 0.8
                        },
                        end: {
                          x: atom2.x + perpX1 * 0.8,
                          y: atom2.y + perpY1 * 0.8,
                          z: atom2.z + perpZ1 * 0.8
                        },
                        radius: 0.08,
                        color: 'yellow'
                      });
                    } catch (cylinderError) {
                      console.warn(`⚠️ Erro ao adicionar ligação dupla ${index}:`, cylinderError);
                    }
                  }

                  // Para ligação tripla (order = 3)
                  if (bond.order === 3) {
                    try {
                      // Segunda ligação
                      newViewer.addCylinder({
                        start: {
                          x: atom1.x + perpX1 * 0.6,
                          y: atom1.y + perpY1 * 0.6,
                          z: atom1.z + perpZ1 * 0.6
                        },
                        end: {
                          x: atom2.x + perpX1 * 0.6,
                          y: atom2.y + perpY1 * 0.6,
                          z: atom2.z + perpZ1 * 0.6
                        },
                        radius: 0.06,
                        color: 'red'
                      });

                      // Terceira ligação
                      newViewer.addCylinder({
                        start: {
                          x: atom1.x + perpX2 * 0.6,
                          y: atom1.y + perpY2 * 0.6,
                          z: atom1.z + perpZ2 * 0.6
                        },
                        end: {
                          x: atom2.x + perpX2 * 0.6,
                          y: atom2.y + perpY2 * 0.6,
                          z: atom2.z + perpZ2 * 0.6
                        },
                        radius: 0.06,
                        color: 'red'
                      });
                    } catch (cylinderError) {
                      console.warn(`⚠️ Erro ao adicionar ligação tripla ${index}:`, cylinderError);
                    }
                  }
                }
              }
            });
          }

          // Adicionar labels centralizados nas esferas com timeout
          await new Promise(resolve => {
            try {
              const atoms = newViewer.getModel().selectedAtoms({});
              atoms.forEach((atom, index) => {
                try {
                  // Calcular posição centralizada baseada no tamanho da esfera
                  const sphereScale = styleConfig.sphere?.scale || 0.3;
                  const labelOffset = sphereScale * 0.1; // Pequeno offset para centralização

                  newViewer.addLabel(atom.elem, {
                    position: {
                      x: atom.x,
                      y: atom.y,
                      z: atom.z + labelOffset
                    },
                    backgroundColor: 'rgba(0,0,0,0.8)',
                    fontColor: 'white',
                    fontSize: 12,
                    showBackground: true,
                    alignment: 'center',
                    inFront: true
                  });
                } catch (labelError) {
                  console.warn(`⚠️ Erro ao adicionar label para átomo ${index}:`, labelError);
                }
              });
            } catch (labelError) {
              // Ignorar erros de labels
            }
            setTimeout(resolve, 50);
          });

          // Renderizar com timeout
          await new Promise(resolve => {
            newViewer.zoomTo();
            newViewer.render();
            setTimeout(resolve, 100);
          });

          setViewer(newViewer);
        }
      }
    } catch (err) {
      console.error('Error with 3DMol:', err);
      throw err;
    }
  };

  // Função para obter configuração de estilo
  const getStyleConfig = (style) => {
    switch (style) {
      case 'ball-stick':
        return {
          stick: { radius: 0.1, color: 'white' },
          sphere: {
            scale: 0.35,
            colorscheme: 'Jmol' // Cores padrão dos elementos
          }
        };
      case 'space-fill':
        return {
          sphere: {
            scale: 0.8,
            colorscheme: 'Jmol'
          }
        };
      case 'wireframe':
        return {
          stick: {
            radius: 0.05,
            color: 'white'
          }
        };
      case 'cartoon':
        return { cartoon: { color: 'spectrum' } };
      default:
        return {
          stick: { radius: 0.1, color: 'white' },
          sphere: {
            scale: 0.35,
            colorscheme: 'Jmol'
          }
        };
    }
  };

  useEffect(() => {
    const initViewer = async () => {
      if (!moleculeData && !currentInput) return;

      setIsLoading(true);
      setError(null);

      try {
        let structure = null;

        // Tentar obter estrutura conhecida
        // SISTEMA ROBUSTO - PRIORIDADE ABSOLUTA
        const inputToTry = currentInput || moleculeData?.iupac || moleculeData?.formula;

        console.log(`🔍 SISTEMA ROBUSTO: Tentando gerar estrutura para: "${inputToTry}"`);

        // FORÇAR uso do sistema robusto (com validação de valência)
        if (inputToTry) {
          structure = generateRobustMolecule(inputToTry);

          if (structure) {
            console.log(`✅ SISTEMA ROBUSTO FUNCIONOU! Gerou estrutura para: ${inputToTry}`);
            console.log(`📋 Átomos: ${structure.atoms.length}, Ligações: ${structure.bonds.length}`);
            console.log(`🔬 Validação:`, structure.validation);
          } else {
            console.log(`❌ Sistema robusto falhou para: ${inputToTry}`);

            // Fallback para sistema limpo
            structure = generateCleanMolecule(inputToTry);
            if (structure) {
              console.log('✅ Usando sistema limpo como fallback');
            } else {
              // APENAS como último recurso, tentar sistema antigo
              if (moleculeData?.smiles) {
                structure = generateFromSMILES(moleculeData.smiles);
                console.log('Usando SMILES como fallback');
              }

              if (!structure) {
                structure = findStructureByName(inputToTry);
                console.log('Usando findStructureByName como fallback');
              }
            }
          }
        }

        if (structure) {
          if (viewerType === '3dmol') {
            await init3DMolViewer(structure);
          }
        } else {
          setError(`No 3D structure available for: ${currentInput || 'unknown molecule'}`);
        }

      } catch (err) {
        console.error('Error initializing viewer:', err);
        setError('Failed to load 3D viewer');
      }

      setIsLoading(false);
    };

    initViewer();
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, [moleculeData, currentInput, viewerType, renderStyle]);

  // Funções de controle
  const resetView = () => {
    if (viewer) {
      viewer.zoomTo();
      viewer.render();
    }
  };

  const changeStyle = (newStyle) => {
    setRenderStyle(newStyle);
    if (viewer) {
      const styleConfig = getStyleConfig(newStyle);
      viewer.setStyle({}, styleConfig);
      viewer.render();
    }
  };

  return (
    <div className="molecule-viewer-advanced">
      <div className="viewer-controls">
        <div className="control-group">
          <label>Render Style:</label>
          <select 
            value={renderStyle} 
            onChange={(e) => changeStyle(e.target.value)}
            className="style-select"
          >
            <option value="ball-stick">Ball & Stick</option>
            <option value="space-fill">Space Fill</option>
            <option value="wireframe">Wireframe</option>
          </select>
        </div>
        
        <div className="control-group">
          <label>Viewer:</label>
          <select 
            value={viewerType} 
            onChange={(e) => setViewerType(e.target.value)}
            className="viewer-select"
          >
            <option value="3dmol">3DMol.js</option>
          </select>
        </div>
        
        <button onClick={resetView} className="control-btn">
          🔄 Reset View
        </button>
      </div>

      {isLoading && (
        <div className="loading-state">
          <div className="spinner"></div>
          <p>Loading 3D structure...</p>
        </div>
      )}

      {error && (
        <div className="error-state">
          <p>{error}</p>
          <p className="error-hint">
            Try entering a common molecule name like: methane, ethane, benzene, ethanol
          </p>
        </div>
      )}

      <div 
        ref={containerRef} 
        className="viewer-container"
        style={{ 
          display: isLoading || error ? 'none' : 'block'
        }}
      />

      <div className="viewer-info">
        <p>💡 Click and drag to rotate • Scroll to zoom • Right-click to pan</p>
        {moleculeData && (
          <p>Viewing: <strong>{moleculeData.iupac}</strong> ({moleculeData.formula})</p>
        )}
      </div>
    </div>
  );
};

export default MoleculeViewer3DAdvanced;
