import React, { useState } from 'react';
import './FunctionalGroups.css';

const FunctionalGroups = ({ onMoleculeSelect }) => {
  const [selectedGroup, setSelectedGroup] = useState(null);

  // Grupos funcionais organizados por categoria
  const functionalGroups = {
    hydrocarbons: {
      name: 'Hydrocarbons',
      groups: [
        {
          name: 'Alkane',
          structure: 'C-C',
          formula: 'CₙH₂ₙ₊₂',
          suffix: '-ane',
          example: 'methane (CH₄)',
          properties: ['Saturated', 'Single bonds only', 'Relatively unreactive'],
          reactions: ['Combustion', 'Halogenation']
        },
        {
          name: 'Alkene',
          structure: 'C=C',
          formula: 'CₙH₂ₙ',
          suffix: '-ene',
          example: 'ethene (C₂H₄)',
          properties: ['Unsaturated', 'Contains C=C', 'More reactive than alkanes'],
          reactions: ['Addition', 'Polymerization', 'Oxidation']
        },
        {
          name: 'Alkyne',
          structure: 'C≡C',
          formula: 'CₙH₂ₙ₋₂',
          suffix: '-yne',
          example: 'ethyne (C₂H₂)',
          properties: ['Unsaturated', 'Contains C≡C', 'Highly reactive'],
          reactions: ['Addition', 'Hydration', 'Halogenation']
        },
        {
          name: 'Aromatic',
          structure: 'Benzene ring',
          formula: 'C₆H₆ (benzene)',
          suffix: 'benzene',
          example: 'benzene (C₆H₆)',
          properties: ['Delocalized electrons', 'Stable ring', 'Planar structure'],
          reactions: ['Electrophilic substitution', 'Nitration', 'Halogenation']
        }
      ]
    },
    oxygenContaining: {
      name: 'Oxygen-Containing',
      groups: [
        {
          name: 'Alcohol',
          structure: 'R-OH',
          formula: 'ROH',
          suffix: '-ol',
          example: 'ethanol (C₂H₅OH)',
          properties: ['Polar', 'Hydrogen bonding', 'Higher boiling points'],
          reactions: ['Oxidation', 'Dehydration', 'Esterification']
        },
        {
          name: 'Aldehyde',
          structure: 'R-CHO',
          formula: 'RCHO',
          suffix: '-al',
          example: 'ethanal (CH₃CHO)',
          properties: ['Polar carbonyl', 'Reactive C=O', 'Oxidizable'],
          reactions: ['Oxidation', 'Reduction', 'Nucleophilic addition']
        },
        {
          name: 'Ketone',
          structure: 'R-CO-R',
          formula: 'RCOR',
          suffix: '-one',
          example: 'propanone (CH₃COCH₃)',
          properties: ['Polar carbonyl', 'Less reactive than aldehydes'],
          reactions: ['Reduction', 'Nucleophilic addition', 'Condensation']
        },
        {
          name: 'Carboxylic Acid',
          structure: 'R-COOH',
          formula: 'RCOOH',
          suffix: '-oic acid',
          example: 'ethanoic acid (CH₃COOH)',
          properties: ['Acidic', 'Hydrogen bonding', 'Dimer formation'],
          reactions: ['Neutralization', 'Esterification', 'Reduction']
        },
        {
          name: 'Ester',
          structure: 'R-COO-R',
          formula: 'RCOOR',
          suffix: '-oate',
          example: 'methyl ethanoate (CH₃COOCH₃)',
          properties: ['Pleasant odors', 'Lower boiling points than acids'],
          reactions: ['Hydrolysis', 'Saponification', 'Transesterification']
        },
        {
          name: 'Ether',
          structure: 'R-O-R',
          formula: 'ROR',
          suffix: 'ether',
          example: 'diethyl ether (C₂H₅OC₂H₅)',
          properties: ['Relatively unreactive', 'Good solvents', 'Low boiling points'],
          reactions: ['Cleavage with HI', 'Peroxide formation']
        }
      ]
    },
    nitrogenContaining: {
      name: 'Nitrogen-Containing',
      groups: [
        {
          name: 'Amine',
          structure: 'R-NH₂, R₂NH, R₃N',
          formula: 'RNH₂',
          suffix: '-amine',
          example: 'methylamine (CH₃NH₂)',
          properties: ['Basic', 'Hydrogen bonding', 'Fishy odors'],
          reactions: ['Acid-base', 'Alkylation', 'Acylation']
        },
        {
          name: 'Amide',
          structure: 'R-CONH₂',
          formula: 'RCONH₂',
          suffix: '-amide',
          example: 'ethanamide (CH₃CONH₂)',
          properties: ['Resonance stabilized', 'High boiling points'],
          reactions: ['Hydrolysis', 'Reduction', 'Dehydration']
        },
        {
          name: 'Nitrile',
          structure: 'R-CN',
          formula: 'RCN',
          suffix: '-nitrile',
          example: 'ethanenitrile (CH₃CN)',
          properties: ['Triple bond', 'Polar', 'Good solvents'],
          reactions: ['Hydrolysis', 'Reduction', 'Addition']
        }
      ]
    }
  };

  const selectGroup = (group) => {
    setSelectedGroup(group);
  };

  const viewExample = (example) => {
    const moleculeName = example.split('(')[0].trim();
    onMoleculeSelect(moleculeName);
  };

  return (
    <div className="functional-groups">
      <div className="groups-header">
        <h3>Functional Groups</h3>
        <p>Explore organic functional groups, their properties, and reactions</p>
      </div>

      <div className="groups-categories">
        {Object.entries(functionalGroups).map(([categoryKey, category]) => (
          <div key={categoryKey} className="category-section">
            <h4 className="category-title">{category.name}</h4>
            
            <div className="groups-grid">
              {category.groups.map((group, index) => (
                <div 
                  key={index}
                  className={`group-card ${selectedGroup?.name === group.name ? 'selected' : ''}`}
                  onClick={() => selectGroup(group)}
                >
                  <div className="group-header">
                    <h5 className="group-name">{group.name}</h5>
                    <div className="group-suffix">{group.suffix}</div>
                  </div>
                  
                  <div className="group-structure">
                    <strong>Structure:</strong> {group.structure}
                  </div>
                  
                  <div className="group-formula">
                    <strong>General Formula:</strong> {group.formula}
                  </div>
                  
                  <div className="group-example">
                    <strong>Example:</strong> 
                    <button 
                      className="example-btn"
                      onClick={(e) => {
                        e.stopPropagation();
                        viewExample(group.example);
                      }}
                    >
                      {group.example}
                    </button>
                  </div>
                </div>
              ))}
            </div>
          </div>
        ))}
      </div>

      {selectedGroup && (
        <div className="group-details">
          <h4>{selectedGroup.name} Details</h4>
          
          <div className="details-grid">
            <div className="properties-section">
              <h5>Properties</h5>
              <ul>
                {selectedGroup.properties.map((property, index) => (
                  <li key={index}>{property}</li>
                ))}
              </ul>
            </div>
            
            <div className="reactions-section">
              <h5>Common Reactions</h5>
              <ul>
                {selectedGroup.reactions.map((reaction, index) => (
                  <li key={index}>{reaction}</li>
                ))}
              </ul>
            </div>
          </div>
          
          <div className="structure-info">
            <h5>Structure Information</h5>
            <p><strong>General Structure:</strong> {selectedGroup.structure}</p>
            <p><strong>General Formula:</strong> {selectedGroup.formula}</p>
            <p><strong>IUPAC Suffix:</strong> {selectedGroup.suffix}</p>
            <p><strong>Example:</strong> {selectedGroup.example}</p>
          </div>
        </div>
      )}

      <div className="priority-order">
        <h4>📋 Functional Group Priority Order</h4>
        <p>When multiple functional groups are present, IUPAC naming follows this priority:</p>
        <div className="priority-list">
          <div className="priority-item">1. Carboxylic Acid (-oic acid)</div>
          <div className="priority-item">2. Ester (-oate)</div>
          <div className="priority-item">3. Amide (-amide)</div>
          <div className="priority-item">4. Nitrile (-nitrile)</div>
          <div className="priority-item">5. Aldehyde (-al)</div>
          <div className="priority-item">6. Ketone (-one)</div>
          <div className="priority-item">7. Alcohol (-ol)</div>
          <div className="priority-item">8. Amine (-amine)</div>
          <div className="priority-item">9. Ether (ether)</div>
          <div className="priority-item">10. Alkene (-ene)</div>
          <div className="priority-item">11. Alkyne (-yne)</div>
          <div className="priority-item">12. Alkane (-ane)</div>
        </div>
      </div>

      <div className="identification-tips">
        <h4>🔍 Identification Tips</h4>
        <div className="tips-grid">
          <div className="tip-card">
            <h5>IR Spectroscopy</h5>
            <ul>
              <li>O-H stretch: 3200-3600 cm⁻¹</li>
              <li>C=O stretch: 1700-1750 cm⁻¹</li>
              <li>C=C stretch: 1600-1700 cm⁻¹</li>
              <li>N-H stretch: 3300-3500 cm⁻¹</li>
            </ul>
          </div>
          <div className="tip-card">
            <h5>Chemical Tests</h5>
            <ul>
              <li>Lucas test: Alcohols</li>
              <li>Tollens' test: Aldehydes</li>
              <li>Iodoform test: Methyl ketones</li>
              <li>Litmus test: Carboxylic acids</li>
            </ul>
          </div>
        </div>
      </div>
    </div>
  );
};

export default FunctionalGroups;
