import React, { useState } from 'react';
import './NomenclatureConverter.css';

const NomenclatureConverter = ({ onMoleculeSelect, commonMolecules }) => {
  const [inputType, setInputType] = useState('name'); // 'name', 'formula', 'smiles'
  const [inputValue, setInputValue] = useState('');
  const [results, setResults] = useState(null);
  const [searchResults, setSearchResults] = useState([]);

  // Regras de nomenclatura IUPAC básicas
  const nomenclatureRules = {
    alkanes: {
      prefixes: ['meth', 'eth', 'prop', 'but', 'pent', 'hex', 'hept', 'oct', 'non', 'dec'],
      suffix: 'ane'
    },
    alkenes: {
      prefixes: ['eth', 'prop', 'but', 'pent', 'hex', 'hept', 'oct', 'non', 'dec'],
      suffix: 'ene'
    },
    alkynes: {
      prefixes: ['eth', 'prop', 'but', 'pent', 'hex', 'hept', 'oct', 'non', 'dec'],
      suffix: 'yne'
    },
    alcohols: {
      prefixes: ['meth', 'eth', 'prop', 'but', 'pent', 'hex', 'hept', 'oct', 'non', 'dec'],
      suffix: 'anol'
    },
    carboxylicAcids: {
      prefixes: ['meth', 'eth', 'prop', 'but', 'pent', 'hex', 'hept', 'oct', 'non', 'dec'],
      suffix: 'anoic acid'
    }
  };

  // Grupos funcionais comuns
  const functionalGroups = [
    { name: 'Alcohol', suffix: '-ol', example: 'ethanol' },
    { name: 'Aldehyde', suffix: '-al', example: 'ethanal' },
    { name: 'Ketone', suffix: '-one', example: 'propanone' },
    { name: 'Carboxylic Acid', suffix: '-oic acid', example: 'ethanoic acid' },
    { name: 'Ester', suffix: '-oate', example: 'methyl ethanoate' },
    { name: 'Ether', suffix: 'ether', example: 'diethyl ether' },
    { name: 'Amine', suffix: '-amine', example: 'methylamine' },
    { name: 'Amide', suffix: '-amide', example: 'ethanamide' }
  ];

  const handleSearch = () => {
    if (!inputValue.trim()) return;

    const searchTerm = inputValue.toLowerCase().trim();
    let foundMolecule = null;

    // Buscar nos dados comuns
    const found = Object.entries(commonMolecules).find(([key, data]) => {
      switch (inputType) {
        case 'name':
          return key === searchTerm || data.iupac.toLowerCase() === searchTerm;
        case 'formula':
          return data.formula.toLowerCase() === searchTerm;
        case 'smiles':
          return data.smiles.toLowerCase() === searchTerm;
        default:
          return false;
      }
    });

    if (found) {
      foundMolecule = found[1];
      setResults({
        found: true,
        commonName: found[0],
        iupacName: foundMolecule.iupac,
        formula: foundMolecule.formula,
        smiles: foundMolecule.smiles,
        type: determineCompoundType(foundMolecule)
      });
    } else {
      // Tentar análise básica
      const analysis = analyzeInput(inputValue, inputType);
      setResults({
        found: false,
        input: inputValue,
        inputType: inputType,
        analysis: analysis
      });
    }

    // Buscar moléculas similares
    const similar = findSimilarMolecules(searchTerm);
    setSearchResults(similar);
  };

  const determineCompoundType = (molecule) => {
    const formula = molecule.formula;
    const smiles = molecule.smiles;
    
    if (smiles.includes('=')) return 'Alkene';
    if (smiles.includes('#')) return 'Alkyne';
    if (smiles.includes('O') && smiles.includes('C(=O)O')) return 'Carboxylic Acid';
    if (smiles.includes('O') && smiles.includes('C=O')) return 'Ketone/Aldehyde';
    if (smiles.includes('O') && !smiles.includes('=')) return 'Alcohol/Ether';
    if (smiles.includes('c')) return 'Aromatic';
    if (formula.match(/^C\d*H\d*$/)) return 'Alkane';
    
    return 'Organic Compound';
  };

  const analyzeInput = (input, type) => {
    const analysis = {
      possibleType: 'Unknown',
      suggestions: [],
      rules: []
    };

    if (type === 'name') {
      // Analisar nome IUPAC
      const lowerInput = input.toLowerCase();
      
      if (lowerInput.endsWith('ane')) {
        analysis.possibleType = 'Alkane';
        analysis.rules.push('Ends with -ane: saturated hydrocarbon');
      } else if (lowerInput.endsWith('ene')) {
        analysis.possibleType = 'Alkene';
        analysis.rules.push('Ends with -ene: unsaturated hydrocarbon with C=C');
      } else if (lowerInput.endsWith('yne')) {
        analysis.possibleType = 'Alkyne';
        analysis.rules.push('Ends with -yne: unsaturated hydrocarbon with C≡C');
      } else if (lowerInput.endsWith('ol')) {
        analysis.possibleType = 'Alcohol';
        analysis.rules.push('Ends with -ol: contains OH group');
      } else if (lowerInput.endsWith('oic acid')) {
        analysis.possibleType = 'Carboxylic Acid';
        analysis.rules.push('Ends with -oic acid: contains COOH group');
      }
    }

    return analysis;
  };

  const findSimilarMolecules = (searchTerm) => {
    return Object.entries(commonMolecules)
      .filter(([key, data]) => 
        key.includes(searchTerm) || 
        data.iupac.toLowerCase().includes(searchTerm) ||
        data.formula.toLowerCase().includes(searchTerm)
      )
      .slice(0, 5)
      .map(([key, data]) => ({ name: key, ...data }));
  };

  const selectMolecule = (molecule) => {
    onMoleculeSelect(molecule.name || molecule.iupac);
    setInputValue(molecule.name || molecule.iupac);
  };

  return (
    <div className="nomenclature-converter">
      <div className="converter-header">
        <h3>Nomenclature Converter</h3>
        <p>Convert between common names, IUPAC names, formulas, and SMILES</p>
      </div>

      <div className="search-section">
        <div className="input-controls">
          <div className="input-type-selector">
            <label>Search by:</label>
            <select 
              value={inputType} 
              onChange={(e) => setInputType(e.target.value)}
              className="type-select"
            >
              <option value="name">Name</option>
              <option value="formula">Formula</option>
              <option value="smiles">SMILES</option>
            </select>
          </div>
          
          <div className="search-input-group">
            <input
              type="text"
              value={inputValue}
              onChange={(e) => setInputValue(e.target.value)}
              placeholder={`Enter ${inputType}...`}
              className="search-input"
              onKeyPress={(e) => e.key === 'Enter' && handleSearch()}
            />
            <button onClick={handleSearch} className="search-btn">
              🔍 Search
            </button>
          </div>
        </div>
      </div>

      {results && (
        <div className="results-section">
          {results.found ? (
            <div className="molecule-result">
              <h4>✅ Molecule Found</h4>
              <div className="result-grid">
                <div className="result-item">
                  <strong>Common Name:</strong> {results.commonName}
                </div>
                <div className="result-item">
                  <strong>IUPAC Name:</strong> {results.iupacName}
                </div>
                <div className="result-item">
                  <strong>Formula:</strong> {results.formula}
                </div>
                <div className="result-item">
                  <strong>SMILES:</strong> {results.smiles}
                </div>
                <div className="result-item">
                  <strong>Type:</strong> {results.type}
                </div>
              </div>
              <button 
                onClick={() => selectMolecule({ name: results.commonName })}
                className="view-3d-btn"
              >
                🧬 View in 3D
              </button>
            </div>
          ) : (
            <div className="analysis-result">
              <h4>❓ Analysis</h4>
              <div className="analysis-info">
                <p><strong>Input:</strong> {results.input} ({results.inputType})</p>
                <p><strong>Possible Type:</strong> {results.analysis.possibleType}</p>
                {results.analysis.rules.length > 0 && (
                  <div className="rules-list">
                    <strong>IUPAC Rules Applied:</strong>
                    <ul>
                      {results.analysis.rules.map((rule, index) => (
                        <li key={index}>{rule}</li>
                      ))}
                    </ul>
                  </div>
                )}
              </div>
            </div>
          )}
        </div>
      )}

      {searchResults.length > 0 && (
        <div className="similar-molecules">
          <h4>Similar Molecules:</h4>
          <div className="similar-grid">
            {searchResults.map((molecule, index) => (
              <div 
                key={index} 
                className="similar-item"
                onClick={() => selectMolecule(molecule)}
              >
                <div className="similar-name">{molecule.name}</div>
                <div className="similar-formula">{molecule.formula}</div>
                <div className="similar-iupac">{molecule.iupac}</div>
              </div>
            ))}
          </div>
        </div>
      )}

      <div className="functional-groups-reference">
        <h4>Functional Groups Reference:</h4>
        <div className="groups-grid">
          {functionalGroups.map((group, index) => (
            <div key={index} className="group-item">
              <div className="group-name">{group.name}</div>
              <div className="group-suffix">{group.suffix}</div>
              <div className="group-example">{group.example}</div>
            </div>
          ))}
        </div>
      </div>
    </div>
  );
};

export default NomenclatureConverter;
