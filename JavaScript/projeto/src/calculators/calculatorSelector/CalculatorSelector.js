import React from 'react';
import { BrowserRouter as Router } from 'react-router-dom';
import './styles/CalculatorSelector.css';
import './styles/calculator-options-fix.css';
import './styles/calculator-options-no-hover.css';
import './styles/calculator-hover-colors.css';

const CalculatorSelector = ({ onSelectCalculator }) => {
  const calculators = [
    {
      id: 'chemistry',
      title: 'Chemistry',
      description: 'Calculate molar mass, gas laws, unit conversions, and analyze chemical compounds',
      icon: '⚗️',
      color: '#4CAF50' // Verde
    },
    {
      id: 'physics',
      title: 'Physics',
      description: 'Calculate mechanics, thermodynamics, electricity, and other physics formulas',
      icon: '🔭',
      color: '#9C27B0' // Roxo
    },
    {
      id: 'math',
      title: 'Mathematics',
      description: 'Calculate algebra, calculus, geometry, and other mathematical operations',
      icon: '📐',
      color: '#2196F3' // Azul
    }
  ];

  return (
    <div className="calculator-selector-container">
      <h1 className="selector-title">Select Calculator</h1>
      <div
        className="calculator-options"
        style={{
          backgroundColor: 'transparent',
          background: 'transparent',
          border: 'none',
          boxShadow: 'none',
          transition: 'none'
        }}
      >
        {calculators.map(calculator => (
          <div
            key={calculator.id}
            className={`calculator-option calculator-option-${calculator.id}`}
            onClick={() => onSelectCalculator(calculator.id)}
            style={{ borderColor: calculator.color }}
          >
            <div className="calculator-icon" style={{ backgroundColor: calculator.color }}>
              {calculator.icon}
            </div>
            <h2 className="calculator-title">{calculator.title}</h2>
            <p className="calculator-description">{calculator.description}</p>
          </div>
        ))}
      </div>
    </div>
  );
};

export default CalculatorSelector;
