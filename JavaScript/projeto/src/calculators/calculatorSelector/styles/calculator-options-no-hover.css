/**
 * Correção específica para remover o efeito de fundo verde no hover da classe calculator-options
 */

/* Remover qualquer efeito de hover na div calculator-options */
.calculator-options {
  background-color: transparent !important;
  transition: none !important;
  border: none !important;
  box-shadow: none !important;
}

.calculator-options:hover {
  background-color: transparent !important;
  border-color: transparent !important;
  box-shadow: none !important;
  transform: none !important;
  background: transparent !important;
  border: none !important;
}

/* Garantir que não haja efeitos de hover indesejados */
.calculator-options * {
  transition: all 0.3s ease;
}

.calculator-options:hover * {
  /* Manter as transições apenas para os elementos filhos */
  transition: all 0.3s ease;
}

/* Remover qualquer estilo que possa estar sendo aplicado por outras regras */
.calculator-options:hover,
.calculator-options:focus,
.calculator-options:active {
  background-color: transparent !important;
  border-color: transparent !important;
  box-shadow: none !important;
  transform: none !important;
  background: transparent !important;
  border: none !important;
}

/* Garantir que o fundo seja sempre transparente */
.calculator-selector-container .calculator-options {
  background-color: transparent !important;
  background: transparent !important;
}

.calculator-selector-container .calculator-options:hover {
  background-color: transparent !important;
  background: transparent !important;
}

/* Remover qualquer estilo que possa estar sendo aplicado por outras regras */
div.calculator-options,
div.calculator-options:hover,
div.calculator-options:focus,
div.calculator-options:active {
  background-color: transparent !important;
  border-color: transparent !important;
  box-shadow: none !important;
  transform: none !important;
  background: transparent !important;
  border: none !important;
}
