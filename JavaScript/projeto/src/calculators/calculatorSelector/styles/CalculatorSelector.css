.calculator-selector-container {
  width: 100%;
  max-width: 1200px;
  margin: 0 auto;
  padding: 40px 20px;
  display: flex;
  flex-direction: column;
  align-items: center;
  min-height: 100vh;
  background-color: rgba(0, 0, 0, 0.2);
}

.selector-title {
  font-size: 2.5rem;
  color: white;
  margin-bottom: 40px;
  text-align: center;
  text-shadow: 0 2px 4px rgba(0, 0, 0, 0.5);
}

.calculator-options {
  display: flex;
  flex-wrap: wrap;
  justify-content: center;
  gap: 30px;
  width: 100%;
  background-color: transparent !important;
  transition: none !important;
}

.calculator-option {
  width: 300px;
  background-color: rgba(0, 0, 0, 0.3);
  border-radius: 10px;
  padding: 25px;
  display: flex;
  flex-direction: column;
  align-items: center;
  cursor: pointer;
  transition: all 0.3s ease;
  border: 2px solid transparent;
  box-shadow: 0 4px 8px rgba(0, 0, 0, 0.2);
}

.calculator-options:hover {
  background-color: transparent !important;
  border-color: transparent !important;
  box-shadow: none !important;
  transform: none !important;
}

.calculator-option:hover {
  transform: translateY(-10px);
  box-shadow: 0 10px 20px rgba(0, 0, 0, 0.3);
}

/* Estilos específicos para cada calculadora no hover foram movidos para calculator-hover-colors.css */

.calculator-icon {
  font-size: 3rem;
  width: 80px;
  height: 80px;
  border-radius: 50%;
  display: flex;
  justify-content: center;
  align-items: center;
  margin-bottom: 20px;
}

.calculator-title {
  font-size: 1.5rem;
  color: white;
  margin-bottom: 15px;
  text-align: center;
}

.calculator-description {
  font-size: 0.9rem;
  color: rgba(255, 255, 255, 0.8);
  text-align: center;
  line-height: 1.5;
}

/* Responsividade */
@media (max-width: 768px) {
  .calculator-options {
    flex-direction: column;
    align-items: center;
  }

  .calculator-option {
    width: 100%;
    max-width: 300px;
  }
}
