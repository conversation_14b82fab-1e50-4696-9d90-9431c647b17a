/*==========================================================================
   ESTILOS BÁSICOS E LAYOUT GERAL
   ========================================================================== */

.App {
  position: relative;
  min-height: 100vh;
  width: 100%;
  overflow-y: auto;
  overflow-x: hidden;
  background-color: black;
  padding-bottom: 50px;
}

/* Garantir que o menu dropdown do painel de unidades apareça acima de tudo */
body > div[class*="react-select__menu"],
body > div[id^="react-select"][id$="-listbox"],
body > div[id^="react-select"][id$="-menu"],
#react-select-menu-portal,
div[id^="react-select"][id$="-portal"] {
  z-index: 1001 !important; /* Valor menor para não interferir com outros componentes */

  /* POSICIONAMENTO DO DROPDOWN: Ajuste position para controlar como o dropdown é posicionado */
  position: absolute !important;

  /* POSICIONAMENTO DO DROPDOWN: Ajuste este valor para aproximar ou afastar o dropdown do select */
  margin-top: -1px !important; /* Conecta perfeitamente ao select */
}

/* Garantir que o menu dropdown do painel de unidades apareça acima de tudo */
.units-panel div[class*="menu"],
.units-panel div[id^="react-select"][id$="-listbox"] {
  z-index: 1002 !important; /* Valor maior para o painel de unidades */

  /* POSICIONAMENTO DO DROPDOWN: Ajuste position para controlar como o dropdown é posicionado */
  position: absolute !important;

  /* POSICIONAMENTO DO DROPDOWN: Ajuste este valor para aproximar ou afastar o dropdown do select */
  margin-top: -1px !important; /* Conecta perfeitamente ao select */
}

.App-header {
  background-color: #282c34;
  min-height: 100vh;
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  font-size: calc(10px + 2vmin);
  color: white;
}

.App-link {
  color: #61dafb;
}

@keyframes App-logo-spin {
  from {
    transform: rotate(0deg);
  }
  to {
    transform: rotate(360deg);
  }
}
