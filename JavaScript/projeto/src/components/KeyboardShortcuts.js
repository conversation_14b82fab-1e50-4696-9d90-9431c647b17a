import { useEffect } from 'react';

/**
 * Hook para adicionar atalhos de teclado
 * @param {Object} shortcuts - Objeto com os atalhos de teclado
 * @param {boolean} enabled - Se os atalhos estão habilitados
 * @example
 * // Uso:
 * useKeyboardShortcuts({
 *   'Alt+C': () => handleCalculate(),
 *   'Alt+R': () => handleReset(),
 *   'Escape': () => handleClose()
 * });
 */
const useKeyboardShortcuts = (shortcuts, enabled = true) => {
  useEffect(() => {
    if (!enabled) return;
    
    const handleKeyDown = (e) => {
      // Construir a combinação de teclas
      const key = e.key.toLowerCase();
      const combo = [
        e.ctrlKey ? 'Ctrl' : '',
        e.altKey ? 'Alt' : '',
        e.shiftKey ? 'Shift' : '',
        key === ' ' ? 'Space' : key.charAt(0).toUpperCase() + key.slice(1)
      ].filter(Boolean).join('+');
      
      // Verificar se a combinação existe nos atalhos
      const handler = shortcuts[combo] || shortcuts[e.key];
      
      if (handler) {
        e.preventDefault();
        handler(e);
      }
    };
    
    window.addEventListener('keydown', handleKeyDown);
    
    return () => {
      window.removeEventListener('keydown', handleKeyDown);
    };
  }, [shortcuts, enabled]);
};

/**
 * Componente para exibir os atalhos de teclado disponíveis
 * @param {Object} props - Propriedades do componente
 * @param {Object} props.shortcuts - Objeto com os atalhos de teclado
 * @param {string} props.title - Título da seção de atalhos
 */
const KeyboardShortcutsHelp = ({ shortcuts, title = 'Atalhos de Teclado' }) => {
  return (
    <div className="keyboard-shortcuts-help">
      <h3>{title}</h3>
      <ul>
        {Object.entries(shortcuts).map(([key, description]) => (
          <li key={key}>
            <kbd>{key}</kbd>
            <span>{description}</span>
          </li>
        ))}
      </ul>
      
      <style jsx>{`
        .keyboard-shortcuts-help {
          background-color: #1a1a1a;
          border: 1px solid var(--border-color);
          border-radius: 8px;
          padding: 15px;
          margin-top: 20px;
        }
        
        h3 {
          margin-top: 0;
          color: var(--primary-color);
          font-size: 16px;
        }
        
        ul {
          list-style: none;
          padding: 0;
          margin: 0;
        }
        
        li {
          display: flex;
          align-items: center;
          margin-bottom: 8px;
        }
        
        kbd {
          background-color: #333;
          border: 1px solid #666;
          border-radius: 4px;
          padding: 2px 6px;
          font-family: monospace;
          font-size: 12px;
          margin-right: 10px;
          min-width: 60px;
          text-align: center;
        }
        
        span {
          color: #ddd;
          font-size: 14px;
        }
      `}</style>
    </div>
  );
};

export { useKeyboardShortcuts, KeyboardShortcutsHelp };
