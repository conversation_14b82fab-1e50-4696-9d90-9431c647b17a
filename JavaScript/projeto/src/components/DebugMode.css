/* Estilos para o modo de debug */

/* Bo<PERSON><PERSON> de toggle */
.debug-toggle {
  position: fixed;
  bottom: 10px;
  right: 10px;
  padding: 8px 12px;
  background-color: #333;
  color: white;
  border: none;
  border-radius: 4px;
  cursor: pointer;
  z-index: 10000;
  font-size: 12px;
}

.debug-toggle.active {
  background-color: #f44336;
}

/* Painel de informações */
.debug-info-panel {
  position: fixed;
  top: 10px;
  right: 10px;
  width: 300px;
  max-height: 80vh;
  background-color: rgba(0, 0, 0, 0.8);
  color: white;
  border-radius: 4px;
  overflow: hidden;
  z-index: 10000;
  font-family: monospace;
  font-size: 12px;
  box-shadow: 0 0 10px rgba(0, 0, 0, 0.5);
}

.debug-panel-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 8px 12px;
  background-color: #333;
  border-bottom: 1px solid #555;
}

.debug-panel-header h3 {
  margin: 0;
  font-size: 14px;
}

.debug-panel-controls {
  display: flex;
  gap: 8px;
}

.debug-panel-controls button {
  background: none;
  border: none;
  color: white;
  cursor: pointer;
  font-size: 16px;
  padding: 0;
  display: flex;
  align-items: center;
  justify-content: center;
}

.debug-panel-controls button.active {
  color: #f44336;
}

/* Conteúdo do painel */
.debug-info-content {
  max-height: calc(80vh - 40px);
  overflow-y: auto;
  padding: 12px;
}

.debug-info-content .section {
  margin-bottom: 16px;
  padding-bottom: 16px;
  border-bottom: 1px solid #555;
}

.debug-info-content .section:last-child {
  border-bottom: none;
  margin-bottom: 0;
  padding-bottom: 0;
}

.debug-info-content p {
  margin: 4px 0;
  line-height: 1.4;
}

.debug-info-content .empty-state {
  text-align: center;
  padding: 20px 0;
  color: #aaa;
}

.debug-info-content .pinned-indicator {
  color: #f44336;
  font-weight: bold;
  margin-bottom: 8px;
}

.scroll-hint {
  text-align: center;
  padding: 4px;
  background-color: #444;
  margin-bottom: 8px;
  font-size: 10px;
  color: #aaa;
}

/* Painel de configuração */
.debug-config-panel {
  background-color: #222;
  padding: 12px;
  border-bottom: 1px solid #555;
}

.debug-config-section {
  margin-bottom: 16px;
}

.debug-config-section h4 {
  margin: 0 0 8px 0;
  font-size: 12px;
  color: #aaa;
}

.debug-config-row {
  display: flex;
  flex-wrap: wrap;
  gap: 8px;
  margin-bottom: 8px;
}

.debug-config-option {
  display: flex;
  align-items: center;
  gap: 4px;
}

.debug-config-option input[type="checkbox"] {
  margin: 0;
}

.debug-config-option label {
  font-size: 11px;
}

.debug-config-option input[type="range"] {
  width: 100px;
}

/* Elementos de debug */
.debug-outline {
  position: fixed;
  pointer-events: none;
  z-index: 9998;
  box-sizing: border-box;
}

.debug-dimension-label {
  position: fixed;
  pointer-events: none;
  z-index: 9999;
  background-color: rgba(0, 0, 0, 0.7);
  color: white;
  font-size: 10px;
  padding: 2px 4px;
  border-radius: 2px;
  text-align: center;
}

.center-line-x, .center-line-y {
  position: fixed;
  pointer-events: none;
  z-index: 9998;
}

.center-point {
  position: fixed;
  pointer-events: none;
  z-index: 9999;
  border-radius: 50%;
}

.debug-grid-line {
  position: fixed;
  pointer-events: none;
  z-index: 9997;
}

.debug-center-line {
  position: fixed;
  pointer-events: none;
  z-index: 9997;
}

.debug-thirds-line {
  position: fixed;
  pointer-events: none;
  z-index: 9997;
}
