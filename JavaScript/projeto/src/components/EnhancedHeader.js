import React from 'react';
import ThemeToggle from './ThemeToggle';
import HelpButton from './HelpButton';
import FeedbackButton from './FeedbackButton';
import { KeyboardShortcutsHelp } from './KeyboardShortcuts';

/**
 * Componente de cabeçalho melhorado com botões de tema, ajuda e feedback
 * @param {Object} props - Propriedades do componente
 * @param {string} props.title - Título da aplicação
 * @param {string} props.className - Classes CSS adicionais
 */
const EnhancedHeader = ({
  title = 'Chemistry Calculator',
  className = ''
}) => {
  // Atalhos de teclado disponíveis
  const shortcuts = {
    'Alt+1': 'Ir para a primeira operação',
    'Alt+2': 'Ir para a segunda operação',
    'Alt+3': 'Ir para a terceira operação',
    'Alt+C': 'Calcular',
    'Alt+R': 'Resetar',
    'Esc': 'Fechar modal/popup'
  };
  
  return (
    <header className={`enhanced-header ${className}`}>
      <div className="header-left">
        <h1>{title}</h1>
      </div>
      
      <div className="header-right">
        <FeedbackButton />
        
        <HelpButton title="Ajuda e Atalhos">
          <div className="help-content">
            <h3>Bem-vindo à Calculadora de Química</h3>
            <p>
              Esta aplicação permite realizar diversos cálculos químicos, incluindo:
            </p>
            <ul>
              <li>Lei dos Gases Ideais</li>
              <li>Conversões de Unidades</li>
              <li>Composição Elementar</li>
            </ul>
            
            <p>
              Use o carrossel na parte superior para navegar entre as diferentes operações.
              Cada operação tem seus próprios controles e campos de entrada.
            </p>
            
            <KeyboardShortcutsHelp shortcuts={shortcuts} />
          </div>
        </HelpButton>
        
        <ThemeToggle />
      </div>
      
      <style jsx>{`
        .enhanced-header {
          display: flex;
          align-items: center;
          justify-content: space-between;
          padding: 10px 20px;
          background-color: rgba(0, 0, 0, 0.3);
          border-bottom: 1px solid var(--border-color);
        }
        
        .header-left h1 {
          margin: 0;
          font-size: 20px;
          color: var(--primary-color);
        }
        
        .header-right {
          display: flex;
          align-items: center;
          gap: 15px;
        }
        
        .help-content {
          color: #ddd;
        }
        
        .help-content h3 {
          color: var(--primary-color);
          margin-top: 0;
        }
        
        .help-content ul {
          margin-bottom: 20px;
        }
        
        .help-content li {
          margin-bottom: 5px;
        }
      `}</style>
    </header>
  );
};

export default EnhancedHeader;
