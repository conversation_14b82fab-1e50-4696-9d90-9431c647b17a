import React, { useState, useEffect } from 'react';
import '../qol/styles/qol-improvements.css';

/**
 * Notification component for displaying temporary messages
 * @param {Object} props - Component props
 * @param {string} props.message - The message to display
 * @param {string} props.type - The type of notification ('success', 'error', 'info')
 * @param {number} props.duration - How long to show the notification in ms
 * @param {boolean} props.show - Whether to show the notification
 * @param {Function} props.onClose - Function to call when notification closes
 */
const Notification = ({
  message,
  type = 'info',
  duration = 3000,
  show = false,
  onClose = () => {}
}) => {
  const [visible, setVisible] = useState(show);

  useEffect(() => {
    setVisible(show);

    if (show) {
      const timer = setTimeout(() => {
        setVisible(false);
        setTimeout(() => {
          onClose();
        }, 500); // Wait for exit animation to complete
      }, duration);

      return () => clearTimeout(timer);
    }
  }, [show, duration, onClose]);

  return (
    <div className={`notification ${type} ${visible ? 'show' : ''}`}>
      {message}
    </div>
  );
};

/**
 * Hook for managing notifications
 * @returns {Object} - Notification state and functions
 */
export const useNotification = () => {
  const [notification, setNotification] = useState({
    show: false,
    message: '',
    type: 'info',
    duration: 3000
  });

  const showNotification = (message, type = 'info', duration = 3000) => {
    setNotification({
      show: true,
      message,
      type,
      duration
    });
  };

  const hideNotification = () => {
    setNotification(prev => ({
      ...prev,
      show: false
    }));
  };

  return {
    notification,
    showNotification,
    hideNotification
  };
};

export default Notification;
