import React from 'react';

/**
 * Componente de rodapé melhorado
 * @param {Object} props - Propriedades do componente
 * @param {string} props.className - Classes CSS adicionais
 */
const EnhancedFooter = ({ className = '' }) => {
  const currentYear = new Date().getFullYear();
  
  return (
    <footer className={`enhanced-footer ${className}`}>
      <div className="footer-content">
        <div className="footer-left">
          <p>© {currentYear} Chemistry Calculator</p>
        </div>
        
        <div className="footer-right">
          <a href="#" onClick={(e) => e.preventDefault()}>Termos de Uso</a>
          <a href="#" onClick={(e) => e.preventDefault()}>Privacidade</a>
          <a href="#" onClick={(e) => e.preventDefault()}>Sobre</a>
        </div>
      </div>
      
      <style jsx>{`
        .enhanced-footer {
          padding: 15px 20px;
          background-color: rgba(0, 0, 0, 0.3);
          border-top: 1px solid var(--border-color);
          margin-top: auto;
        }
        
        .footer-content {
          display: flex;
          align-items: center;
          justify-content: space-between;
          max-width: 1200px;
          margin: 0 auto;
        }
        
        .footer-left p {
          margin: 0;
          font-size: 14px;
          color: #aaa;
        }
        
        .footer-right {
          display: flex;
          gap: 20px;
        }
        
        .footer-right a {
          color: #aaa;
          text-decoration: none;
          font-size: 14px;
          transition: color 0.3s ease;
        }
        
        .footer-right a:hover {
          color: var(--primary-color);
        }
        
        @media (max-width: 600px) {
          .footer-content {
            flex-direction: column;
            gap: 10px;
          }
          
          .footer-right {
            margin-top: 10px;
          }
        }
      `}</style>
    </footer>
  );
};

export default EnhancedFooter;
