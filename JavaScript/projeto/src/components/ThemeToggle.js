import React, { useState, useEffect } from 'react';

/**
 * Componente para alternar entre temas claro e escuro
 * @param {Object} props - Propriedades do componente
 * @param {string} props.className - Classes CSS adicionais
 */
const ThemeToggle = ({ className = '' }) => {
  // Verificar se o tema escuro está ativo
  const [isDarkMode, setIsDarkMode] = useState(() => {
    // Verificar preferência salva
    const savedTheme = localStorage.getItem('theme');
    if (savedTheme) {
      return savedTheme === 'dark';
    }
    
    // Verificar preferência do sistema
    return window.matchMedia('(prefers-color-scheme: dark)').matches;
  });
  
  // Aplicar o tema quando o componente montar ou o tema mudar
  useEffect(() => {
    // Adicionar ou remover a classe dark-theme do body
    if (isDarkMode) {
      document.body.classList.add('dark-theme');
      document.body.classList.remove('light-theme');
    } else {
      document.body.classList.add('light-theme');
      document.body.classList.remove('dark-theme');
    }
    
    // Salvar a preferência
    localStorage.setItem('theme', isDarkMode ? 'dark' : 'light');
  }, [isDarkMode]);
  
  // Alternar o tema
  const toggleTheme = () => {
    setIsDarkMode(prev => !prev);
  };
  
  return (
    <button
      className={`theme-toggle ${className} ${isDarkMode ? 'dark' : 'light'}`}
      onClick={toggleTheme}
      aria-label={`Alternar para tema ${isDarkMode ? 'claro' : 'escuro'}`}
      title={`Alternar para tema ${isDarkMode ? 'claro' : 'escuro'}`}
    >
      {isDarkMode ? '☀️' : '🌙'}
      
      <style jsx>{`
        .theme-toggle {
          background: none;
          border: none;
          cursor: pointer;
          font-size: 18px;
          padding: 5px;
          border-radius: 50%;
          transition: all 0.3s ease;
          display: flex;
          align-items: center;
          justify-content: center;
          width: 36px;
          height: 36px;
        }
        
        .theme-toggle:hover {
          background-color: rgba(255, 255, 255, 0.1);
        }
        
        .theme-toggle.light {
          color: #333;
        }
        
        .theme-toggle.dark {
          color: #fff;
        }
      `}</style>
    </button>
  );
};

export default ThemeToggle;
