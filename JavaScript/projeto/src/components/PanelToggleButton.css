.panel-toggle-button {
  position: fixed;
  top: 20px;
  right: 20px;
  z-index: 1001;
  
  display: flex;
  align-items: center;
  gap: 8px;
  
  background: rgba(0, 0, 0, 0.8);
  border: 1px solid var(--border-color, #4CAF50);
  border-radius: 8px;
  padding: 8px 12px;
  
  color: white;
  font-size: 14px;
  font-weight: 500;
  
  cursor: pointer;
  transition: all 0.3s ease;
  
  backdrop-filter: blur(10px);
  box-shadow: 0 4px 6px rgba(0, 0, 0, 0.1);
}

.panel-toggle-button:hover {
  background: rgba(0, 0, 0, 0.9);
  border-color: var(--primary-color, #4CAF50);
  transform: translateY(-1px);
  box-shadow: 0 6px 12px rgba(0, 0, 0, 0.2);
}

.panel-toggle-button:active {
  transform: translateY(0);
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
}

.toggle-icon {
  display: flex;
  align-items: center;
  justify-content: center;
  color: var(--primary-color, #4CAF50);
  transition: transform 0.3s ease;
}

.panel-toggle-button:hover .toggle-icon {
  transform: scale(1.1);
}

.toggle-text {
  font-weight: 500;
  white-space: nowrap;
}

.toggle-shortcut {
  background: rgba(255, 255, 255, 0.1);
  border: 1px solid rgba(255, 255, 255, 0.2);
  border-radius: 4px;
  padding: 2px 6px;
  font-size: 12px;
  font-weight: bold;
  color: var(--primary-color, #4CAF50);
  min-width: 20px;
  text-align: center;
}

/* Estados do botão */
.panel-toggle-button.panels-visible {
  border-color: #ff6b6b;
}

.panel-toggle-button.panels-visible:hover {
  border-color: #ff5252;
}

.panel-toggle-button.panels-visible .toggle-icon {
  color: #ff6b6b;
}

.panel-toggle-button.panels-visible .toggle-shortcut {
  color: #ff6b6b;
}

.panel-toggle-button.panels-hidden {
  border-color: var(--primary-color, #4CAF50);
}

.panel-toggle-button.panels-hidden:hover {
  border-color: #66BB6A;
}

/* Responsividade */
@media (max-width: 768px) {
  .panel-toggle-button {
    top: 10px;
    right: 10px;
    padding: 6px 10px;
    font-size: 12px;
  }
  
  .toggle-text {
    display: none; /* Ocultar texto em telas pequenas */
  }
  
  .toggle-shortcut {
    font-size: 10px;
    padding: 1px 4px;
  }
}

@media (max-width: 480px) {
  .panel-toggle-button {
    padding: 4px 8px;
  }
  
  .toggle-icon svg {
    width: 16px;
    height: 16px;
  }
}
