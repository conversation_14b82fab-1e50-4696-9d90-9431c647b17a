import React from 'react';
import { usePanelVisibility } from '../contexts/PanelVisibilityContext';
import './PanelToggleButton.css';

const PanelToggleButton = () => {
  const { panelsVisible, togglePanels } = usePanelVisibility();

  return (
    <button 
      className={`panel-toggle-button ${panelsVisible ? 'panels-visible' : 'panels-hidden'}`}
      onClick={togglePanels}
      title={`${panelsVisible ? 'Ocultar' : 'Mostrar'} painéis laterais (Tecla H)`}
      aria-label={`${panelsVisible ? 'Ocultar' : 'Mostrar'} painéis laterais`}
    >
      <div className="toggle-icon">
        {panelsVisible ? (
          // Ícone para ocultar (setas para dentro)
          <svg width="20" height="20" viewBox="0 0 24 24" fill="none" stroke="currentColor" strokeWidth="2">
            <path d="M3 12h6m6 0h6M9 6l-6 6 6 6M15 18l6-6-6-6"/>
          </svg>
        ) : (
          // <PERSON>cone para mostrar (setas para fora)
          <svg width="20" height="20" viewBox="0 0 24 24" fill="none" stroke="currentColor" strokeWidth="2">
            <path d="M3 12h6m6 0h6M15 6l6 6-6 6M9 18l-6-6 6-6"/>
          </svg>
        )}
      </div>
      <span className="toggle-text">
        {panelsVisible ? 'Ocultar Painéis' : 'Mostrar Painéis'}
      </span>
      <span className="toggle-shortcut">H</span>
    </button>
  );
};

export default PanelToggleButton;
