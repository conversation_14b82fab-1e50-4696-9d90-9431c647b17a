import React, { useState } from 'react';
import Modal from './Modal';

/**
 * Componente de botão de ajuda que abre um modal com informações
 * @param {Object} props - Propriedades do componente
 * @param {React.ReactNode} props.children - Conteúdo do modal de ajuda
 * @param {string} props.title - Título do modal
 * @param {string} props.className - Classes CSS adicionais
 */
const HelpButton = ({
  children,
  title = 'Ajuda',
  className = ''
}) => {
  const [isOpen, setIsOpen] = useState(false);
  
  const openModal = () => setIsOpen(true);
  const closeModal = () => setIsOpen(false);
  
  return (
    <>
      <button
        className={`help-button ${className}`}
        onClick={openModal}
        aria-label="Ajuda"
        title="Ajuda"
      >
        ?
      </button>
      
      <Modal
        isOpen={isOpen}
        onClose={closeModal}
        title={title}
      >
        {children}
      </Modal>
      
      <style jsx>{`
        .help-button {
          width: 24px;
          height: 24px;
          border-radius: 50%;
          background-color: rgba(76, 175, 80, 0.2);
          border: 1px solid var(--primary-color);
          color: var(--primary-color);
          font-weight: bold;
          font-size: 14px;
          display: flex;
          align-items: center;
          justify-content: center;
          cursor: pointer;
          transition: all 0.3s ease;
        }
        
        .help-button:hover {
          background-color: rgba(76, 175, 80, 0.4);
          transform: scale(1.1);
        }
      `}</style>
    </>
  );
};

export default HelpButton;
