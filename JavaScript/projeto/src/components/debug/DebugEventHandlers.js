import { useEffect, useCallback } from 'react';

/**
 * Componente para gerenciar os event handlers do modo debug
 */
const DebugEventHandlers = ({
  isDebugMode,
  isPinned,
  handleMouseEnter,
  handleMouseLeave,
  handleElementClick
}) => {
  // Adicionar/remover event listeners
  const setupEventListeners = useCallback((add) => {
    try {
      // Usar uma única query com todos os seletores para todas as interfaces
      const selectors = [
        // Condições
        '.conditions-container',
        '.condition-row',
        '.condition-input',
        '.condition-box',
        '.presets-container',
        '.preset-button',
        '.conditions-block',
        // Conversões
        '.conversion-container',
        '.conversion-row',
        '.conversion-input-container',
        '.conversion-input',
        '.invert-button',
        '.conversions-block',
        // Unidades
        '.units-container',
        '.unit-row',
        '.unit-select',
        '.unit-label',
        // Composto
        '.compound-container',
        '.compound-input',
        '.compound-result',
        '.compound-info-block',
        // Lei dos Gases
        '.gas-law-container',
        '.gas-law-row',
        '.gas-law-input',
        '.gas-law-result',
        '.gas-law-block',
        // Geral
        '.main-container',
        '.parameter-panel',
        '.results-panel',
        '.input-with-unit',
        '.input-unit-group',
        '.calculator-block',
        '.calculo-container'
      ];

      const elements = document.querySelectorAll(selectors.join(', '));

      elements.forEach(element => {
        if (!element) return;

        if (add) {
          element.addEventListener('mouseenter', handleMouseEnter);
          element.addEventListener('mouseleave', handleMouseLeave);
          element.addEventListener('click', handleElementClick);
          // Adicionar estilo de cursor para indicar que o elemento é clicável
          element.style.cursor = 'pointer';
        } else {
          element.removeEventListener('mouseenter', handleMouseEnter);
          element.removeEventListener('mouseleave', handleMouseLeave);
          element.removeEventListener('click', handleElementClick);
          // Remover estilo de cursor
          element.style.cursor = '';
        }
      });
    } catch (error) {
      console.error('Error setting up event listeners:', error);
    }
  }, [handleMouseEnter, handleMouseLeave, handleElementClick]);

  // Efeito para adicionar/remover event listeners
  useEffect(() => {
    if (isDebugMode) {
      // Adicionar event listeners
      setupEventListeners(true);

      // Cleanup
      return () => {
        // Remover event listeners
        setupEventListeners(false);
      };
    }
  }, [isDebugMode, isPinned, setupEventListeners]);

  // Este componente não renderiza nada visível diretamente
  return null;
};

export default DebugEventHandlers;
