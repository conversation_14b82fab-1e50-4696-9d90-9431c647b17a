import React, { useState, useRef, useCallback } from 'react';
import '../../calculators/chemistry/styles/debug/debug-mode.css';
import '../../calculators/chemistry/styles/debug/debug-info-panel.css';
import DebugButton from './DebugButton';
import DebugInfoPanel from './DebugInfoPanel';
import DebugVisualElements from './DebugVisualElements';
import DebugEventHandlers from './DebugEventHandlers';

/**
 * Componente principal para o modo debug
 *
 * Este componente integra os diferentes módulos do modo debug:
 * - DebugButton: Botão para ativar/desativar o modo debug
 * - DebugInfoPanel: Painel com informações sobre o elemento selecionado
 * - DebugVisualElements: Elementos visuais (bordas, linhas, etc.)
 * - DebugEventHandlers: Gerenciamento de eventos (hover, click, etc.)
 */
const DebugMode = () => {
  // Estados
  const [isDebugMode, setIsDebugMode] = useState(false);
  // eslint-disable-next-line no-unused-vars
  const [hoveredElement, setHoveredElement] = useState(null);
  const [selectedElement, setSelectedElement] = useState(null);
  const [elementInfo, setElementInfo] = useState({});
  const [isPinned, setIsPinned] = useState(false);
  const [isConfigOpen, setIsConfigOpen] = useState(false);
  const [config, setConfig] = useState({
    // Opções de visualização
    showDimensions: false,
    showOutlines: false,
    showCenterLines: false,
    showBoxModel: false,
    showLayout: false,
    showStyle: false,

    // Interfaces - todas desativadas por padrão
    showConditions: false,
    showPresets: false,
    showInputs: false,
    showConversions: false,
    showCompound: false,
    showGasLaw: false,

    // Visibilidade de elementos específicos (true = mostrar, false = esconder)
    showConversion1: true,
    showConversion2: true,
    showConversion3: true,
    showCondition1: true,
    showCondition2: true,
    showStpPreset: true,
    showSatpPreset: true,
    showRoomTempPreset: true,
    showCustomPreset: true,
    showCompoundSection1: true,
    showCompoundSection2: true,
    showGasLawSection1: true,
    showGasLawSection2: true,

    // Grades de guia
    showFullGrid: false,
    showCenterGrid: false,
    showRuleOfThirds: false,
    gridOpacity: 0.3,
    gridSize: 20,

    // Linhas de centralização para cada interface - todas desativadas por padrão
    showConditionsCenterLines: false,
    showPresetsCenterLines: false,
    showConversionsCenterLines: false,
    showCompoundCenterLines: false,
    showGasLawCenterLines: false,
    showParameterPanelCenterLines: false,
    showResultsPanelCenterLines: false,

    // Componentes específicos para cada interface
    // Conversions
    showConversionBox: false,
    showConversionControls: false,
    showConversionSelect: false,

    // Conditions
    showConditionBox: false,
    showConditionInput: false,
    showPresetButtons: false,

    // Compound
    showCompoundInput: false,
    showCompoundResult: false,

    // Gas Law
    showGasLawInput: false,
    showGasLawEquation: false,
    showGasLawResult: false,
  });

  // Referências
  const infoRef = useRef(null);

  // Ativar/desativar o modo debug
  const toggleDebugMode = () => {
    setIsDebugMode(prev => !prev);
  };

  // Alternar configurações de debug com atualização imediata
  const toggleConfig = (key) => {
    // Atualizar o estado imediatamente
    setConfig(prev => {
      // Verificar se o valor realmente mudou para evitar renderizações desnecessárias
      if (prev[key] === undefined) return prev;

      const newConfig = {
        ...prev,
        [key]: !prev[key]
      };

      // Forçar uma atualização imediata dos elementos visuais
      // Isso é feito disparando um evento personalizado que o DebugVisualElements irá escutar
      const event = new CustomEvent('debug-config-changed', { detail: { config: newConfig } });
      document.dispatchEvent(event);

      return newConfig;
    });
  };

  // Função para ativar/desativar todas as configurações de um grupo
  const toggleGroupConfig = (prefix, value) => {
    setConfig(prev => {
      const newConfig = { ...prev };
      let changed = false;

      // Encontrar todas as chaves que começam com o prefixo
      Object.keys(prev).forEach(key => {
        if (key.startsWith(prefix)) {
          if (newConfig[key] !== value) {
            newConfig[key] = value;
            changed = true;
          }
        }
      });

      // Forçar uma atualização imediata dos elementos visuais apenas se houve mudanças
      if (changed) {
        const event = new CustomEvent('debug-config-changed', { detail: { config: newConfig } });
        document.dispatchEvent(event);
      }

      return newConfig;
    });
  };

  // Alternar painel de configuração
  const toggleConfigPanel = () => {
    setIsConfigOpen(prev => !prev);
  };

  // Função para obter informações de um elemento
  const getElementInfo = useCallback((element) => {
    if (!element) return {};

    const rect = element.getBoundingClientRect();
    const computedStyle = window.getComputedStyle(element);

    return {
      className: element.className,
      tagName: element.tagName.toLowerCase(),
      id: element.id || 'none',
      width: Math.round(rect.width),
      height: Math.round(rect.height),
      top: Math.round(rect.top),
      left: Math.round(rect.left),
      padding: computedStyle.padding,
      margin: computedStyle.margin,
      border: computedStyle.border,
      position: computedStyle.position,
      display: computedStyle.display,
      flexDirection: computedStyle.flexDirection,
      justifyContent: computedStyle.justifyContent,
      alignItems: computedStyle.alignItems,
      gap: computedStyle.gap,
      zIndex: computedStyle.zIndex,
      backgroundColor: computedStyle.backgroundColor,
      color: computedStyle.color
    };
  }, []);

  // Função para lidar com o evento mouseenter
  const handleMouseEnter = useCallback((event) => {
    if (!isPinned) {
      const element = event.currentTarget;
      setHoveredElement(element);
      setElementInfo(getElementInfo(element));
    }
  }, [isPinned, getElementInfo]);

  // Função para lidar com o evento mouseleave
  const handleMouseLeave = useCallback(() => {
    if (!isPinned) {
      setHoveredElement(null);
    }
  }, [isPinned]);

  // Função para lidar com o clique em um elemento (para fixar a seleção)
  const handleElementClick = useCallback((event) => {
    event.preventDefault();
    event.stopPropagation();

    const element = event.currentTarget;

    if (isPinned && selectedElement === element) {
      // Se clicar no mesmo elemento que já está selecionado, desfixa
      setIsPinned(false);
      setSelectedElement(null);
      setHoveredElement(element); // Mantém o hover
    } else {
      // Fixa o elemento selecionado
      setIsPinned(true);
      setSelectedElement(element);
      setElementInfo(getElementInfo(element));
    }
  }, [isPinned, selectedElement, getElementInfo]);

  // Função para desafixar a seleção
  const unpinSelection = useCallback(() => {
    setIsPinned(false);
    setSelectedElement(null);
    setHoveredElement(null);
  }, []);

  return (
    <>
      {/* Botão para ativar/desativar o modo debug */}
      <DebugButton
        isDebugMode={isDebugMode}
        toggleDebugMode={toggleDebugMode}
      />

      {/* Painel de informações (visível apenas quando o modo debug está ativo) */}
      {isDebugMode && (
        <DebugInfoPanel
          infoRef={infoRef}
          elementInfo={elementInfo}
          isPinned={isPinned}
          unpinSelection={unpinSelection}
          isConfigOpen={isConfigOpen}
          toggleConfigPanel={toggleConfigPanel}
          config={config}
          toggleConfig={toggleConfig}
          toggleGroupConfig={toggleGroupConfig}
        />
      )}

      {/* Elementos visuais (bordas, linhas, etc.) */}
      <DebugVisualElements
        isDebugMode={isDebugMode}
        config={config}
      />

      {/* Gerenciamento de eventos (hover, click, etc.) */}
      <DebugEventHandlers
        isDebugMode={isDebugMode}
        isPinned={isPinned}
        handleMouseEnter={handleMouseEnter}
        handleMouseLeave={handleMouseLeave}
        handleElementClick={handleElementClick}
      />
    </>
  );
};

export default DebugMode;
