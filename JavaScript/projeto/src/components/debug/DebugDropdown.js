import React, { useState } from 'react';

/**
 * Componente para criar um dropdown no painel de debug
 */
const DebugDropdown = ({ title, children, defaultOpen = false, selectAllId, selectAllChecked, onSelectAll }) => {
  const [isOpen, setIsOpen] = useState(defaultOpen);

  const toggleDropdown = () => {
    setIsOpen(!isOpen);
  };

  return (
    <div className="debug-dropdown">
      <div className="debug-dropdown-header" onClick={toggleDropdown}>
        {selectAllId && onSelectAll ? (
          <div className="debug-dropdown-title">
            <label htmlFor={selectAllId} className="debug-select-all" onClick={(e) => e.stopPropagation()}>
              <input
                type="checkbox"
                id={selectAllId}
                checked={selectAllChecked}
                onChange={onSelectAll}
                onClick={(e) => e.stopPropagation()}
              />
              <span dangerouslySetInnerHTML={{ __html: title }}></span>
            </label>
            <div style={{ flex: 1 }}></div>
            <span className={`debug-dropdown-icon ${isOpen ? 'open' : ''}`}>
              ▼
            </span>
          </div>
        ) : (
          <div className="debug-dropdown-title">
            <h5 dangerouslySetInnerHTML={{ __html: title }}></h5>
            <div style={{ flex: 1 }}></div>
            <span className={`debug-dropdown-icon ${isOpen ? 'open' : ''}`}>
              ▼
            </span>
          </div>
        )}
      </div>
      <div className={`debug-dropdown-content ${isOpen ? 'open' : ''}`}>
        {children}
      </div>
    </div>
  );
};

export default DebugDropdown;
