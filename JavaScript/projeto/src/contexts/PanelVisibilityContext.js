import React, { createContext, useContext, useState, useEffect } from 'react';

const PanelVisibilityContext = createContext();

export const usePanelVisibility = () => {
  const context = useContext(PanelVisibilityContext);
  if (!context) {
    throw new Error('usePanelVisibility must be used within a PanelVisibilityProvider');
  }
  return context;
};

export const PanelVisibilityProvider = ({ children }) => {
  const [panelsVisible, setPanelsVisible] = useState(true);

  // Função para alternar visibilidade
  const togglePanels = () => {
    setPanelsVisible(prev => !prev);
  };

  // Função para ocultar painéis
  const hidePanels = () => {
    setPanelsVisible(false);
  };

  // Função para mostrar painéis
  const showPanels = () => {
    setPanelsVisible(true);
  };

  // Listener para tecla de atalho (H para Hide/Show)
  useEffect(() => {
    const handleKeyPress = (event) => {
      // Verificar se não está digitando em um input
      if (event.target.tagName === 'INPUT' || 
          event.target.tagName === 'TEXTAREA' || 
          event.target.isContentEditable) {
        return;
      }

      // Tecla H para alternar painéis
      if (event.key.toLowerCase() === 'h' && !event.ctrlKey && !event.altKey && !event.metaKey) {
        event.preventDefault();
        togglePanels();
      }
    };

    document.addEventListener('keydown', handleKeyPress);
    return () => {
      document.removeEventListener('keydown', handleKeyPress);
    };
  }, []);

  // Aplicar classe CSS baseada na visibilidade
  useEffect(() => {
    if (panelsVisible) {
      document.body.classList.remove('panels-hidden');
    } else {
      document.body.classList.add('panels-hidden');
    }
  }, [panelsVisible]);

  const value = {
    panelsVisible,
    togglePanels,
    hidePanels,
    showPanels
  };

  return (
    <PanelVisibilityContext.Provider value={value}>
      {children}
    </PanelVisibilityContext.Provider>
  );
};

export default PanelVisibilityContext;
