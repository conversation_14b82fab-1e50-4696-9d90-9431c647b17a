/**
 * Estilos base para toda a aplicação
 */

/* Estilos para canvas e background */
.squares-canvas {
  position: fixed;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  z-index: 0;
  mix-blend-mode: screen;
}

.background-squares {
  position: fixed;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  z-index: 0;
}

.content {
  position: relative;
  z-index: 1;
}

/* Estilos para containers de conteúdo */
.content-container {
  position: relative;
  width: 100%;
  min-height: 100vh;
  z-index: 1;
  display: flex;
  flex-direction: column;
  align-items: center;
  padding: 0px;
}

.scroll-content {
  width: 100%;
  max-width: 800px; /* Voltar ao tamanho original */
  padding: 2rem 0;
  pointer-events: auto;
  margin: 0 auto; /* Centralizar o conteúdo */
}

/* Quando os painéis estão ocultos, permitir mais largura */
body.panels-hidden .scroll-content {
  max-width: 1000px; /* Aumentar apenas quando painéis ocultos */
}

.scroll-wrapper {
  overflow-y: visible;
  width: 100%;
  max-width: 800px;
  padding: 20px;
  margin-bottom: 50px;
  pointer-events: auto;
}

/* Estilos para áreas scrolláveis */
.scrollable-area, #scrollable-content {
  position: relative;
  z-index: 2;
  width: 100%;
  min-height: 100vh;
  overflow-y: auto;
  -webkit-overflow-scrolling: touch;
}

.interactive-background {
  position: fixed;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  z-index: 1;
  pointer-events: none;
}

.interactive-background canvas {
  pointer-events: auto;
}

/* Estilos específicos para inputs numéricos */
input[type="number"] {
  -moz-appearance: textfield;
  appearance: textfield;
}

input[type="number"]::-webkit-outer-spin-button,
input[type="number"]::-webkit-inner-spin-button {
  -webkit-appearance: none;
  margin: 0;
}

input[type="number"]:focus {
  outline: none;
}

/* Garantir que todos os inputs mantenham o efeito de brilho */
.calculator-block input:focus,
.calculo-container input:focus,
.conditions-panel input:focus,
.dilution-inputs input:focus {
  outline: none;
  border-color: var(--primary-color);
  box-shadow: 0 0 15px rgba(76, 175, 80, 0.5);
}

/* Estilos para botões */
button {
  margin: 0px 0px 0px 0;
  padding: 10px 15px;
  border: none;
  border-radius: 5px;
  background-color: var(--primary-color);
  color: var(--text-color);
  cursor: pointer;
  font-size: 16px;
  transition: all 0.3s ease;
  box-shadow: 0 2px 5px rgba(0, 0, 0, 0.2);
  pointer-events: auto;
}

button:hover {
  background-color: var(--primary-dark);
  transform: translateY(-2px);
  box-shadow: 0 4px 8px rgba(0, 0, 0, 0.3);
}

/* Estilos para textos */
p {
  line-height: 1.6;
  color: rgba(255, 255, 255, 0.9);
  pointer-events: auto;
}

.info-text {
  font-size: 0.9rem;
  font-style: italic;
  color: rgba(255, 255, 255, 0.7);
  margin-top: 10px;
  pointer-events: auto;
}

h2 {
  text-align: center;
  margin-bottom: 20px;
  color: var(--primary-color);
  width: 100%;
}

/* Estilos para scrollbar */
::-webkit-scrollbar {
  width: 8px;
}

::-webkit-scrollbar-track {
  background: transparent;
}

::-webkit-scrollbar-thumb {
  background: rgba(76, 175, 80, 0.3);
  border-radius: 4px;
}

::-webkit-scrollbar-thumb:hover {
  background: rgba(76, 175, 80, 0.5);
}

/* Remover seleção azul */
* {
  -webkit-tap-highlight-color: transparent;
  -webkit-touch-callout: none;
  -webkit-user-select: none;
  -khtml-user-select: none;
  -moz-user-select: none;
  -ms-user-select: none;
  user-select: none;
}

/* Permitir seleção apenas em elementos de input */
input, textarea {
  -webkit-user-select: text;
  -khtml-user-select: text;
  -moz-user-select: text;
  -ms-user-select: text;
  user-select: text;
}

/* Remover outline azul ao focar */
*:focus {
  outline: none;
}

/* Estilo personalizado para focus (acessibilidade) */
input:focus, textarea:focus, select:focus, button:focus {
  box-shadow: 0 0 0 2px var(--primary-color);
}
