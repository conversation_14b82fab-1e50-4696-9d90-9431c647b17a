/* =========================================================================
   IMPORTAÇÕES
   ========================================================================= */
@import './base.css';

/* =========================================================================
   VARIÁVEIS CSS GLOBAIS
   ========================================================================= */
:root {
  /* Cores principais */
  --primary-color: #4CAF50;                /* Verde principal */
  --primary-dark: #3e8e41;                 /* Verde escuro */
  --background-dark: #121212;              /* Fundo escuro */
  --panel-bg: rgba(34, 34, 34, 0.7);       /* Fundo dos painéis */
  --text-color: #ffffff;                   /* Cor do texto */

  /* Cores de destaque e bordas */
  --border-color: rgba(76, 175, 80, 0.5);  /* Verde transparente para bordas */
  --highlight-color: rgba(76, 175, 80, 0.1); /* Verde claro para destaques */

  /* Bordas arredondadas */
  --border-radius-sm: 4px;                 /* Raio pequeno */
  --border-radius-md: 8px;                 /* Raio médio */
  --border-radius-lg: 12px;                /* Raio grande */

  /* Tamanhos de fonte */
  --font-size-small: 12px;                 /* Fonte pequena */
  --font-size-normal: 14px;                /* Fonte normal */
  --font-size-medium: 16px;                /* Fonte média */
  --font-size-large: 18px;                 /* Fonte grande */

  /* Transições */
  --transition-speed: 0.3s;                /* Velocidade de transição */
  --transition-timing: ease;               /* Timing de transição */
}

/* =========================================================================
   ESTILOS BASE
   ========================================================================= */
body {
  margin: 0;                               /* Sem margem */
  padding: 0;                              /* Sem padding */
  font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', 'Roboto', 'Oxygen',
    'Ubuntu', 'Cantarell', 'Fira Sans', 'Droid Sans', 'Helvetica Neue',
    sans-serif;                            /* Fontes do sistema */
  -webkit-font-smoothing: antialiased;     /* Suavização de fontes */
  -moz-osx-font-smoothing: grayscale;      /* Suavização de fontes */
  background-color: var(--background-dark); /* Fundo escuro */
  color: var(--text-color);                /* Texto branco */
  min-height: 100vh;                       /* Altura mínima */
  overflow-x: hidden;                      /* Oculta overflow horizontal */
}

/* =========================================================================
   SELETOR DE CALCULADORAS
   ========================================================================= */
.calculator-selector {
  display: flex;                           /* Layout flexbox */
  flex-direction: column;                  /* Direção de coluna */
  align-items: center;                     /* Alinhamento centralizado */
  justify-content: center;                 /* Centralização vertical */
  min-height: 100vh;                       /* Altura mínima */
  padding: 20px;                           /* Espaçamento interno */
}

.calculator-selector-title {
  font-size: 32px;                         /* Tamanho da fonte */
  margin-bottom: 40px;                     /* Margem inferior */
  color: var(--primary-color);             /* Cor verde */
  text-align: center;                      /* Alinhamento centralizado */
}

/* =========================================================================
   GRID DE CALCULADORAS
   ========================================================================= */
.calculator-grid {
  display: grid;                           /* Layout grid */
  grid-template-columns: repeat(auto-fit, minmax(250px, 1fr)); /* Colunas responsivas */
  gap: 20px;                               /* Espaçamento entre itens */
  width: 100%;                             /* Largura total */
  max-width: 1000px;                       /* Largura máxima */
}

.calculator-card {
  background-color: var(--panel-bg);       /* Fundo semi-transparente */
  border: 2px solid var(--border-color);   /* Borda verde */
  border-radius: var(--border-radius-lg);  /* Bordas arredondadas */
  padding: 30px;                           /* Espaçamento interno */
  display: flex;                           /* Layout flexbox */
  flex-direction: column;                  /* Direção de coluna */
  align-items: center;                     /* Alinhamento centralizado */
  cursor: pointer;                         /* Cursor de ponteiro */
  transition: all 0.3s ease;               /* Transição suave */
  text-align: center;                      /* Alinhamento de texto */
}

.calculator-card:hover {
  transform: translateY(-5px);             /* Efeito de elevação */
  border-color: var(--primary-color);      /* Borda verde mais forte */
  box-shadow: 0 10px 20px rgba(0, 0, 0, 0.2); /* Sombra */
}

.calculator-icon {
  font-size: 48px;                         /* Tamanho do ícone */
  margin-bottom: 20px;                     /* Margem inferior */
}

.calculator-name {
  font-size: 24px;                         /* Tamanho da fonte */
  margin-bottom: 10px;                     /* Margem inferior */
}

.calculator-description {
  font-size: 14px;                         /* Tamanho da fonte */
  opacity: 0.8;                            /* Semi-transparente */
}

/* =========================================================================
   RESPONSIVIDADE
   ========================================================================= */
@media (max-width: 768px) {
  .calculator-grid {
    grid-template-columns: 1fr;            /* Uma coluna em telas pequenas */
  }

  .calculator-selector-title {
    font-size: 24px;                       /* Fonte menor em telas pequenas */
  }
}
