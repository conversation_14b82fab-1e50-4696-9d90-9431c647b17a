const fs = require('fs');
const path = require('path');

// Caminho para o arquivo DebugInfoPanel.js
const filePath = path.join(__dirname, 'src', 'components', 'debug', 'DebugInfoPanel.js');

// Ler o conteúdo do arquivo
let content = fs.readFileSync(filePath, 'utf8');

// Expressão regular para encontrar todas as checkboxes
const regex = /<input\s+type="checkbox"\s+id="([^"]+)"\s+checked=\{config\.([^}]+)\}\s+onChange=\{\(\) => toggleConfig\('([^']+)'\)\}\s*\/>\s*<label htmlFor="([^"]+)">([^<]+)<\/label>/g;

// Substituir todas as ocorrências
content = content.replace(regex, (match, id1, config, toggle, id2, label) => {
  return `<label htmlFor="${id1}">
                  <input
                    type="checkbox"
                    id="${id1}"
                    checked={config.${config}}
                    onChange={() => toggleConfig('${toggle}')}
                  />
                  <span>${label}</span>
                </label>`;
});

// Escrever o conteúdo de volta para o arquivo
fs.writeFileSync(filePath, content, 'utf8');

console.log('Checkboxes atualizadas com sucesso!');
