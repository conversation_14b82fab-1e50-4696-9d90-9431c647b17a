#!/bin/bash

# Script para alternar entre diferentes versões da aplicação

# Verificar se o parâmetro foi fornecido
if [ $# -ne 1 ]; then
  echo "Uso: $0 [original|tabswitcher|demo]"
  exit 1
fi

# Diretório base
BASE_DIR="src"

# Backup do App.js atual
cp $BASE_DIR/App.js $BASE_DIR/App_current_backup.js

# Alternar para a versão especificada
case "$1" in
  original)
    echo "Alternando para a versão original da aplicação..."
    cp $BASE_DIR/App_original_fixed.js $BASE_DIR/App.js
    ;;
  tabswitcher)
    echo "Alternando para a versão com TabSwitcher..."
    cp $BASE_DIR/App_with_tabswitcher_full.js $BASE_DIR/App.js
    ;;
  demo)
    echo "Alternando para a versão de demonstração..."
    cp $BASE_DIR/App_demo.js $BASE_DIR/App.js
    ;;
  *)
    echo "Versão desconhecida: $1"
    echo "Uso: $0 [original|tabswitcher|demo]"
    # Restaurar o backup
    cp $BASE_DIR/App_current_backup.js $BASE_DIR/App.js
    exit 1
    ;;
esac

echo "Versão alternada com sucesso!"
echo "Reinicie o servidor para aplicar as alterações."
